name: 'Driver '
description: 'APIs for Driver Management'
endpoints:
  -
    httpMethods:
      - POST
    uri: api/driver/availability
    metadata:
      groupName: 'Driver '
      groupDescription: 'APIs for Driver Management'
      subgroup: 'Driver Endpoints'
      subgroupDescription: 'Endpoints for handling register Driver, Update Driver Availability.'
      title: 'Update Driver Availability Status'
      description: 'This endpoint Update Driver Availability Status.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      is_available:
        name: is_available
        description: ''
        required: true
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      current_latitude:
        name: current_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      current_longitude:
        name: current_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      is_available: false
      current_latitude: -90
      current_longitude: -179
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Availability updated successfully.","data":null,"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/driver/rides/{ride_id}/apply'
    metadata:
      groupName: 'Driver '
      groupDescription: 'APIs for Driver Management'
      subgroup: 'Driver Endpoints'
      subgroupDescription: 'Endpoints for handling register Driver, Update Driver Availability.'
      title: 'Apply to a Ride'
      description: 'This endpoint allows drivers to apply for an available ride and changes status to in progress.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      ride_id:
        name: ride_id
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      ride:
        name: ride
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      ride_id: 1
      ride: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Ride application successful","data":{"id":1,"pricing_id":1,"estimated_distance":4.33,"estimated_duration":33,"actual_distance":0,"actual_duration":104,"fare":22.17,"final_price":22.17,"status":"accepted","type":"immediate","requested_at":"2025-02-21 18:14:55","completed_at":null,"created_at":"2025-02-21"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/driver/rides/{ride_id}/start'
    metadata:
      groupName: 'Driver '
      groupDescription: 'APIs for Driver Management'
      subgroup: 'Driver Endpoints'
      subgroupDescription: 'Endpoints for handling register Driver, Update Driver Availability.'
      title: 'Start a Ride'
      description: 'This endpoint allows drivers to start an accepted ride and changes status to in progress.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      ride_id:
        name: ride_id
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      ride:
        name: ride
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      ride_id: 1
      ride: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Ride started successfully","data":{"id":1,"pricing_id":1,"estimated_distance":32.17,"estimated_duration":104,"actual_distance":0,"actual_duration":33,"fare":95.02,"final_price":95.02,"status":"in_progress","type":"immediate","requested_at":"2025-02-21 18:14:55","completed_at":null,"created_at":"2025-02-21"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/driver/rides/{ride_id}/complete'
    metadata:
      groupName: 'Driver '
      groupDescription: 'APIs for Driver Management'
      subgroup: 'Driver Endpoints'
      subgroupDescription: 'Endpoints for handling register Driver, Update Driver Availability.'
      title: 'Complete a Ride'
      description: 'This endpoint allows drivers to complete an in-progress ride and changes status to completed.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      ride_id:
        name: ride_id
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      ride:
        name: ride
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      ride_id: 1
      ride: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_latitude:
        name: current_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      current_longitude:
        name: current_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_latitude: -89
      current_longitude: -179
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Ride completed successfully","data":{"id":1,"pricing_id":1,"estimated_distance":6.56,"estimated_duration":13,"actual_distance":0,"actual_duration":44,"fare":63.01,"final_price":63.01,"status":"completed","type":"immediate","requested_at":"2025-02-21 18:14:55","completed_at":"2025-02-21T15:14:55.000000Z","created_at":"2025-02-21"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/driver/rides/{ride_id}/cancel'
    metadata:
      groupName: 'Driver '
      groupDescription: 'APIs for Driver Management'
      subgroup: 'Driver Endpoints'
      subgroupDescription: 'Endpoints for handling register Driver, Update Driver Availability.'
      title: 'Cancel a Ride'
      description: 'This endpoint allows drivers to cancel a pending or accepted ride and changes status to canceled.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      ride_id:
        name: ride_id
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      ride:
        name: ride
        description: 'The ID of the ride.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      ride_id: 1
      ride: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      cancel_reason:
        name: cancel_reason
        description: 'The reason for canceling the ride.'
        required: true
        example: 'Driver unavailable'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      cancel_reason: 'Driver unavailable'
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Ride canceled successfully","data":{"id":1,"pricing_id":1,"estimated_distance":38.81,"estimated_duration":24,"actual_distance":0,"actual_duration":5,"fare":33.81,"final_price":33.81,"status":"canceled","type":"immediate","requested_at":"2025-02-21 18:14:55","completed_at":null,"created_at":"2025-02-21"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
