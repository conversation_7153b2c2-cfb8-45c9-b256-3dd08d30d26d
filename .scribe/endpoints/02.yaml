name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/regions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"success","data":[{"id":1,"name":"aliquam"},{"id":2,"name":"voluptatibus"},{"id":3,"name":"tempora"},{"id":4,"name":"excepturi"},{"id":5,"name":"ut"},{"id":6,"name":"libero"},{"id":7,"name":"voluptatem"},{"id":8,"name":"amet"},{"id":9,"name":"mollitia"},{"id":10,"name":"voluptas"}],"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/localhost\/api\/regions?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"http:\/\/localhost\/api\/regions","per_page":15,"to":10,"total":10},"links":{"first":"http:\/\/localhost\/api\/regions?page=1","last":"http:\/\/localhost\/api\/regions?page=1","prev":null,"next":null}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '59'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/car-types
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"success","data":[{"id":1,"km_range":"10.00","name":"Classic","descrption":null},{"id":2,"km_range":"15.00","name":"Comfort","descrption":null},{"id":3,"km_range":"20.00","name":"VIP","descrption":null},{"id":4,"km_range":"5.00","name":"Motor","descrption":null},{"id":5,"km_range":"10.00","name":"Normal Taxi","descrption":null}],"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/localhost\/api\/car-types?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"http:\/\/localhost\/api\/car-types","per_page":15,"to":5,"total":5},"links":{"first":"http:\/\/localhost\/api\/car-types?page=1","last":"http:\/\/localhost\/api\/car-types?page=1","prev":null,"next":null}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '58'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/shifts
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"success","data":[{"id":1,"name":"Morning Shift","notes":null,"start_time":"06:00 AM","end_time":"12:00 PM"},{"id":2,"name":"Afternoon Shift","notes":null,"start_time":"12:00 PM","end_time":"06:00 PM"},{"id":3,"name":"Evening Shift","notes":null,"start_time":"06:00 PM","end_time":"12:00 AM"},{"id":4,"name":"Night Shift","notes":null,"start_time":"10:00 PM","end_time":"06:00 AM"}],"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/localhost\/api\/shifts?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"http:\/\/localhost\/api\/shifts","per_page":15,"to":4,"total":4},"links":{"first":"http:\/\/localhost\/api\/shifts?page=1","last":"http:\/\/localhost\/api\/shifts?page=1","prev":null,"next":null}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '57'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/shifts/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the shift.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"success","data":{"id":1,"name":"Morning Shift","notes":null,"start_time":"06:00 AM","end_time":"12:00 PM"},"meta":null,"links":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '56'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/pricings
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '55'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/media
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/pricings/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pricing.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"success","data":{"id":1,"shift_id":1,"car_type_id":1,"minimum_fare":14000,"km_price":2500,"miu_price":500},"meta":null,"links":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '54'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/notifications
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
