name: 'Ride '
description: 'APIs for Ride Management'
endpoints:
  -
    httpMethods:
      - POST
    uri: api/rides/estimate
    metadata:
      groupName: 'Ride '
      groupDescription: 'APIs for Ride Management'
      subgroup: 'Ride Endpoints'
      subgroupDescription: 'Endpoints for handling request a ride, confirm a ride request.'
      title: 'Estimate Pricing for a ride'
      description: 'This endpoint estimate Pricing for a ride.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      points:
        name: points
        description: 'Must have at least 2 items.'
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: immediate
        type: string
        enumValues:
          - immediate
          - scheduled
        exampleWasSpecified: false
        nullable: false
        custom: []
      coupon_code:
        name: coupon_code
        description: 'The <code>code</code> of an existing record in the coupons table.'
        required: false
        example: earum
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      note:
        name: note
        description: 'Must not be greater than 500 characters.'
        required: false
        example: tthinslr
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'points[].latitude':
        name: 'points[].latitude'
        description: 'Must be between -90 and 90.'
        required: true
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'points[].longitude':
        name: 'points[].longitude'
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'points[].address':
        name: 'points[].address'
        description: 'Must not be greater than 255 characters.'
        required: true
        example: qttnotp
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      points:
        -
          latitude: -90
          longitude: -179
          address: qttnotp
      type: immediate
      coupon_code: earum
      note: tthinslr
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Ride estimated successfully","data":{"pricing_details":[{"pricing_id":11,"id":1,"car_type":"Classic","estimated_distance":10.5,"estimated_duration":25,"price":"52,500","details":{"minimum_fare":"17,000","flag_down_fee":"6,000","km_price":"3,000","miu_price":"600"}},{"pricing_id":12,"id":2,"car_type":"Comfort","estimated_distance":10.5,"estimated_duration":25,"price":"62,250","details":{"minimum_fare":"20,000","flag_down_fee":"8,000","km_price":"3,500","miu_price":"700"}},{"pricing_id":13,"id":3,"car_type":"VIP","estimated_distance":10.5,"estimated_duration":25,"price":"62,250","details":{"minimum_fare":"20,000","flag_down_fee":"8,000","km_price":"3,500","miu_price":"700"}},{"pricing_id":14,"id":4,"car_type":"Motor","estimated_distance":10.5,"estimated_duration":25,"price":"62,250","details":{"minimum_fare":"20,000","flag_down_fee":"8,000","km_price":"3,500","miu_price":"700"}},{"pricing_id":15,"id":5,"car_type":"Normal Taxi","estimated_distance":10.5,"estimated_duration":25,"price":"62,250","details":{"minimum_fare":"20,000","flag_down_fee":"8,000","km_price":"3,500","miu_price":"700"}}],"points":[{"latitude":33.510414,"longitude":36.278336,"address":"Pickup location","type":"pickup","sort_order":1},{"latitude":33.513456,"longitude":36.276543,"address":"Dropoff location","type":"dropoff","sort_order":2}]},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/rides/confirm
    metadata:
      groupName: 'Ride '
      groupDescription: 'APIs for Ride Management'
      subgroup: 'Ride Endpoints'
      subgroupDescription: 'Endpoints for handling request a ride, confirm a ride request.'
      title: 'Confirm a Ride'
      description: "This endpoint let's user to confirm a ride request."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      points:
        name: points
        description: 'Must have at least 2 items.'
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: scheduled
        type: string
        enumValues:
          - immediate
          - scheduled
        exampleWasSpecified: false
        nullable: false
        custom: []
      coupon_code:
        name: coupon_code
        description: ''
        required: false
        example: qui
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      pricing_id:
        name: pricing_id
        description: 'The <code>id</code> of an existing record in the pricings table.'
        required: true
        example: 38189.748679468
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      note:
        name: note
        description: ''
        required: false
        example: dicta
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'points[].latitude':
        name: 'points[].latitude'
        description: ''
        required: true
        example: 37343193.20347
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'points[].longitude':
        name: 'points[].longitude'
        description: ''
        required: true
        example: 367.6387884
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'points[].address':
        name: 'points[].address'
        description: ''
        required: true
        example: necessitatibus
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      points:
        -
          latitude: 37343193.20347
          longitude: 367.6387884
          address: necessitatibus
      type: scheduled
      coupon_code: qui
      pricing_id: 38189.748679468
      note: dicta
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
