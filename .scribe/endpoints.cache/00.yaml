## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: Auth
description: 'APIs for Authentication Management'
endpoints:
  -
    httpMethods:
      - POST
    uri: api/auth/request-otp
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: 'Request OTP'
      description: 'This endpoint sends an OTP code to the specified phone number.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      phone:
        name: phone
        description: 'Must match the regex /^963\d{9}$/.'
        required: true
        example: '************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      phone: '************'
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Code sent successfully","data":null,"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/verify-otp
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: 'Verify OTP'
      description: "This endpoint verifies the OTP code sent to the user's phone."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      phone:
        name: phone
        description: 'The phone number to verify.'
        required: true
        example: '+************.'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      code:
        name: code
        description: 'The OTP code sent to the phone. Test Code: 12345.'
        required: true
        example: excepturi
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fcm_token:
        name: fcm_token
        description: ''
        required: false
        example: aut
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      phone: '+************.'
      code: excepturi
      fcm_token: aut
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Login successful.","data":{"id":1,"user_id":1,"points":75,"refer_code":"NXKFoD","user":{"id":1,"first_name":"est","last_name":"asperiores","email":"<EMAIL>","phone":"************","phone_verified_at":"1987-03-13 06:42:43","user_type":"Customer","is_active":1,"birthdate":"1988-09-30","gender":"male","profile_picture":""},"access_token":"1|sbspQlAjKmcvOPJ6101oG21CG1u8NxXU8r6yJY6oe95aedf9"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/complete-profile
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: 'Complete Profile'
      description: "This endpoint completes the user's profile by updating the necessary details."
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: 'Must not be greater than 25 characters.'
        required: true
        example: a
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Must not be greater than 25 characters.'
        required: true
        example: ojcalxhz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Must be a valid email address.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      phone:
        name: phone
        description: 'Must match the regex /^963\d{9}$/. Must not be greater than 12 characters.'
        required: true
        example: ri
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      birthdate:
        name: birthdate
        description: 'Must be a valid date.'
        required: true
        example: '2025-02-21T18:15:27'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      gender:
        name: gender
        description: ''
        required: true
        example: male
        type: string
        enumValues:
          - male
          - female
        exampleWasSpecified: false
        nullable: false
        custom: []
      profile_picture:
        name: profile_picture
        description: 'Must be an image. Must not be greater than 2048 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      first_name: a
      last_name: ojcalxhz
      email: <EMAIL>
      phone: ri
      birthdate: '2025-02-21T18:15:27'
      gender: male
    fileParameters:
      profile_picture: null
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Profile updated successfully","data":{"id":1,"user_id":1,"points":0,"refer_code":"NV6b61","user":{"id":1,"first_name":"John","last_name":"Doe","email":null,"phone":"************","phone_verified_at":null,"user_type":"Customer","is_active":1,"birthdate":"1990-01-01","gender":"male","profile_picture":""},"access_token":"1|jTPLJ3ywrq3TRQPFZWlWNfvxqZT08wllqpQRtIur94fdb8ee"},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/auth/profile
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: 'Get Profile'
      description: "This endpoint retrieves the authenticated user's profile."
      authenticated: false
      custom: []
    headers:
      Authorization: 'Bearer token required The access token of the authenticated user.'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Profile fetched successfully","data":{"id":1,"user_id":1,"points":71,"refer_code":"KcUK2I","user":{"id":1,"first_name":"est","last_name":"veniam","email":"<EMAIL>","phone":"************","phone_verified_at":"2000-08-08 11:48:04","user_type":"Customer","is_active":1,"birthdate":"2001-01-21","gender":"male","profile_picture":""}},"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/logout
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: Logout
      description: 'This endpoint logs out the authenticated user by revoking the current access token.'
      authenticated: false
      custom: []
    headers:
      Authorization: 'Bearer token required The access token of the authenticated user.'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Logged out successfully","data":null,"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/delete-account
    metadata:
      groupName: Auth
      groupDescription: 'APIs for Authentication Management'
      subgroup: 'Auth Endpoints'
      subgroupDescription: 'Endpoints for handling OTP, user profile, logout, and account deletion.'
      title: 'Delete User Account'
      description: "This endpoint deletes the authenticated user's account along with related data."
      authenticated: false
      custom: []
    headers:
      Authorization: 'Bearer token required The access token of the authenticated user.'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"User deleted successfully","data":null,"meta":null,"links":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
