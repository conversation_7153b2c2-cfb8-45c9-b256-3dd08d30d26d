# Pusher Authentication for Private Channels

This document provides instructions for authenticating with <PERSON><PERSON><PERSON> to subscribe to private channels for driver location tracking.

## Authentication Endpoint

To authenticate with <PERSON><PERSON><PERSON> for private channels, you need to make a POST request to the following endpoint:

```
POST /api/broadcasting/auth
```

### Authentication Requirements

- The request must include a valid Bear<PERSON> token in the Authorization header
- The request must include the `socket_id` and `channel_name` parameters

### Request Headers

```
Authorization: Bearer YOUR_API_TOKEN
Content-Type: application/json
Accept: application/json
```

### Request Body

```json
{
  "socket_id": "SOCKET_ID_FROM_PUSHER",
  "channel_name": "private-driver.123"
}
```

### Response

If authentication is successful, you will receive a response with the authentication signature:

```json
{
  "auth": "YOUR_APP_KEY:GENERATED_SIGNATURE"
}
```

## Channel Naming Conventions

### Driver Location Channels

Private channels for driver locations follow this naming convention:

```
private-driver.{driver_id}
```

Example: `private-driver.123`

### Ride Location Channels

Private channels for ride locations follow this naming convention:

```
private-ride.{ride_id}
```

Example: `private-ride.456`

## Implementation Examples

### React Native with Pusher JS

```javascript
import Pusher from 'pusher-js/react-native';

// Initialize Pusher
const pusher = new Pusher('YOUR_APP_KEY', {
  cluster: 'YOUR_APP_CLUSTER',
  authEndpoint: 'https://your-api-url.com/api/broadcasting/auth',
  auth: {
    headers: {
      Authorization: `Bearer ${userToken}`,
      Accept: 'application/json'
    }
  }
});

// Subscribe to a private channel
const channel = pusher.subscribe(`private-driver.${driverId}`);

// Listen for location updates
channel.bind('location.updated', (data) => {
  console.log('Driver location updated:', data);
  // Update the map with the new location
  updateDriverMarker(data);
});
```

### Flutter with Pusher Client

```dart
import 'package:pusher_client/pusher_client.dart';

// Initialize Pusher
PusherOptions options = PusherOptions(
  cluster: 'YOUR_APP_CLUSTER',
  auth: PusherAuth(
    'https://your-api-url.com/api/broadcasting/auth',
    headers: {
      'Authorization': 'Bearer $userToken',
      'Accept': 'application/json'
    }
  )
);

PusherClient pusher = PusherClient(
  'YOUR_APP_KEY',
  options,
  autoConnect: true,
  enableLogging: true
);

// Subscribe to a private channel
Channel channel = pusher.subscribe('private-driver.$driverId');

// Listen for location updates
channel.bind('location.updated', (event) {
  print('Driver location updated: ${event.data}');
  // Update the map with the new location
  updateDriverMarker(jsonDecode(event.data));
});
```

## Security Considerations

- Never hardcode your Pusher secret in your mobile app
- Always use private channels for sensitive data like driver locations
- Ensure your API tokens have appropriate expiration times
- Implement proper authorization checks on the server side

## Troubleshooting

If you encounter authentication issues:

1. Ensure your API token is valid and not expired
2. Check that you're using the correct channel name format (including the 'private-' prefix)
3. Verify that the user has permission to access the requested channel
4. Check the network request in your app's debugging tools to see the exact error response

For any further assistance, please contact the backend team.
