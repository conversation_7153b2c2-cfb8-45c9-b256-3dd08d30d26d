<span class="dropdown">
    <button type="button" class="btn btn-sm dropdown-toggle hide-arrow py-0" data-bs-toggle="dropdown">
        <i class="fas fa-ellipsis-v"></i>
    </button>
    <div class="dropdown-menu dropdown-menu-end">
        @isset($options['show'])
            @php
                $routeParts = explode('.', $route);
                $resourceName = end($routeParts);
                $showPermissionName = 'list-' . Str::singular($resourceName);
                $showPermissionNamePlural = 'list-' . $resourceName;

                // Check if user has either singular or plural permission
                $hasPermission = auth()->user()->can($showPermissionName) || auth()->user()->can($showPermissionNamePlural);

            @endphp

            @if($options['show'] || $hasPermission)
            <a class="dropdown-item " href="{{ url(str_replace('.', '/', $route) . '/' . $id) }}">
                <i class="font-small-4 me-1 fa fa-eye"></i> {{ __('Show') }}
            </a>
            @endif
        @endisset
        @isset($options['edit']['status'])
            @php
                $routeParts = explode('.', $route);
                $resourceName = end($routeParts);
                $permissionName = 'update-' . Str::singular($resourceName);
                $permissionNamePlural = 'update-' . $resourceName;

                // Check if user has either singular or plural permission
                $hasPermission = auth()->user()->can($permissionName) || auth()->user()->can($permissionNamePlural);


            @endphp

            @if($options['edit']['status'] || $hasPermission)
            <a class="dropdown-item " href="{{ url(str_replace('.', '/', $route) . '/' . $id . '/edit') }}">
                <i class="font-small-4 me-1 fa fa-edit"></i> {{ __('Edit') }}
            </a>
            @endif
        @endisset
        @isset($options['delete'])
            @php
                $routeParts = explode('.', $route);
                $resourceName = end($routeParts);
                $deletePermissionName = 'delete-' . Str::singular($resourceName);
                $deletePermissionNamePlural = 'delete-' . $resourceName;

                // Check if user has either singular or plural permission
                $hasPermission = auth()->user()->can($deletePermissionName) || auth()->user()->can($deletePermissionNamePlural);


            @endphp

            @if($options['delete'] || $hasPermission)
            <a class="dropdown-item delete-entity-btn" data-id="{{ $id }}" href="#"
                onclick='deleteEntity("{{ $model }}", "{{ $options['action_type'] ?? \App\Enums\DeleteActionEnum::FORCE_DELETE()->value }}", {{ $id }}, {{ $options['with_trashed'] }})'
                data-with-trashed="{{ $options['with_trashed'] }}">
                <i class="font-small-4 me-1 fa fa-trash"></i> {{ __('Delete') }}
            </a>
            @endif
        @endisset
    </div>
</span>

<script>
    function deleteEntity(model, actionType, id, withTrashed) {
        var url = '/dashboard/delete-object?objectId=' + id +
                  '&objectType=' + encodeURIComponent(model) +
                  '&actionType=' + encodeURIComponent(actionType) +
                  '&withTrashed=' + (withTrashed || 1);
        Swal.fire({
            title: "{{ __('Are You Sure?') }}",
            icon: 'warning',
            html: "{{ __('This will happen permanently and will affect dependent elements') }}",
            showCancelButton: true,
            showDenyButton: false,
            confirmButtonText: '<i class="fas fa-trash me-1"></i>{{ __('Delete') }}',
            cancelButtonText: '<i class="fas fa-times me-1"></i>{{ __('Cancel') }}',
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            buttonsStyling: true,
            customClass: {
                popup: 'swal2-popup-custom',
                title: 'swal2-title-custom',
                htmlContainer: 'swal2-html-custom',
                confirmButton: 'btn btn-danger px-4 py-2 me-2',
                cancelButton: 'btn btn-secondary px-4 py-2'
            },
            backdrop: 'rgba(0,0,0,0.4)',
            allowOutsideClick: false,
            allowEscapeKey: true,
            focusConfirm: false,
            focusCancel: true
        }).then(function(result) {
            if (result.isConfirmed) {
                window.deleteObject(url).then(function(data) {
                    console.log(data.success);
                    if (data.success) {
                        if ($('.table').DataTable().ajax.json())
                            $('.table').DataTable().ajax.reload();
                        else
                            window.location.reload();
                        Swal.fire({
                            title: "{{ __('Success') }}",
                            text: data['message'],
                            icon: 'success',
                            timer: 3000
                        });
                    } else {
                        Swal.fire({
                            title: "{{ __('Error') }}",
                            icon: 'error',
                            timer: 3000
                        });
                    }
                })["catch"](function(err) {
                    console.log(err);
                });
            }
        });

        console.log("Deleting with ID:  {{ $id }}");
    }
</script>
