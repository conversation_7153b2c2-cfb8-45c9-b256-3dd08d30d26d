@extends('layouts/layoutMaster')


@section('title', __('Edit Setting'))

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/leaflet/leaflet.scss'])
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />

@endsection
@section('page-style')
@endsection
@section('styles')
@endsection

@section('content')
    <div class="content-wrapper">
        <div class="content-header row">
            <div class="content-header-left col-md-9 col-12 mb-2">
                <div class="row breadcrumbs-top">
                    <div class="col-12">
                        <h2 class="content-header-title float-left mb-0">{{ __('Edit Setting') }}</h2>
                        <div class="breadcrumb-wrapper">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a
                                        href="{{ route('dashboard.main') }}">{{ __('Dashboard') }}</a>
                                </li>
                                @if (isset($menu))
                                    <li class="breadcrumb-item"><a href="{{ route($menu_link) }}">{{ $menu }}</a>
                                    </li>
                                @endif
                                <li class="breadcrumb-item active">
                                    {{ __('Settings') }}
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="content-body">
            <x-any-errors></x-any-errors>
            <section class="form-control-repeater">
                <div class="row">

                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <form action="{{ route('dashboard.settings.update') }}" method="post"
                                    enctype="multipart/form-data">
                                    @csrf
                                    <div class="card mb-4">
                                        <h5 class="card-header">{{ __('Settings') }}</h5>
                                        <div class="card-body">
                                            @foreach ($settings as $setting)
                                                @if (!in_array($setting->key->value, $excptedKeys))
                                                    <div class="m-4">
                                                        <fieldset>
                                                            <div>
                                                                <label for="value_{{ $setting->id }}"
                                                                    class="form-label">{{ $setting->key->label($setting->key->value) }}</label>
                                                                @if ($setting->key->getType($setting->key->value) === 'textarea')
                                                                    <textarea required id="value_{{ $setting->id }}" name="settings[{{ $setting->id }}][value]" class="form-control"
                                                                        rows="5">{!! $setting->value !!}</textarea>
                                                                @elseif ($setting->key->getType($setting->key->value) === 'checkbox')
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input"
                                                                               type="checkbox"
                                                                               id="value_{{ $setting->id }}"
                                                                               name="settings[{{ $setting->id }}][value]"
                                                                               value="1"
                                                                               {{ $setting->value == '1' ? 'checked' : '' }}>
                                                                        <input type="hidden" name="settings[{{ $setting->id }}][value]" value="0">
                                                                    </div>
                                                                @else
                                                                    <input required
                                                                        type="{{ $setting->key->getType($setting->key->value) ?? 'text' }}"
                                                                        id="value_{{ $setting->id }}"
                                                                        name="settings[{{ $setting->id }}][value]"
                                                                        class="form-control"
                                                                        placeholder="{{ __($setting->key->value) }}"
                                                                        value="{{ $setting?->value }}" />
                                                                @endif
                                                                {{-- <input required
                                                                    type="{{ $setting->key->getType($setting->key->value) ?? 'text' }}"
                                                                    id="value_{{ $setting->id }}"
                                                                    name="settings[{{ $setting->id }}][value]"
                                                                    class="form-control"
                                                                    placeholder="{{ __($setting->key->value) }}"
                                                                    value="{{ $setting?->value }}" /> --}}
                                                                @if ($setting->has_image)
                                                                    <div class="col-6">
                                                                        <label for="image"
                                                                            class="form-label">{{ __('Image') }}</label>
                                                                        <input type="file" id="image"
                                                                            name="settings[{{ $setting->id }}][image]"
                                                                            class="form-control"
                                                                            placeholder="{{ __('Image') }}" />
                                                                        <img class="img-fluid rounded mb-3 pt-1 mt-4"
                                                                            src="{{ $setting->getFirstMediaUrl('image') }}"
                                                                            height="200" width="300" alt="User avatar">
                                                                    </div>
                                                                @endif
                                                                <input hidden required type="text"
                                                                    name="settings[{{ $setting->id }}][key]"
                                                                    class="form-control" value="{{ $setting?->key }}" />
                                                            </div>
                                                        </fieldset>
                                                    </div>
                                                @endif
                                            @endforeach




                                            <hr />
                                            <div class="col-6">
                                                <x-forms.submit-button />
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                </div>
            </section>
        </div>
    </div>
@endsection

@section('vendor-script')
@endsection

@section('page-script')

    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>
    <script>
        const dragMapVar = document.getElementById('dragMap');
        if (dragMapVar) {
            const map = L.map('dragMap').setView([52.0207975, 4.4937836], 12);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);

            var searchControl = L.Control.geocoder({
                defaultMarkGeocode: false
            }).on('markgeocode', function(e) {
                var latlng = e.geocode.center;
                map.flyTo(latlng, 12);
                // You can do additional actions here, such as adding a marker
            }).addTo(map);

            // Check if latitude and longitude input fields have values
            var latitude = $('#latitude').val();
            var longitude = $('#longitude').val();
            console.log('latitude');
            console.log(latitude);
            console.log(longitude);
            if (latitude && longitude) {
                console.log('yes');
                // Set initial marker location from input fields
                var initialLocation = [parseFloat(latitude), parseFloat(longitude)];
                map.setView(initialLocation, 12);

                var marker = L.marker(initialLocation, {
                    draggable: true
                }).addTo(map);

                marker.on('dragend', function(event) {
                    var markerLocation = marker.getLatLng();
                    console.log(markerLocation.lat, markerLocation.lng);
                    // Update hidden input fields in the form with these values
                    $('#latitude').val(markerLocation.lat);
                    $('#longitude').val(markerLocation.lng);
                });

                map.on('click', function(event) {
                    marker.setLatLng(event.latlng);
                });

            } else {
                // Try to get the user's location
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        // Success, center the map on the user's location
                        var userLocation = [position.coords.latitude, position.coords.longitude];
                        $('#latitude').val(position.coords.latitude);
                        $('#longitude').val(position.coords.longitude);
                        map.setView(userLocation, 12);

                        // Add a marker at the user's location
                        var marker = L.marker(userLocation, {
                            draggable: true
                        }).addTo(map);

                        marker.on('dragend', function(event) {
                            var markerLocation = marker.getLatLng();
                            console.log(markerLocation.lat, markerLocation.lng);
                            // Update hidden input fields in the form with these values
                            $('#latitude').val(markerLocation.lat);
                            $('#longitude').val(markerLocation.lng);
                        });

                        map.on('click', function(event) {
                            marker.setLatLng(event.latlng);
                        });
                    },
                    function(error) {
                        // Error handling, you can set a default location in case of an error
                        console.error('Error getting user location:', error);
                        var defaultLocation = [33.5138, 36.2765];
                        map.setView(defaultLocation, 12);
                    }
                );
            }
        }
    </script>
    <script src="https://cdn.ckeditor.com/4.16.2/standard/ckeditor.js"></script>
    <script>
        CKEDITOR.replace('editor', {
            toolbar: [{
                    name: 'basicstyles',
                    items: ['Bold', 'Italic', 'Underline']
                },
                {
                    name: 'paragraph',
                    items: ['NumberedList', 'BulletedList']
                },
                {
                    name: 'links',
                    items: ['Link', 'Unlink']
                },
                {
                    name: 'document',
                    items: ['Source']
                }
            ]
        });
    </script>
@endsection
