@vite(['resources/assets/vendor/fonts/tabler-icons.scss', 'resources/assets/vendor/fonts/fontawesome.scss', 'resources/assets/vendor/fonts/flag-icons.scss'])
<!-- Core CSS -->
@vite(['resources/assets/vendor/scss' . $configData['rtlSupport'] . '/core' . ($configData['style'] !== 'light' ? '-' . $configData['style'] : '') . '.scss', 'resources/assets/vendor/scss' . $configData['rtlSupport'] . '/' . $configData['theme'] . ($configData['style'] !== 'light' ? '-' . $configData['style'] : '') . '.scss', 'resources/assets/css/demo.css'])

@vite(['resources/assets/vendor/libs/node-waves/node-waves.scss', 'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss', 'resources/assets/vendor/libs/typeahead-js/typeahead.scss'])

<!-- Vendor Styles -->
@yield('vendor-style')


<link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

<!-- Page Styles -->
@yield('page-style')
<style>
    table.dataTable thead tr {
        background-color: rgb(236 236 236);
    }

    .swal2-popup-custom {
        border-radius: 15px !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
        border: none !important;
        padding: 2rem !important;
    }

    .swal2-title-custom {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin-bottom: 1rem !important;
    }

    .swal2-html-custom {
        font-size: 1rem !important;
        color: #6c757d !important;
        margin-bottom: 2rem !important;
        line-height: 1.5 !important;
    }

    .swal2-actions {
        gap: 0.75rem !important;
        margin-top: 1.5rem !important;
    }

    .swal2-confirm.btn-danger {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        color: white !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    .swal2-confirm.btn-danger:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
    }

    .swal2-cancel.btn-secondary {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        color: white !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    .swal2-cancel.btn-secondary:hover {
        background-color: #5a6268 !important;
        border-color: #545b62 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important;
    }

    .swal2-icon.swal2-warning {
        border-color: #ffc107 !important;
        color: #ffc107 !important;
        font-size: 3rem !important;
    }
</style>
