<?php

/**
 * Configuration file for MTN SMS Service.
 * This file contains the necessary credentials and URL for the MTN SMS API.
 * Update these values in the `.env` file to ensure security and flexibility.
 */

return [
    // The base URL of the MTN SMS API.
    'url' => env('MTN_URL', 'https://services.mtnsyr.com:7443/general/MTNSERVICES/ConcatenatedSender.aspx'),
    
    // The username provided by MTN for API authentication.
    'user' => env('MTN_USER', 'ssdwq522'),

    // The password provided by MTN for API authentication.
    'pass' => env('MTN_PASS', 'ssrt151212'),

    // The sender ID that will appear in the recipient's SMS.
    'from' => env('MTN_FROM', 'Alia'),
];
