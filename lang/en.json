{"Home": "Home", "Page 2": "Page 2", "Error": "Error", "Login": "<PERSON><PERSON>", "Register": "Register", "Car type created successfully": "Car type created successfully", "Car type updated successfully": "Car type updated successfully", "Car type deleted successfully": "Car type deleted successfully", "Save": "Save", "Cancel": "Cancel", "Kilometer Range": "Kilometer Range", "Name (English)": "Name (English)", "Name (Arabic)": "Name (Arabic)", "User created successfully": "User created successfully", "User updated successfully": "User updated successfully", "User deleted successfully": "User deleted successfully", "Ride created successfully": "Ride created successfully", "Pricing not found": "Pricing not found", "Invalid pricing. The minimum fare must be greater than zero.": "Invalid pricing. The minimum fare must be greater than zero.", "This car type is currently unavailable.": "This car type is currently unavailable.", "The kilometer range field is required.": "The kilometer range field is required.", "The kilometer range must be a number.": "The kilometer range must be a number.", "The kilometer range must be a number with exactly one decimal place (e.g., 1,8).": "The kilometer range must be a number with exactly one decimal place (e.g., 1,8).", "The name field is required.": "The name field is required.", "The description field is required.": "The description field is required.", "The name must be a string.": "The name must be a string.", "The name may not be greater than :max characters.": "The name may not be greater than :max characters.", "The description must be a string.": "The description must be a string.", "The description may not be greater than :max characters.": "The description may not be greater than :max characters.", "The selected car type is invalid.": "The selected car type is invalid.", "Not available": "Not available", "Not assigned": "Not assigned", "Not calculated": "Not calculated", "Unknown": "Unknown", "Range": "Range", "Est.": "Est.", "min": "min", "Distance & Duration": "Distance & Duration", "Customer": "Customer", "Driver": "Driver", "Car Type": "Car Type", "Price": "Price", "Coupon": "Coupon", "The calculated fare is below the minimum fare. The minimum fare will be applied.": "The calculated fare is below the minimum fare. The minimum fare will be applied.", "No active shift found for the current time": "No active shift found for the current time", "No available car types found for the current time": "No available car types found for the current time", "Unable to calculate pricing. Please try again.": "Unable to calculate pricing. Please try again.", "Ride updated successfully": "Ride updated successfully", "Ride deleted successfully": "Ride deleted successfully", "Available": "Available", "Available for booking": "Available for booking", "Car type availability updated successfully": "Car type availability updated successfully", "Failed to update car type availability": "Failed to update car type availability", "Show Ride": "Show Ride", "Dashboard": "Dashboard", "Coupon Information": "Coupon Information", "View Ride Map": "View Ride Map", "Ride Map": "Ride Map", "Pickup Location": "Pickup Location", "Dropoff Location": "Dropoff Location", "Driver Location": "Driver Location", "Route": "Route", "Close": "Close", "User Information": "User Information", "Driver Information": "Driver Information", "Vehicle Information": "Vehicle Information", "Car Types": "Car Types", "Regions": "Regions", "Documents": "Documents", "Front License Image": "Front License Image", "Back License Image": "Back License Image", "Car Image": "Car Image", "Car Mechanics Image": "Car Mechanics Image", "Gender": "Gender", "Select Gender": "Select Gender", "Male": "Male", "Female": "Female", "Birthdate": "Birthdate", "Status": "Status", "Select Status": "Select Status", "Pending": "Pending", "Approved": "Approved", "Refused": "Refused", "Refuse Reason": "Refuse Reason", "License Number": "License Number", "Model": "Model", "Plate Number": "Plate Number", "Color": "Color", "Seats": "Seats", "Manufacturing Year": "Manufacturing Year", "Driver created successfully": "Driver created successfully", "Failed to create driver: ": "Failed to create driver: ", "Driver updated successfully": "Driver updated successfully", "Failed to update driver: ": "Failed to update driver: ", "Driver approved successfully. Notification has been sent.": "Driver approved successfully. Notification has been sent.", "Driver rejected successfully. Notification has been sent.": "Driver rejected successfully. Notification has been sent.", "Select Region": "Select Region", "Region": "Region", "Email": "Email", "Profile Picture": "Profile Picture", "Phone number must start with 963 followed by 9 digits": "Phone number must start with 963 followed by 9 digits", "First Name": "First Name", "Last Name": "Last Name", "Phone": "Phone", "Is Available": "Is Available", "YYYY-MM-DD": "YYYY-MM-DD", "Front License Preview": "Front License Preview", "Back License Preview": "Back License Preview", "Car Preview": "Car Preview", "Car Mechanics Preview": "Car Mechanics Preview", "Profile Preview": "Profile Preview", "Please fix the following errors:": "Please fix the following errors:", "Are You Sure?": "Are You Sure?", "This will happen permanently and will affect dependent elements": "This will happen permanently and will affect dependent elements", "Success": "Success", "Create Shift": "Create Shift", "Edit Shift": "Edit Shift", "Shift Name": "Shift Name", "Start Time": "Start Time", "End Time": "End Time", "Notes": "Notes", "Notes (English)": "Notes (English)", "Notes (Arabic)": "Notes (Arabic)", "Shift created successfully": "Shift created successfully", "Shift updated successfully": "Shift updated successfully", "Shift deleted successfully": "Shift deleted successfully", "Shift Information": "Shift Information", "Show Shift": "Show Shift", "ID": "ID", "Back to List": "Back to List", "Basic Information": "Basic Information", "Distance and Duration": "Distance and Duration", "Pricing": "Pricing", "Timestamps": "Timestamps", "Notes and Reasons": "Notes and Reasons", "Immediate": "Immediate", "Scheduled": "Scheduled", "Driver Gender": "Driver Gender", "Any": "Any", "Estimated Distance": "Estimated Distance", "Actual Distance": "Actual Distance", "Estimated Duration": "Estimated Duration", "Actual Duration": "Actual Duration", "Final Price": "Final Price", "Scheduled At": "Scheduled At", "Requested At": "Requested At", "Accepted At": "Accepted At", "Started At": "Started At", "Completed At": "Completed At", "Canceled At": "Canceled At", "Cancel Reason": "Cancel Reason", "Last updated": "Last updated", "No drivers available": "No drivers available", "Error loading drivers": "Error loading drivers", "Connecting...": "Connecting...", "Connected": "Connected", "Disconnected": "Disconnected", "Select a driver": "Select a driver", "API Token": "API Token", "Optional": "Optional", "Enter API token for authentication": "Enter API token for authentication", "If provided, this token will be used for Bearer authentication": "If provided, this token will be used for Bear<PERSON> authentication", "Connect to Pusher": "Connect to <PERSON><PERSON><PERSON>", "Select Driver": "Select Driver", "Loading drivers...": "Loading drivers...", "Channel Name": "Channel Name", "Subscribe to Channel": "Subscribe to Channel", "Unsubscribe": "Unsubscribe", "Location Simulator": "Location Simulator", "Latitude": "Latitude", "Longitude": "Longitude", "Send Location Update": "Send Location Update", "Event Log": "Event Log", "Waiting for events...": "Waiting for events...", "Clear Log": "Clear Log", "Coupons": "Coupons", "Create Coupon": "Create Coupon", "Edit Coupon": "Edit Coupon", "Show Coupon": "Show Coupon", "Code": "Code", "Usage Limit Per User": "Usage Limit Per User", "Type": "Type", "Value": "Value", "Start Date": "Start Date", "End Date": "End Date", "Active": "Active", "Expired": "Expired", "Percentage": "Percentage", "Fixed Amount": "Fixed Amount", "Amount": "Amount", "Discount percentage (0-100)": "Discount percentage (0-100)", "Fixed discount amount": "Fixed discount amount", "Validity Period": "Validity Period", "Translations": "Translations", "Description": "Description", "Description (English)": "Description (English)", "Description (Arabic)": "Description (Arabic)", "Unique code for the coupon (e.g., SUMMER2023)": "Unique code for the coupon (e.g., SUMMER2023)", "How many times a user can use this coupon": "How many times a user can use this coupon", "Type of discount": "Type of discount", "Discount value (percentage or fixed amount)": "Discount value (percentage or fixed amount)", "When the coupon becomes valid": "When the coupon becomes valid", "When the coupon expires": "When the coupon expires", "Coupon created successfully": "Coupon created successfully", "Coupon updated successfully": "Coupon updated successfully", "Coupon deleted successfully": "Coupon deleted successfully", "Are you sure you want to delete this coupon?": "Are you sure you want to delete this coupon?", "The coupon code is required.": "The coupon code is required.", "This coupon code is already in use.": "This coupon code is already in use.", "The usage limit per user is required.": "The usage limit per user is required.", "The usage limit must be at least 1.": "The usage limit must be at least 1.", "The start date is required.": "The start date is required.", "The end date is required.": "The end date is required.", "The end date must be after the start date.": "The end date must be after the start date.", "The coupon type is required.": "The coupon type is required.", "The coupon value is required.": "The coupon value is required.", "The coupon value must be at least 0.": "The coupon value must be at least 0.", "The percentage value cannot exceed 100.": "The percentage value cannot exceed 100.", "User profile not found": "User profile not found", "Failed to complete profile": "Failed to complete profile", "You cannot view super admin details": "You cannot view super admin details", "You cannot edit super admin details": "You cannot edit super admin details", "You cannot delete super admin": "You cannot delete super admin", "Cannot apply coupon to this ride status": "Cannot apply coupon to this ride status", "Coupon discount exceeds ride fare": "Coupon discount exceeds ride fare", "Coupon usage limit exceeded": "Coupon usage limit exceeded", "The coupon is invalid or has expired.": "The coupon is invalid or has expired.", "Location service unavailable": "Location service unavailable", "Show Wallet": "Show Wallet", "Wallets": "Wallets", "Create Wallet": "Create Wallet", "Edit Wallet": "Edit <PERSON>", "Wallet Information": "Wallet Information", "Owner Information": "Owner Information", "Current Owner Type": "Current Owner Type", "Current Owner ID": "Current Owner ID", "Current Owner Name": "Current Owner Name", "New Owner Type": "New Owner Type", "New Owner ID": "New Owner ID", "Owner Type": "Owner Type", "Owner ID": "Owner ID", "Select Type": "Select Type", "Select Owner": "Select Owner", "Balance": "Balance", "Is active": "Is active", "Created at": "Created at", "Updated at": "Updated at", "Name": "Name", "Yes": "Yes", "No": "No", "Wallet created successfully": "Wallet created successfully", "Wallet updated successfully": "Wallet updated successfully", "Transaction Details": "Transaction Details", "Related Information": "Related Information", "Sender Wallet": "Sender <PERSON>", "Receiver Wallet": "Receiver <PERSON><PERSON>", "Sender Name": "Sender Name", "Receiver Name": "Receiver Name", "Payment Method": "Payment Method", "Reference": "Reference", "Purpose": "Purpose", "Ride ID": "Ride ID", "Coupon Code": "Coupon Code", "Coupon Amount": "Coupon Amount", "Transaction created successfully": "Transaction created successfully", "Transaction updated successfully": "Transaction updated successfully", "Transaction deleted successfully": "Transaction deleted successfully", "pending": "Pending", "success": "Success", "failed": "Failed", "cash": "Cash", "e_payment": "Electronic Payment", "credit": "Credit", "debit": "Debit", "bonus": "Bonus", "fee": "Fee", "penalty": "Penalty", "Please wait :seconds seconds before requesting another OTP.": "Please wait :seconds seconds before requesting another OTP.", "Hi there! Your OTP code for Alia App is : :otpCode.": "Hi there! Your OTP code for Alia App is : :otpCode.", "Ride cannot be applied in the current state ,Ride must be pending": "Ride cannot be applied in the current state, ride must be pending", "Ride cannot be arrived in the current state.Ride must be accepted": "Ride cannot be arrived in the current state. Ride must be accepted", "Ride cannot be started in the current state.Ride must be accepted": "Ride cannot be started in the current state. Ride must be accepted", "Ride cannot be completed in the current state.Ride must be in progress": "Ride cannot be completed in the current state. Ride must be in progress", "Ride cannot be canceled in the current state.Ride must be pending": "Ride cannot be canceled in the current state. Ride must be pending", "Ride canceled successfully": "Ride canceled successfully"}