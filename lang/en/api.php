<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'codes' => [
        'success' => [
            'code' => "200",
            'message' => 'Success'
        ],
        '422' => [
            'code' => "422",
            'message' => 'validationError'
        ],
        'alreadyExist' => [
            'code' => "11",
            'message' => 'Username, gsm or email already exist'
        ],
        'missingParameters' => [
            'code' => "12",
            'message' => 'Missing required parameters'
        ],
        'userNotFound' => [
            'code' => "13",
            'message' => 'User not found'
        ],
        'codeNotValid' => [
            'code' => "14",
            'message' => 'Code not valid'
        ],
        'accountNotActive' => [
            'code' => "15",
            'message' => 'Account not active'
        ],
        'objectNotFound' => [
            'code' => "16",
            'message' => 'Object not found'
        ],
        'operationNotPermitted' => [
            'code' => "17",
            'message' => 'Operation not permitted'
        ],
        'profileNotCompleted' => [
            'code' => "18",
            'message' => 'Profile not completed'
        ],
        'paymentNotCompleted' => [
            'code' => "19",
            'message' => 'Payment Not Completed'
        ],
        'gsmNotMatch' => [
            'code' => "20",
            'message' => 'GSM must be in Saudi Arabia'
        ],
        'relationAlreadyExist' => [
            'code' => "21",
            'message' => 'Relation is already exists'
        ],
        'channelError' => [
            'code' => "22",
            'message' => 'Error creating channel'
        ],
        'alreadyRated' => [
            'code' => "23",
            'message' => 'You already rated this item'
        ],
        'privateAccount' => [
            'code' => "24",
            'message' => 'This account is private'
        ],
        'fileNotValid' => [
            'code' => "25",
            'message' => 'File not valid'
        ],
        'notAuthorized' => [
            'code' => "26",
            'message' => 'You\'re not authorized to do this action'
        ],
        //HTTP Errors
        'unauthorized' => [
            'code' => "401",
            'message' => 'Unauthorized'
        ],
        'methodNotAllowed' => [
            'code' => "405",
            'message' => 'Method Not Allowed'
        ],
        'badRequest' => [
            'code' => "400",
            'message' => 'Bad request'
        ],
        'forbidden' => [
            'code' => "403",
            'message' => 'Forbidden'
        ],
        'resourceNotFound' => [
            'code' => "404",
            'message' => 'Resource Not Found'
        ],
        'objectCreated' => [
            'code' => "201",
            'message' => 'Object Created'
        ],
        'noContent' => [
            'code' => "204",
            'message' => 'No Content'
        ],
        'partialContent' => [
            'code' => "206",
            'message' => 'Partial Content'
        ]
    ],

    'sms' => [
        'welcomeText' => 'Thank you for using Aljalad app, your verification code is :code, it\'s valid for :valid minutes',
        'inviteText' => 'Your friend :name is using Aljalad app, and invites you to try it, check it out at https://www.aljalad.com',
    ],

    'email' => [
        'welcomeText' => 'Hi there,',
        'inviteText' => 'Kindly use (:password) as your verification code to reset your password',
        'passwordText' => 'Kindly use (:password) as your password to login to your account',
    ],

    'rating' => [
        'errors' => [
            'not_customer' => 'We appreciate your interest, but this feature is available only for customers to rate their drivers.',
            'not_assigned_customer' => 'Thank you for your interest in rating, but you can only rate drivers for rides you have personally taken.',
            'ride_not_completed' => 'We understand you want to share your experience, but ratings can only be submitted after the ride is completed.',
            'not_driver' => 'We appreciate your interest, but this feature is available only for drivers to rate their customers.',
            'not_assigned_driver' => 'Thank you for your interest in rating, but you can only rate customers for rides you have personally driven.',
            'already_rated' => 'Thank you for your enthusiasm! We noticed you have already submitted a rating for this ride.',
            'invalid_user_type' => 'We apologize, but ratings can only be viewed by customers and drivers.'
        ],
        'success' => [
            'driver_rated' => 'Thank you for your valuable feedback! Your rating for the driver has been submitted successfully and helps us improve our service.',
            'customer_rated' => 'Thank you for your valuable feedback! Your rating for the customer has been submitted successfully and helps build a better community.',
            'ratings_retrieved' => 'Here are your ratings! Thank you for being part of our community.',
            'driver_ratings_retrieved' => 'Driver ratings have been successfully retrieved. We appreciate your interest!',
            'customer_ratings_retrieved' => 'Customer ratings have been successfully retrieved. We appreciate your interest!'
        ]
    ]
];