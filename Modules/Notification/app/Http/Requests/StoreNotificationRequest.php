<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Validation\Rule;

use Modules\Notification\Enums\TopicEnum;
use Illuminate\Foundation\Http\FormRequest;

class StoreNotificationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.body"] = ['required', "string"];
            $langRules["$locale.title"] = ['required', "string", 'max:255'];
        }
        return [
            'topic' => ['required', Rule::enum(TopicEnum::class)],
        ]+$langRules;
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
