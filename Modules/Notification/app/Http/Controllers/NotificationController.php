<?php

namespace Modules\Notification\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Notification\Enums\TopicEnum;
use Modules\Notification\Models\DeviceToken;
use Modules\Notification\Services\NotificationService;
use Modules\Notification\Http\Requests\StoreNotificationRequest;

class NotificationController extends Controller
{
    function __construct(private NotificationService $notificationService)
    {
    }
    public function create()
    {
        return view('notification::notifications.create');
    }

    public function store(StoreNotificationRequest $request)
    {
        $this->notificationService->sendNotificationForTopic(
            $request->en['title'],
            $request->en['body'],
            $request->ar['title'],
            $request->ar['body'],
            auth('web')->user()->userable,
            [],
            $request->topic
        );
        return redirect()->route('dashboard.notifications.create')->with(['success' => trans('Done Successfully')]);
    }

    public function storeFcmToken(Request $request)
    {
        info($request->all());
        if (!DeviceToken::whereToken($request->token)->exists()) {
            $user = auth()->user();
            $user->deviceTokens()
                ->updateOrCreate(['token' => $request->token], ['token' => $request->token, 'user_id' => $user->id]);
            // $this->notificationService->registerToken($request->token, TopicEnum::ALL->value);
            $parts = explode('\\', $user->userable_type);
            $modelName = end($parts);
            $topic = strtolower($modelName);
            info('topics ');
            info($topic);
            info(TopicEnum::ALL->value);
            $this->notificationService->registerTokenToTopics($request->token, [$topic, TopicEnum::ALL->value]);

            return response()->json(['Token successfully stored.']);
        }
        return null;
    }

    public function markNotificaitonSeen(Request $request): bool
    {
        $user = auth()->user();
        $user->notifications()->whereSeen(false)->updateExistingPivot(
            $user->notifications()->pluck('notifications.id')->toArray(),
            ['seen' => true]
        );
        return true;
    }
}
