<?php

namespace Modules\Notification\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Modules\Notification\Services\NotificationService;
use Modules\Notification\Transformers\NotificationResource;

/**
 * @group Notifications
 *
 * APIs for managing user notifications
 */
class NotificationController extends Controller
{
    public function __construct(private NotificationService $notificationService){}


    /**
     * Get User Notifications
     *
     * Retrieve authenticated user's notifications with pagination
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (default: 20). Example: 20
     */
    public function myNotifications()
    {
        $notifications = $this->notificationService->byUser(auth()->user(), 20);
        $notifications = NotificationResource::collection($notifications)->response()->getData();
        return response()->success($notifications);
    }
}
