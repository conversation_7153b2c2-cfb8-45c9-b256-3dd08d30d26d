<?php

namespace Modules\Notification\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

use Astrotomic\Translatable\Translatable;
use Modules\Notification\Enums\NotificationTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;


class Notification extends Model implements TranslatableContract
{
    use HasFactory, Translatable;
    protected $fillable = [
        'type',
        'extra',
        'ownerable_id',
        'ownerable_type'
    ];

    protected $translatedAttributes = [
        'title',
        'body',
    ];

    protected $casts = [
        'type' => NotificationTypeEnum::class,
        'extra' => 'array',
    ];
    public function users()
    {
        return $this->belongsToMany(User::class, 'notification_user');
    }

    public function ownerable()
    {
        return $this->morphTo('ownerable');
    }
}