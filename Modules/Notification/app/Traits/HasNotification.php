<?php

namespace Modules\Notification\Traits;

use Modules\Notification\Models\DeviceToken;
use Modules\Notification\Models\Notification;
use Modules\Notification\Models\NotificationUser;

trait HasNotification
{
    public function notifications()
    {
        return $this->belongsToMany(Notification::class, 'notification_user')
            ->withPivot('seen');
    }

    public function userNotifications()
    {
        return $this->hasMany(NotificationUser::class);
    }


    public function deviceTokens()
    {
        return $this->hasMany(DeviceToken::class);
    }
}