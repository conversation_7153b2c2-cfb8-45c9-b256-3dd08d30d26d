<?php

namespace Modules\Notification\Services;


use App\Models\User;
use App\Models\Admin;
use App\Models\Customer;
use App\Models\Provider;
use Illuminate\Support\Facades\DB;
use Kreait\Firebase\Contract\Messaging;
use Modules\Notification\Enums\TopicEnum;
use Kreait\Firebase\Messaging\CloudMessage;
use Modules\Notification\Models\DeviceToken;
use Kreait\Laravel\Firebase\Facades\Firebase;
use Modules\Notification\Models\Notification;
use Modules\Notification\Enums\NotificationTypeEnum;
use Kreait\Firebase\Messaging\Notification as MessagingNotification;

/**
 * Class NotificationService.
 */
class NotificationService
{
    public Messaging $messaging;
    public function __construct()
    {
        $this->messaging = Firebase::messaging();
    }
    public function byUser(User $user, $perPage)
    {
        return $user->notifications()->orderByDesc('created_at')->paginate($perPage);
    }

    public function sendNotificationForTopic(string $title, string $body, $titleAr, $bodyAr, Admin|User $owner, array $data = [], ?string $topic = "all")
    {
        $toSend = [
            'title' => app()->getLocale() == 'ar' ? $titleAr : $title,
            'body' => app()->getLocale() == 'ar' ? $bodyAr : $body,
            'type' => NotificationTypeEnum::NORMAL->value,
            'extra' => json_encode($data)
        ];
        $notification = MessagingNotification::fromArray($toSend);
        $message = CloudMessage::new();
        $message = $message->withTarget("topic", $topic);
        $message = $message->withData($toSend);
        $message = $message->withNotification($notification);
        $response = null;
        try {
            $response = $this->messaging->send($message);
            info('send notifications to topic success');
        } catch (\Throwable $th) {
        }
        $notification = Notification::create([
            'ar' => ['title' => $titleAr, 'body' => $bodyAr],
            'en' => ['title' => $title, 'body' => $body],
            'type' => NotificationTypeEnum::NORMAL->value,
            'extra' => $data,
            'ownerable_id' => $owner?->id,
            'ownerable_type' => $owner::class,
        ]);
        $usersIds = [];
        if ($topic == TopicEnum::ALL->value) {
            $usersIds = User::query()->pluck('id')->toArray();
        }

        $notification->users()->attach($usersIds);
        return $response;
    }

    public function sendNotificationForUser(
        User $user,
        string $title,
        string $body,
        string $titleAr,
        string $bodyAr,
        NotificationTypeEnum $type,
        Admin|User  | Customer $owner,
        array $data = []
    ) {
        DB::beginTransaction();
        $data['date'] = now();
        $notification = MessagingNotification::fromArray([
            'title' => app()->getLocale() == 'ar' ? $titleAr : $title,
            'body' => app()->getLocale() == 'ar' ? $bodyAr : $body,
            "data" => $data
        ]);
        $toSend = [
            'title' => app()->getLocale() == 'ar' ? $titleAr : $title,
            'body' => app()->getLocale() == 'ar' ? $bodyAr : $body,
            'type' => $type->name,
            'extra' => json_encode($data)
        ];
        $message = CloudMessage::new();
        $message = $message->withData($toSend);
        $message = $message->withNotification($notification);
        $notification = Notification::create([
            'ar' => ['title' => $titleAr, 'body' => $bodyAr],
            'en' => ['title' => $title, 'body' => $body],
            'type' => $type->value,
            'extra' => $data,
            'ownerable_id' => $owner?->id,
            'ownerable_type' => $owner::class,
        ]);

        $tokens = $user?->deviceTokens()?->pluck('token')->toArray();
        $notification->users()->attach([$user->id]);
        try {
            // info($tokens);
            $report = $this->messaging->sendMulticast($message, $tokens);
            // info('send notification t user');
            // info($report);
        } catch (\Throwable $th) {
            info('error when send notification t user');
            info($th->getMessage());
        }
        DB::commit();
    }
    public function sendNotificationForUsers(array $usersIds, string $title, string $body, NotificationTypeEnum $type, Admin|User $owner, array $data = [])
    {
        DB::beginTransaction();
        $data['date'] = now();
        $toSend = [
            'title' => $title,
            'body' => $body,
            'type' => $type->name,
            'date' => now(),
            'extra' => json_encode($data)
        ];
        $notification = MessagingNotification::fromArray([
            "title" => $title,
            "body" => $body,
            "data" => $data
        ]);
        $message = CloudMessage::new();
        $message = $message->withNotification($notification);
        $message = $message->withData($toSend);
        $notification = Notification::create([
            'title' => $title,
            'body' => $body,
            'type' => $type->value,
            'extra' => $data,
            'ownerable_id' => $owner?->id,
            'ownerable_type' => $owner::class,
        ]);
        $tokens = DeviceToken::query()->whereIn('user_id', $usersIds)->pluck('token')->toArray();
        try {
            $report = $this->messaging->sendMulticast($message, $tokens);
            info('send notification to users:');
        } catch (\Throwable $th) {
            info($th->getMessage());
        }
        $notification->users()->attach($usersIds);
        DB::commit();
    }

    public function registerToken($tokens, string $topic = "all")
    {
        $this->messaging->subscribeToTopic($topic, $tokens);
    }
    public function registerTokenToTopics($tokens, array $topics = ["all"])
    {
        $this->messaging->subscribeToTopics($topics, $tokens);
    }
    public function unRegisterToken($tokens, string $topic = "all", User $user = null)
    {
        $this->messaging->unsubscribeFromTopic($topic, $tokens);
    }
}
