<?php

use App\Models\Service;
use Illuminate\Support\Facades\Route;
use Modules\Notification\Http\Controllers\NotificationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('dashboard')->middleware('auth')->name('dashboard.')->group(function (): void {
    Route::resource('notifications', NotificationController::class)->only(['create', 'store']);
    Route::post('store-fcm-token', [NotificationController::class, 'storeFcmToken'])->name('store.token');
    Route::post('mark-notifications-seen', [NotificationController::class, 'markNotificaitonSeen'])->name('notifications.seen');

});

