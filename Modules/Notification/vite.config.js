import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    build: {
        outDir: '../../public/build-notification',
        emptyOutDir: true,
        manifest: true,
    },
    plugins: [
        laravel({
            publicDirectory: '../../public',
            buildDirectory: 'build-notification',
            input: [
                __dirname + '/resources/assets/sass/app.scss',
                __dirname + '/resources/assets/js/app.js',
                __dirname + '/resources/js/firebase-script.js'
            ],
            refresh: true,
        }),
    ],
});

//export const paths = [
//    'Modules/Notification/resources/assets/sass/app.scss',
//    'Modules/Notification/resources/assets/js/app.js',
//];