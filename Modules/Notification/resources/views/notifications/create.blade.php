 @extends('layouts/layoutMaster')
 @section('title', __('Notifications Management'))

 @section('vendor-style')

     @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/tagify/tagify.scss', 'resources/assets/vendor/libs/bootstrap-select/bootstrap-select.scss', 'resources/assets/vendor/libs/typeahead-js/typeahead.scss'])

 @endsection

 @section('content')
     <div class="content-wrapper">
         <div class="content-body">
             <div class="col-md-12">
                 <x-any-errors></x-any-errors>
                 <form class="form" method="post" enctype="multipart/form-data"
                     action="{{ route('dashboard.notifications.store') }}">
                     @csrf
                     <div class="card mb-4">
                         <h5 class="card-header">{{ __('Notification Details') }}</h5>
                         <div class="card-body">
                             <div class="row mb-4">
                                 <div class="col-6">
                                     <label for="title" class="form-label">{{ __('Title Ar') }}</label>
                                     <input required type="text" id="title" name="ar[title]" class="form-control"
                                         placeholder="{{ __('Title Ar') }}" />
                                 </div>
                                 <div class="col-6">
                                     <label for="title" class="form-label">{{ __('Title En') }}</label>
                                     <input required type="text" id="title" name="en[title]" class="form-control"
                                         placeholder="{{ __('Title En') }}" />
                                 </div>
                             </div>
                             <div class="row mb-4">
                                 <div class="col-12 m-2">
                                     <textarea name="en[body]" placeholder="{{ __('Body En') }}" id="body" cols="120" rows="10"></textarea>
                                     {{-- <x-CKEDITOR id="body" name="body" label="{{ __('Body') }}">
                                     </x-CKEDITOR> --}}
                                 </div>
                                 <div class="col-5 m-2">
                                     <textarea name="ar[body]" id="body" placeholder="{{ __('Body Ar') }}" cols="120" rows="10"></textarea>
                                     {{-- <x-CKEDITOR id="body" name="body" label="{{ __('Body') }}">
                                     </x-CKEDITOR> --}}
                                 </div>
                             </div>
                             <div class="row mb-4">
                                 <div class="col-6">
                                     <label for="topic" class="form-label">{{ __('Type') }}</label>
                                     <select class="form-control form-select select2" name="topic" id="topic">
                                         {{-- <option value={{ null }} disabled selected>{{ __('Select Option') }} --}}
                                         </option>
                                         @foreach (\Modules\Notification\Enums\TopicEnum::cases() as $topic)
                                             <option value="{{ $topic->value }}"> {{ __($topic->value) }}</option>
                                         @endforeach
                                         {{-- <option value="'all"> {{ __('All') }}
                                         <option value="provider"> {{ __('Providers') }}
                                         <option value="professional"> {{ __('Professionals') }}
                                         <option value="customer"> {{ __('Customers') }}
                                         </option> --}}
                                     </select>
                                 </div>
                                 <div class="col-6">

                                 </div>
                             </div>
                             <div class="col-12">
                                 <button type="submit" class="btn btn-primary me-1">{{ __('Submit') }}</button>
                             </div>
                         </div>

                     </div>
                 </form>
             </div>
         </div>
     </div>

 @endsection


 @section('vendor-script')
     @vite(['resources/assets/js/forms-selects.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/js/forms-typeahead.js'])

 @endsection


 @section('page-script')
     @vite(['resources/assets/js/forms-selects.js'])

 @endsection
