<style>
    .notification-list {
        height: 40vh;
        /* width: 60vh; */
        overflow-x: hidden;
    }
</style>
@vite(['resources/assets/vendor/libs/toastr/toastr.scss', 'resources/assets/vendor/libs/typeahead-js/typeahead.scss', 'resources/assets/vendor/libs/animate-css/animate.scss'])

<script src="https://www.gstatic.com/firebasejs/8.7.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.7.0/firebase-analytics.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.7.0/firebase-messaging.js"></script>
@vite(['resources/assets/vendor/libs/toastr/toastr.js', 'resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/typeahead-js/typeahead.js'])
@vite(['resources/assets/js/ui-toasts.js'])

{{-- notifications --}}
{{-- @auth --}}
<script>
   const firebaseConfig = {
    apiKey: "AIzaSyDxOBsCMxQ--7DtulxDTUtV6JnV9EfI8Nw",
    authDomain: "deyram-50903.firebaseapp.com",
    projectId: "deyram-50903",
    storageBucket: "deyram-50903.firebasestorage.app",
    messagingSenderId: "684592807107",
    appId: "1:684592807107:web:698233e608f9d28c73f676",
    measurementId: "G-N2276TY9FT"
  };
</script>
@vite(['Modules/Notification/resources/js/firebase-script.js'])
<script>
    $(document).ready(function() {
        $("#mark-as-seen, #all_notifications").click(function() {
            $.ajax({
                url: route('dashboard.notifications.seen'),
                method: "POST",
                data: {
                    "_token": "{{ csrf_token() }}",
                    userId: {{ auth()->user()?->id }}
                },
                success: function(response) {
                    if (response) {
                        $('.dropdown.notification li').removeClass('new');
                        $('.noti-count').text('0');
                    }
                },
                onFinishing: function(event, currentIndex) {},
                error: function(xhr) {
                    console.log(xhr);
                },
                beforeSend: function() {},
                complete: function() {}
            });
        });
    });
</script>
{{-- /Firebase --}}
