<!-- Notification -->
<li class="nav-item dropdown-notifications navbar-dropdown dropdown me-3 me-xl-1">
    <a class="nav-link dropdown-toggle hide-arrow" id="all_notifications" href="javascript:void(0);" data-bs-toggle="dropdown"
        data-bs-auto-close="outside" aria-expanded="false">
        <i class="ti ti-bell ti-md"></i>
        <span class="badge bg-danger rounded-pill badge-notifications noti-count"> {{ $_notificationCount }}</span>
    </a>
    <ul class="dropdown-menu dropdown-menu-end py-0">
        <li class="dropdown-menu-header border-bottom">
            <div class="dropdown-header d-flex align-items-center py-3">
                <h5 class="text-body mb-0 me-auto">Notification</h5>
                @hasusertype('admin')
                    <a href="{{ route('dashboard.notifications.create') }}" class="dropdown-notifications-all text-body"
                        data-bs-toggle="tooltip" data-bs-placement="top" title="Add">
                        <i class="fa-solid fa-plus"></i></a>
                @endhasusertype
            </div>
        </li>
        <li class="dropdown-notifications-list scrollable-container">
            <ul class="list-group list-group-flush">
                @foreach ($_notification as $notification)
                    <a href="{{ $notification->extra['url'] ?? 'javascript:void(0)' }}">
                        <li class="list-group-item list-group-item-action dropdown-notifications-item">
                            <div class="d-flex">

                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $notification->title }}</h6>
                                    <p class="mb-0">{!! $notification->body !!}</p>
                                    <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                                </div>
                                <div class="flex-shrink-0 dropdown-notifications-actions">
                                    <a href="javascript:void(0)" class="dropdown-notifications-read">
                                        @if (!$notification->pivot->seen)
                                            <span class="badge badge-dot"></span>
                                        @endif
                                    </a>
                                </div>
                            </div>
                        </li>
                    </a>
                @endforeach

            </ul>
        </li>
    </ul>
</li>
<!--/ Notification -->
