firebase.initializeApp(firebaseConfig);
firebase.analytics();
const messaging = firebase.messaging();
messaging.requestPermission().then(function () {
    return messaging.getToken();
}).then(function (response) {
    console.log('firebase token: ' + response);
    console.log(response);
    

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: route('dashboard.store.token'),
        type: 'POST',
        data: {
            token: response
        },
        dataType: 'JSON',
        success: function (response) {
            // alert('Token stored.');
        },
        error: function (error) {
            // alert(error);
        },
    });
}).catch(function (error) {
    // alert(error);
});

messaging.onMessage(function (payload) {
    console.log('Foreground Message', payload);
    console.log(payload.notification);
    let extra = {
        url: '#'
    };
    if(payload.data.extra) {
         extra = JSON.parse(payload.data.extra);
    }


    // console.log(extra.url);
    
    const title = payload.notification.title;
    const body = payload.notification.body;
    const options = {
        body: payload.data.body,
        icon: payload.data.icon,
    };
    toastr['success'](
        `<a href="${extra.url}" style="color: inherit; text-decoration: none;">
            <strong>${title}</strong>
            <br>
            👋 New Notification!
        </a>`, '', {
        closeButton: true,
        timeOut: 8000,
        tapToDismiss: false,
        rtl: $('html').attr('data-textdirection') === 'rtl',
        onclick: function () {
            window.location.href = extra.url;
        }
    });
    $('.noti-count').html(parseInt($('.noti-count').html()) + 1);
    $('.notification-list').prepend(`
        <li class="list-group-item list-group-item-action dropdown-notifications-item">
            <div class="d-flex">
            
                <div class="flex-grow-1">
                    <h6 class="mb-1">${payload.notification.title}</h6>
                    <p class="mb-0">${payload.notification.body}</p>
                    <small class="text-muted">${moment.utc(payload.created_at).fromNow()}</small>
                </div>
                <div class="flex-shrink-0 dropdown-notifications-actions">
                    <a href="javascript:void(0)" class="dropdown-notifications-read">
                        <span class="badge badge-dot"></span>
                    </a>
                </div>
            </div>
        </li>
    `);

});
// navigator.serviceWorker.register('firebase-messaging-sw.js')