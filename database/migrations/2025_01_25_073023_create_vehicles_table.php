<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_id')->constrained()->cascadeOnDelete()->comment(json_encode(['relationshipType' => "belongsTo"]));;
            $table->string('model')->nullable(); // Vehicle model name
            $table->string('plate_number')->unique(); 
            $table->string('color'); 
            $table->integer('seats'); 
            $table->year('manufacturing_year'); // Year of manufacturing
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
