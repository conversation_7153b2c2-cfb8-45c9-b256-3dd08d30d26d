<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('ride_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ride_id')->constrained()->cascadeOnDelete();
            
            $table->decimal('longitude', 10, 8);
            $table->decimal('latitude', 11, 8);
            
            $table->string('address');
            $table->string('type')->default('via');
            $table->integer('sort_order');
            $table->timestamps();
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('ride_points');
    }
};