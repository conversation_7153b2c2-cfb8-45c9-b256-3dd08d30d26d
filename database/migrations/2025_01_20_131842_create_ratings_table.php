<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ride_id')->constrained()->onDelete('cascade');

            // Polymorphic relationship for the rater (who is giving the rating)
            $table->morphs('rater');

            // Polymorphic relationship for the rateable (who/what is being rated)
            $table->morphs('rateable');

            $table->unsignedTinyInteger('rating')->comment('Rating value from 1 to 5');
            $table->text('comment')->nullable()->comment('Optional comment with the rating');
            $table->timestamps();

            $table->unique(['ride_id', 'rater_id', 'rater_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
