<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Get all foreign keys for a table
     * 
     * @param string $tableName
     * @return array
     */
    private function getForeignKeys(string $tableName): array
    {
        $database = config('database.connections.mysql.database');
        return DB::select("
            SELECT
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE
                TABLE_SCHEMA = ?
                AND TABLE_NAME = ?
                AND REFERENCED_TABLE_NAME IS NOT NULL
        ", [$database, $tableName]);
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the foreign key already exists with customers table
        $foreignKeys = $this->getForeignKeys('rides');
        $hasCustomerFK = false;
        
        foreach ($foreignKeys as $foreignKey) {
            if ($foreignKey->COLUMN_NAME === 'customer_id' && $foreignKey->REFERENCED_TABLE_NAME === 'customers') {
                $hasCustomerFK = true;
                break;
            }
        }
        
        // If the foreign key already exists with customers table, do nothing
        if ($hasCustomerFK) {
            return;
        }
        
        // First, handle rides with customer_ids that don't exist in the customers table
        DB::statement("
            UPDATE rides r
            JOIN users u ON r.customer_id = u.id
            JOIN customers c ON u.userable_id = c.id AND u.userable_type = 'App\\Models\\Customer'
            SET r.customer_id = c.id
            WHERE NOT EXISTS (SELECT 1 FROM customers WHERE id = r.customer_id)
        ");
        
        // Delete rides that belong to users without a customer record
        DB::statement("
            DELETE r FROM rides r
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE c.id IS NULL
        ");
        
        // Check if there's a foreign key with the users table
        $hasUserFK = false;
        foreach ($foreignKeys as $foreignKey) {
            if ($foreignKey->COLUMN_NAME === 'customer_id' && $foreignKey->REFERENCED_TABLE_NAME === 'users') {
                $hasUserFK = true;
                break;
            }
        }
        
        // Drop the existing foreign key if it exists
        if ($hasUserFK) {
            Schema::table('rides', function (Blueprint $table) {
                $table->dropForeign(['customer_id']);
            });
        }
        
        // Add the new foreign key to the customers table
        Schema::table('rides', function (Blueprint $table) {
            $table->foreign('customer_id')
                  ->references('id')
                  ->on('customers')
                  ->onDelete('restrict')
                  ->onUpdate('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the foreign key to customers
        Schema::table('rides', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        
        // Restore the original foreign key to users
        Schema::table('rides', function (Blueprint $table) {
            $table->foreign('customer_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
        });
    }
};
