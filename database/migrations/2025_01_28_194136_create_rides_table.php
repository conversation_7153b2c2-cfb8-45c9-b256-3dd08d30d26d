<?php

use App\Enums\RideStatusEnum;
use App\Enums\RideTypeEnum;
use App\Models\Customer;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rides', function (Blueprint $table) {
            $table->comment(json_encode(['relationColumn' => 'name']));
            $table->id();
            
            $table->foreignId('customer_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('driver_id')->nullable()->constrained('drivers')->cascadeOnDelete();
            $table->foreignId('pricing_id')->nullable()->constrained()->cascadeOnDelete();
            $table->foreignId('coupon_id')->nullable()->constrained()->cascadeOnDelete();

             // Status & timing (unchanged)
            $table->string('status')->default(RideStatusEnum::PENDING->value);
            $table->string('type');
            $table->string('driver_gender');
            $table->timestamp('scheduled_at')->nullable()->comment('For scheduled rides');
            $table->timestamp('requested_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('canceled_at')->nullable();

            // Metrics (unchanged)
            $table->decimal('estimated_distance', 8,3);
            $table->decimal('actual_distance', 8,3)->default(0);
            $table->integer('estimated_duration');
            $table->integer('actual_duration')->default(0);
            $table->integer('fare')->nullable();
            $table->integer('final_price')->nullable();

            // Additional info (unchanged)
            $table->text('note')->nullable();
            $table->text('cancel_reason')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rides');
    }
};
