<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('driver_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_id')->constrained()->cascadeOnDelete();
            $table->foreignId('ride_id')->nullable()->constrained()->cascadeOnDelete();
            $table->decimal('latitude', 11, 8);
            $table->decimal('longitude', 11, 8);
            $table->decimal('heading', 5, 2)->nullable()->comment('Direction in degrees (0-360)');
            $table->decimal('speed', 5, 2)->nullable()->comment('Speed in km/h');
            $table->string('address')->nullable();
            $table->boolean('is_online')->default(true);
            $table->timestamps();

            $table->index(['driver_id', 'created_at']);
            $table->index(['ride_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('driver_locations');
    }
};
