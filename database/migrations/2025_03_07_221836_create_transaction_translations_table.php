<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    
    public function up(): void
    {
        Schema::create('transaction_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->string('locale')->index();
            $table->unique(['transaction_id', 'locale']);
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_translations');
    }
};
