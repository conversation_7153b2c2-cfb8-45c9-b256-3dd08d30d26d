<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    
    public function up(): void
    {
        Schema::create('company_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->string('locale')->index();
            $table->unique(['company_id', 'locale']);
            $table->text('name')->nullable();
            $table->text('descrption')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_translations');
    }
};
