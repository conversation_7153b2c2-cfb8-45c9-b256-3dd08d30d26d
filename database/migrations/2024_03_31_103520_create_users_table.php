<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->comment(json_encode([
                'hasMedia' => true,
                'relationColumn' => 'name', 
                'mediaCollections' => [
                    'profile'   => 'IMAGE',
                    
                ]
            ]));
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->nullable();
            $table->string('phone');
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->boolean('is_active')->default(1);
            $table->date('birthdate');
            $table->string('gender');
            $table->string('code',5)->nullable();
            $table->nullableMorphs('userable');
            $userableMorphTypeComment = json_encode([
                'relationshipType' => "MORPH",
                'relatedTables' => ['drivers' => "morphOne",'customers' => "morphOne", 'admins' => "morphOne"]
            ]);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};