<?php

use App\Enums\DriverStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->boolean('is_available')->default(true);
            $table->string('license_number')->unique();
            $table->string('status')->default(DriverStatusEnum::PENDING->value);
            $table->string('refuse_reason')->nullable();
            $table->decimal('current_latitude', 11, 8)->nullable();
            $table->decimal('current_longitude', 11, 8)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drivers');
    }
};
