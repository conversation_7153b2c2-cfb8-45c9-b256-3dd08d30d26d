<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('car_type_id')->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->string('locale')->index();
            $table->unique(['car_type_id', 'locale']);
            $table->text('comment')->nullable();
            $table->string('rateable_type'); 
            $table->foreignId('rateable_id'); 

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings_translations');
    }
};
