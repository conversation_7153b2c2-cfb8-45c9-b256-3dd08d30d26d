<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shift_id')->constrained()->cascadeOnDelete()
            ->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->foreignId('car_type_id')->constrained()->cascadeOnDelete()
            ->comment(json_encode(['relationshipType' => "belongsTo"]));
            $table->integer('minimum_fare'); 
            $table->integer('flag_down_fee'); 
            $table->integer('km_price'); 
            $table->integer('miu_price'); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing');
        Schema::dropForeign(['car_type_id']);
    }
};
