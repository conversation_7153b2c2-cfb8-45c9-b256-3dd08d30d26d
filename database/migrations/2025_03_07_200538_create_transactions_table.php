<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_wallet_id')->nullable()->constrained('wallets')->cascadeOnDelete();
            $table->foreignId('receiver_wallet_id')->nullable()->constrained('wallets')->cascadeOnDelete();

            $table->foreignId('ride_id')->nullable()->constrained('rides')->nullOnDelete();
            $table->foreignId('coupon_id')->nullable()->constrained('coupons');

            $table->morphs('initiator'); 
            $table->string('payment_method',20)
                ->comment(json_encode([
                    'isEnumColumn' => true,
                    'enumReturnType' => 'string',
                    'enumCases' => ['CASH' => 'cash', 'E_PAYMENT' => 'e_payment']
                ]));
            
            $table->string('status',20)
                ->comment(json_encode([
                    'isEnumColumn' => true,
                    'enumReturnType' => 'string',
                    'enumCases' => ['PENDING' => 'peding', 'SUCCESS' => 'success', 'FAILED' => 'failed']
                ]));
            $table->string('type',20)
                ->comment(json_encode([
                    'isEnumColumn' => true,
                    'enumReturnType' => 'string',
                    'enumCases' => ['CREDIT' => 'credit', 'DEBIT' => 'debit', 'BONUS' => 'bonus','FEE' => 'fee',]
                ]));
            $table->string('reference')->unique();
            $table->unsignedInteger('amount');
            
            $table->string('coupon_type')->nullable();
            $table->unsignedInteger('coupon_amount')->nullable();
            $table->timestamps();
        });
    }

   
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
