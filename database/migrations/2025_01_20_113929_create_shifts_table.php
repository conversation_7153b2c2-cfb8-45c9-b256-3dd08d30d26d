<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shifts', function (Blueprint $table) {
            $table->comment(json_encode(['relationColumn' => 'name' ,'notes']));
            $table->id(); 
            $table->time('start_time'); 
            $table->time('end_time'); 
            
            $table->timestamps();
        });
    }

   
};
