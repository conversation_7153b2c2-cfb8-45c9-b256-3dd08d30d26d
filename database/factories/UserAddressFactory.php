<?php

namespace Database\Factories;

use App\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserAddressFactory extends Factory
{
    protected $model = UserAddress::class;

    public function definition()
    {
        return [
          
'user_id' => $this->faker->randomElement(\App\Models\User::pluck('id')->toArray()),
'name' => $this->faker->word,
'description' => $this->faker->paragraph(),
'latitude' => $this->faker->randomFloat(2, 0, 100),
'longitude' => $this->faker->randomFloat(2, 0, 100),


        ];
    }
}
