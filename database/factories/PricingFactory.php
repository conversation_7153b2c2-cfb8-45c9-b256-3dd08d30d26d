<?php

namespace Database\Factories;

use App\Models\CarType;
use App\Models\Pricing;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

class PricingFactory extends Factory
{
    protected $model = Pricing::class;

    public function definition()
    {
        return [

            'shift_id' => Shift::factory(),
            'car_type_id' => CarType::factory(),
            'minimum_fare' => $this->faker->randomFloat(2, 0, 100),
            'flag_down_fee' => $this->faker->randomFloat(2, 0, 100),
            'km_price' => $this->faker->randomFloat(2, 0, 100),
            'miu_price' => $this->faker->randomFloat(2, 0, 100),


        ];
    }
}
