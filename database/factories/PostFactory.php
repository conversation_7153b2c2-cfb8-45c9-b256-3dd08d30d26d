<?php

namespace Database\Factories;

use App\Models\Post;
use Illuminate\Database\Eloquent\Factories\Factory;

class PostFactory extends Factory
{
    protected $model = Post::class;

    public function definition()
    {
        return [
          
'title' => $this->faker->word,
'content' => $this->faker->paragraph(),
'user_id' => $this->faker->randomElement(\App\Models\User::pluck('id')->toArray()),
'category_id' => $this->faker->randomElement(\App\Models\Category::pluck('id')->toArray()),
'postable_type' => $this->faker->word,
'postable_id' => $this->faker->numberBetween(1, 100),


        ];
    }
}
