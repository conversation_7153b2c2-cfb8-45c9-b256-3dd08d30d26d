<?php

namespace Database\Factories;

use App\Models\CategoryUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryUserFactory extends Factory
{
    protected $model = CategoryUser::class;

    public function definition()
    {
        return [
          
'user_id' => $this->faker->randomElement(\App\Models\User::pluck('id')->toArray()),
'category_id' => $this->faker->randomElement(\App\Models\Category::pluck('id')->toArray()),


        ];
    }
}
