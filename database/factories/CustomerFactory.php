<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CustomerFactory extends Factory
{
    protected $model = Customer::class;

    public function definition()
    {
        return [
            'user_id' => User::factory()->asCustomer(),
            'points' => $this->faker->numberBetween(0, 100),
            'refer_code' => Str::random(6),
        ];
    }
}
