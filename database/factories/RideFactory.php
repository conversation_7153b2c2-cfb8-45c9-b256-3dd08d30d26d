<?php

namespace Database\Factories;

use App\Enums\RideStatusEnum;
use App\Enums\UserGenderEnum;
use App\Models\Coupon;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\Pricing;
use App\Models\Ride;
use Illuminate\Database\Eloquent\Factories\Factory;

class RideFactory extends Factory
{
    protected $model = Ride::class;

    public function definition()
    {
        return [
            'customer_id' => Customer::factory(),
            'driver_id' => null,
            'pricing_id' => Pricing::factory(),
            'coupon_id' => null,
            'status' => RideStatusEnum::PENDING,
            'estimated_distance' => $this->faker->randomFloat(2, 1, 100),
            'actual_distance' => 0,
            'driver_gender' => UserGenderEnum::MALE,
            'estimated_duration' => $this->faker->numberBetween(5, 120),
            'actual_duration' => $this->faker->numberBetween(5, 120),
            'fare' => $this->faker->randomFloat(2, 10, 100),
            'final_price' => $this->faker->randomFloat(2, 10, 150),
            'note' => $this->faker->sentence,
            'cancel_reason' => null,
            'scheduled_at' => null,
            'requested_at' => now(),
            'accepted_at' => null,
            'started_at' => null,
            'completed_at' => null,
            'canceled_at' => null,
            'type' => 'immediate'
        ];
    }

    // State modifiers
    public function withDriver()
    {
        return $this->state([
            'driver_id' => Driver::factory()
        ]);
    }

    public function withCoupon()
    {
        return $this->state([
            'coupon_id' => Coupon::factory()
        ]);
    }

    public function scheduled()
    {
        return $this->state([
            'type' => 'scheduled',
            'scheduled_at' => $this->faker->dateTimeBetween('+1 hour', '+1 week')
        ]);
    }

    public function withStatus(RideStatusEnum $status)
    {
        return $this->state([
            'status' => $status
        ]);
    }

    public function completed()
    {
        return $this->state([
            'status' => RideStatusEnum::COMPLETED,
            'accepted_at' => now()->subMinutes(30),
            'started_at' => now()->subMinutes(25),
            'completed_at' => now()->subMinutes(5),
            'actual_distance' => $this->faker->randomFloat(2, 5, 100),
            'actual_duration' => $this->faker->numberBetween(10, 120)
        ]);
    }

    public function canceled(string $reason = null)
    {
        return $this->state([
            'status' => RideStatusEnum::CANCELED,
            'canceled_at' => now(),
            'cancel_reason' => $reason ?? $this->faker->sentence
        ]);
    }

    public function withRidePoints()
    {
        return $this->afterCreating(function (Ride $ride) {
            $ride->points()->createMany([
                [
                    'type' => 'pickup',
                    'latitude' => 33.503101,
                    'longitude' => 36.316568,
                    'address' => 'address one',
                    'sort_order' => 1,
                ],
                [
                    'type' => 'dropoff',
                    'latitude' => 33.512825,
                    'longitude' => 36.297682,
                    'address' => 'address two',
                    'sort_order' => 2,
                ]
            ]);
        });
    }
}
