<?php

namespace Database\Factories;

use App\Enums\DriverStatusEnum;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DriverFactory extends Factory
{
    protected $model = Driver::class;

    public function definition()
    {
        return [

            'user_id' => User::factory()->asDriver(),
            'is_available' => true,
            'license_number' => $this->faker->numberBetween(0,1000),
            'status' => $this->faker->randomElement(DriverStatusEnum::cases()),
            'refuse_reason' => $this->faker->word,
            'current_latitude' => null,
            'current_longitude' => null,


        ];
    }

    public function withCoordinates($lat, $lng)
    {
        return $this->state([
            'current_latitude' => $lat,
            'current_longitude' => $lng
        ]);
    }

}
