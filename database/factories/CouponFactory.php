<?php

namespace Database\Factories;

use App\Models\Coupon;
use Illuminate\Database\Eloquent\Factories\Factory;

class CouponFactory extends Factory
{
    protected $model = Coupon::class;

    public function definition()
    {
        return [
            'code' => $this->faker->unique()->bothify('COUPON-####'),
            'usage_limit_per_user' => $this->faker->numberBetween(1, 5),
            'start_at' => $this->faker->dateTimeBetween('-1 month', '+1 month'),
            'end_at' => $this->faker->dateTimeBetween('+2 months', '+3 months'),
            'type' => $this->faker->randomElement(['fixed', 'percentage']),
            'value' => $this->faker->randomFloat(2, 5, 100),
        ];
    }

    public function active()
    {
        return $this->state([
            'start_at' => now()->subDay(),
            'end_at' => now()->addWeek(),
        ]);
    }

    public function expired()
    {
        return $this->state([
            'start_at' => now()->subDays(14),
            'end_at' => now()->subDays(7)
        ]);
    }

    public function configure()
    {
        return $this->afterCreating(function (Coupon $coupon) {
            $locales = ['en', 'ar'];

            foreach ($locales as $locale) {
                $coupon->translateOrNew($locale)->name = $this->faker->words(3, true);
                $coupon->translateOrNew($locale)->descrption = $this->faker->sentence(6);
            }

            $coupon->save();
        });
    }
}
