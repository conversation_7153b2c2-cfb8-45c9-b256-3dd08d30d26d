<?php

namespace Database\Factories;

use App\Enums\UserGenderEnum;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [

            'first_name' => $this->faker->word,
            'last_name' => $this->faker->word,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->unique()->phoneNumber(),
            'phone_verified_at' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
            'password' => 'password',
            'userable_type' => Customer::class,
            'userable_id' => null,
            'is_active' => true,
            'birthdate' => $this->faker->dateTime()->format('Y-m-d'),
            'gender' => $this->faker->randomElement(UserGenderEnum::cases()),
        ];
    }

    public function asCustomer()
    {
        return $this->state([
            'userable_type' => Customer::class,
            'userable_id' => null
        ])->afterCreating(function (User $user) {
            $user->userable()->associate(Customer::factory()->create([
                'user_id' => $user->id
            ]))->save();
        });
    }

    public function asDriver()
    {
        return $this->state([
            'userable_type' => Driver::class,
            'userable_id' => null
        ])->afterCreating(function (User $user) {
            $user->userable()->associate(Driver::factory()->create([
                'user_id' => $user->id
            ]))->save();
        });
    }

    public function configure()
    {
        return $this->afterMaking(function (User $user) {
            if (!$user->userable_type) {
                $user->userable_type = Customer::class;
            }
        });
    }
}
