<?php

namespace Database\Factories;

use App\Enums\PaymentMethodEnum;
use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Models\Wallet;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class TransactionFactory extends Factory
{
        // Get existing wallets (company, drivers, customers)
        public function definition()
        {
            // Get existing wallets with fallbacks
            $companyWallet = \App\Models\Wallet::whereHasMorph(
                'walletable', 
                ['App\Models\Company']
            )->firstOr(function () {
                return \App\Models\Wallet::factory()->forCompany()->create();
            });
        
            $driverWallets = \App\Models\Wallet::whereHasMorph(
                'walletable', 
                ['App\Models\Driver']
            )->pluck('id');
        
            $customerWallets = \App\Models\Wallet::whereHasMorph(
                'walletable', 
                ['App\Models\User']
            )->pluck('id');
        
            // If no driver/customer wallets, create some
            if ($driverWallets->isEmpty()) {
                $driverWallets = \App\Models\Wallet::factory()
                    ->count(5)
                    ->forDriver()
                    ->create()
                    ->pluck('id');
            }
        
            if ($customerWallets->isEmpty()) {
                $customerWallets = \App\Models\Wallet::factory()
                    ->count(5)
                    ->forCustomer()
                    ->create()
                    ->pluck('id');
            }

        // Random transaction type setup
        $type = $this->faker->randomElement(TransactionTypeEnum::cases());
        $isCompanyTransaction = $this->faker->boolean(30);
        return [
            'sender_wallet_id' => $isCompanyTransaction 
                ? $companyWallet->id 
                : $this->faker->randomElement($driverWallets->merge($customerWallets)),

            'receiver_wallet_id' => $isCompanyTransaction
                ? $this->faker->randomElement($driverWallets->merge($customerWallets))
                : $companyWallet->id,

            'ride_id' => $this->faker->optional(70)->randomElement(\App\Models\Ride::pluck('id')),
            'coupon_id' => $this->faker->optional(20)->randomElement(\App\Models\Coupon::pluck('id')),
            
            'payment_method' => $this->faker->randomElement(TransactionPaymentMethodEnum::cases()),
            'status' => $this->faker->randomElement(TransactionStatusEnum::cases()),
            'type' => $type,
            
            'reference' => \Illuminate\Support\Str::uuid(),
            'amount' => $this->faker->numberBetween(100, 10000), // in cents
            
            'initiator_type' => 'App\Models\Driver',
            'initiator_id' => 1,
        ];
    }

    private function getPurpose(TransactionTypeEnum $type): string
    {
        return match ($type->value) {
            'fee' => 'ride_commission',
            'bonus' => 'driver_incentive',
            'credit' => $this->faker->randomElement(['wallet_topup', 'refund']),
            'debit' => $this->faker->randomElement(['ride_payment', 'service_fee']),
            default => 'other'
        };
    }

    // State methods for common scenarios
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatusEnum::PENDING
        ]);
    }

    public function success(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatusEnum::SUCCESS
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatusEnum::FAILED
        ]);
    }

    public function credit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => TransactionTypeEnum::CREDIT
        ]);
    }

    public function debit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => TransactionTypeEnum::DEBIT
        ]);
    }
}