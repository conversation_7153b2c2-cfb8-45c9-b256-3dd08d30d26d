<?php

namespace Database\Factories;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShiftFactory extends Factory
{
    protected $model = Shift::class;

    public function definition()
    {
        return [
          
'start_time' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
'end_time' => $this->faker->dateTime()->format('Y-m-d H:i:s'),


        ];
    }
}
