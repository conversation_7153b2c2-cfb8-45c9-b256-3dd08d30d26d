<?php

namespace Database\Factories;

use App\Models\CarType;
use App\Models\Driver;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vehicle>
 */
class VehicleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'driver_id' => Driver::factory(),
            'model' => $this->faker->word,
            'plate_number' => $this->faker->unique()->bothify('???-####'),
            'color' => $this->faker->safeColorName,
            'seats' => $this->faker->numberBetween(2, 8),
            'manufacturing_year' => $this->faker->year,
        ];
    }
}
