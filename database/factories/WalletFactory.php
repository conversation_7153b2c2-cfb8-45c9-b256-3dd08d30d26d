<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Driver;
use Illuminate\Database\Eloquent\Factories\Factory;

class WalletFactory extends Factory
{
    public function definition(): array
    {
        return [
            'balance' => $this->faker->numberBetween(0, 100000), // 0 to 1000.00 in cents
            'is_active' => $this->faker->boolean(true), 
            'walletable_type' => $this->faker->randomElement([
                Driver::class,
                Customer::class,
            ]),
            'walletable_id' => function (array $attributes) {
                return $attributes['walletable_type']::factory()->create()->id;
            },
        ];
    }

   

    public function forDriver(?Driver $driver = null): static
    {
        return $this->state(function (array $attributes) use ($driver) {
            $driver = $driver ?? Driver::factory()->create();
            return [
                'walletable_type' => Driver::class,
                'walletable_id' => $driver->id,
            ];
        });
    }

    public function forCustomer(?Customer $customer = null): static
    {
        return $this->state(function (array $attributes) use ($customer) {
            $customer = $customer ?? Customer::factory()->create();
            return [
                'walletable_type' => Customer::class,
                'walletable_id' => $customer->id,
            ];
        });
    }

    public function withBalance(int $amount): static
    {
        return $this->state(['balance' => $amount]);
    }

    public function inactive(): static
    {
        return $this->state(['is_active' => false]);
    }

    public function forCompany()
{
    return $this->state(function (array $attributes) {
        $company = \App\Models\Company::factory()->create();
        return [
            'walletable_id' => $company->id,
            'walletable_type' => \App\Models\Company::class
        ];
    });
}
}