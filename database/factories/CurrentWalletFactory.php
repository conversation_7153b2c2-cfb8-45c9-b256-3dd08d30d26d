<?php

namespace Database\Factories;

use App\Models\CurrentWallet;
use Illuminate\Database\Eloquent\Factories\Factory;

class CurrentWalletFactory extends Factory
{
    protected $model = CurrentWallet::class;

    public function definition()
    {
        return [
          
'current_lat' => $this->faker->randomFloat(2, 0, 100),
'current_lag' => $this->faker->randomFloat(2, 0, 100),
'is_approved' => $this->faker->numberBetween(1, 100),
'is_available' => $this->faker->numberBetween(1, 100),


        ];
    }
}
