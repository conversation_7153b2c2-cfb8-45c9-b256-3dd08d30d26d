<?php

namespace Database\Factories;

use App\Models\Driver;
use App\Models\DriverLocation;
use App\Models\Ride;
use Illuminate\Database\Eloquent\Factories\Factory;

class DriverLocationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DriverLocation::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'driver_id' => Driver::factory(),
            'ride_id' => null,
            'latitude' => $this->faker->latitude(33.0, 34.0),
            'longitude' => $this->faker->longitude(36.0, 37.0),
            'heading' => $this->faker->numberBetween(0, 359),
            'is_online' => true,
            'address' => $this->faker->address,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the location is associated with a ride.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withRide()
    {
        return $this->state(function (array $attributes) {
            return [
                'ride_id' => Ride::factory()->create([
                    'driver_id' => $attributes['driver_id']
                ])->id,
            ];
        });
    }

    /**
     * Indicate that the driver is offline.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function offline()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_online' => false,
            ];
        });
    }
}
