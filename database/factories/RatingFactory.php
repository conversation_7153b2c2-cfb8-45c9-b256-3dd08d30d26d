<?php

namespace Database\Factories;

use App\Models\Rating;
use Illuminate\Database\Eloquent\Factories\Factory;

class RatingFactory extends Factory
{
    protected $model = Rating::class;

    public function definition()
    {
        return [
          
'user_id' => $this->faker->randomElement(\App\Models\User::pluck('id')->toArray()),
'rating_value' => $this->faker->randomFloat(2, 0, 100),


        ];
    }
}
