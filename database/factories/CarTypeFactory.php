<?php

namespace Database\Factories;

use App\Models\CarType;
use Illuminate\Database\Eloquent\Factories\Factory;

class CarTypeFactory extends Factory
{
    protected $model = CarType::class;

    public function definition()
    {
        return [

            'km_range' => $this->faker->randomFloat(2, 0, 100),

             'en' => ['name' => $this->faker->word, 'description' => $this->faker->sentence],
                'ar' => ['name' => $this->faker->word, 'description' => $this->faker->sentence],
        ];
    }
}
