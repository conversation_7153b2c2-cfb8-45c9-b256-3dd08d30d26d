<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CarType;

class CarTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            [
                'en' => ['name' => 'Classic', 'description' => 'Standard classic car'],
                'ar' => ['name' => 'كلاسيك', 'description' => 'سيارة كلاسيكية قياسية'],
                'km_range' => 10.00
            ],
            [
                'en' => ['name' => 'Comfort', 'description' => 'Air-conditioned comfort car'],
                'ar' => ['name' => 'راحة', 'description' => 'سيارة مريحة مع تكييف'],
                'km_range' => 15.00
            ],
            [
                'en' => ['name' => 'VIP', 'description' => 'Luxury VIP vehicles'],
                'ar' => ['name' => 'VIP', 'description' => 'مركبات فاخرة'],
                'km_range' => 20.00
            ],
            [
                'en' => ['name' => 'Motor', 'description' => 'Motorcycle service'],
                'ar' => ['name' => 'دراجة نارية', 'description' => 'خدمة الدراجات النارية'],
                'km_range' => 5.00
            ],
            [
                'en' => ['name' => 'Normal Taxi', 'description' => 'Regular taxi service'],
                'ar' => ['name' => 'تاكسي عادي', 'description' => 'خدمة تاكسي عادية'],
                'km_range' => 10.00
            ]
        ];

        foreach ($types as $type) {
            CarType::create($type);
        }
    }
}
