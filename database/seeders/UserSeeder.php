<?php

namespace Database\Seeders;

use App\Enums\UserGenderEnum;
use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    public function run()
    {
        User::factory()->count(10)->create();

        User::create([
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'phone' => '96359746845',
            'phone_verified_at' => now(),
            'birthdate' => now(),
            'gender' => UserGenderEnum::MALE,
            'password' => '123456789',
            'userable_type' => 'App\Models\Admin',
            'userable_id' => 1, // Adjust as needed
            'is_active' => true,
        ]);
    }
}
