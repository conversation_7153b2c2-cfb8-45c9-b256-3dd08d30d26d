<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Coupon;

class CouponSeeder extends Seeder
{
    public function run(): void
    {
        // Create active coupons
        Coupon::factory()->count(10)->active()->sequence(
            [
                'code' => 'WELCOME20',
                'type' => 'percentage',
                'value' => 20,
                'usage_limit_per_user' => 1,
                'start_at' => now()->subWeek(),
                'end_at' => now()->addYear(),
            ],
            [
                'code' => 'FREERIDE',
                'type' => 'fixed',
                'value' => 15,
                'usage_limit_per_user' => 2,
                'start_at' => now()->subDays(3),
                'end_at' => now()->addMonth(),
            ]
        )->create();

        // Generic active coupons
        Coupon::factory()->count(8)->active()->create();

        // Create expired coupons
        Coupon::factory()->count(5)->expired()->create();

        // Special limited coupon
        Coupon::create([
            'code' => 'VIP25',
            'type' => 'percentage',
            'value' => 25,
            'usage_limit_per_user' => 3,
            'start_at' => now()->subMonth(),
            'end_at' => now()->addMonth(),
        ]);
    }
}
