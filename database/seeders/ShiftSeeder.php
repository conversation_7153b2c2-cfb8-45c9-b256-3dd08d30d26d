<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Shift;

class ShiftSeeder extends Seeder
{
    public function run()
    {
        $shifts = [
            [
                'ar' => ['name' => 'التسعيرة الصباحية'],
                'en' => ['name' => 'Morning Shift'],
                'start_time' => '06:00:00',
                'end_time' => '12:00:00',
            ],
            [
                'ar' => ['name' => ' فترة الذروة 12 ظهرا حتى 6 مساء'],
                'en' => ['name' => 'Afternoon Shift'],
                'start_time' => '12:00:00',
                'end_time' => '18:00:00',
            ],
            [
                'ar' => ['name' => 'التسعيرة المسائية'],
                'en' => ['name' => 'Evening Shift'],
                'start_time' => '18:00:00',
                'end_time' => '00:00:00',
            ],
            [
                'ar' => ['name' => 'التسعيرة الليلة'],
                'en' => ['name' => 'Night Shift'],
                'start_time' => '22:00:00',
                'end_time' => '06:00:00',
            ]
        ];

        foreach ($shifts as $shift) {
            Shift::create($shift);
        }
    }
}
