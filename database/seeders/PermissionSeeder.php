<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Roles management
            [
                'name' => 'list-roles',
                'group' => 'manage-role',
            ],
            [
                'name' => 'update-role',
                'group' => 'manage-role',
            ],
            [
                'name' => 'create-role',
                'group' => 'manage-role',
            ],
            [
                'name' => 'delete-role',
                'group' => 'manage-role',
            ],

            // Admin management
            [
                'name' => 'list-admins',
                'group' => 'manage-admin',
            ],
            [
                'name' => 'update-admin',
                'group' => 'manage-admin',
            ],
            [
                'name' => 'create-admin',
                'group' => 'manage-admin',
            ],
            [
                'name' => 'delete-admin',
                'group' => 'manage-admin',
            ],

            // Region management
            [
                'name' => 'list-regions',
                'group' => 'manage-region',
            ],
            [
                'name' => 'update-region',
                'group' => 'manage-region',
            ],
            [
                'name' => 'create-region',
                'group' => 'manage-region',
            ],
            [
                'name' => 'delete-region',
                'group' => 'manage-region',
            ],

            // City management
            [
                'name' => 'list-cities',
                'group' => 'manage-cities',
            ],
            [
                'name' => 'update-city',
                'group' => 'manage-cities',
            ],
            [
                'name' => 'create-city',
                'group' => 'manage-cities',
            ],
            [
                'name' => 'delete-city',
                'group' => 'manage-cities',
            ],

            // Car management
            [
                'name' => 'list-cars',
                'group' => 'manage-cars',
            ],
            [
                'name' => 'create-car',
                'group' => 'manage-cars',
            ],
            [
                'name' => 'update-car',
                'group' => 'manage-cars',
            ],
            [
                'name' => 'delete-car',
                'group' => 'manage-cars',
            ],

            // Driver management
            [
                'name' => 'list-drivers',
                'group' => 'manage-driver',
            ],
          
            [
                'name' => 'create-driver',
                'group' => 'manage-driver',
            ],
            [
                'name' => 'update-driver',
                'group' => 'manage-driver',
            ],
            [
                'name' => 'delete-driver',
                'group' => 'manage-driver',
            ],
            [
                'name' => 'approve-driver',
                'group' => 'manage-driver',
            ],

            // Shift management
            [
                'name' => 'list-shifts',
                'group' => 'manage-shifts',
            ],
            [
                'name' => 'create-shift',
                'group' => 'manage-shifts',
            ],
            [
                'name' => 'update-shift',
                'group' => 'manage-shifts',
            ],
            [
                'name' => 'delete-shift',
                'group' => 'manage-shifts',
            ],

            // Pricing management
            [
                'name' => 'list-pricings',
                'group' => 'manage-pricings',
            ],
            [
                'name' => 'create-pricing',
                'group' => 'manage-pricings',
            ],
            [
                'name' => 'update-pricing',
                'group' => 'manage-pricings',
            ],
            [
                'name' => 'delete-pricing',
                'group' => 'manage-pricings',
            ],

            // Ride management
            [
                'name' => 'list-rides',
                'group' => 'manage-rides',
            ],
            [
                'name' => 'update-ride',
                'group' => 'manage-rides',
            ],
            [
                'name' => 'delete-ride',
                'group' => 'manage-rides',
            ],

            // Wallet management
            [
                'name' => 'list-wallet',
                'group' => 'manage-wallet',
            ],
            [
                'name' => 'update-wallet',
                'group' => 'manage-wallet',
            ],
            [
                'name' => 'delete-wallet',
                'group' => 'manage-wallet',
            ],

            // Transaction management
            [
                'name' => 'list-transactions',
                'group' => 'manage-transactions',
            ],
            [
                'name' => 'create-transaction',
                'group' => 'manage-transactions',
            ],
            [
                'name' => 'update-transaction',
                'group' => 'manage-transactions',
            ],
            [
                'name' => 'delete-transaction',
                'group' => 'manage-transactions',
            ],

            // User management
            [
                'name' => 'list-users',
                'group' => 'manage-users',
            ],
            [
                'name' => 'list-user', // Singular version for consistency
                'group' => 'manage-users',
            ],
            [
                'name' => 'create-user', // Changed to singular
                'group' => 'manage-users',
            ],
            [
                'name' => 'update-user', // Changed to singular
                'group' => 'manage-users',
            ],
            [
                'name' => 'delete-user', // Already singular
                'group' => 'manage-users',
            ],

            // Customer management
            [
                'name' => 'list-customers',
                'group' => 'manage-customers',
            ],
            [
                'name' => 'create-customer',
                'group' => 'manage-customers',
            ],
            [
                'name' => 'update-customer',
                'group' => 'manage-customers',
            ],
            [
                'name' => 'delete-customer',
                'group' => 'manage-customers',
            ],

            // Provider management
            [
                'name' => 'list-providers',
                'group' => 'manage-providers',
            ],
            [
                'name' => 'create-provider',
                'group' => 'manage-providers',
            ],
            [
                'name' => 'update-provider',
                'group' => 'manage-providers',
            ],
            [
                'name' => 'delete-provider',
                'group' => 'manage-providers',
            ],

            // Car type management
            [
                'name' => 'list-car_types',
                'group' => 'manage-car_types',
            ],
            [
                'name' => 'create-car_type',
                'group' => 'manage-car_types',
            ],
            [
                'name' => 'update-car_type',
                'group' => 'manage-car_types',
            ],
            [
                'name' => 'delete-car_type',
                'group' => 'manage-car_types',
            ],

            // Statistics
            [
                'name' => 'list-statistics',
                'group' => 'manage-statistics',
            ],

            // Notification management
            [
                'name' => 'send-notification',
                'group' => 'manage-notifications',
            ],
            [
                'name' => 'list-notifications',
                'group' => 'manage-notifications',
            ],
            [
                'name' => 'create-notification',
                'group' => 'manage-notifications',
            ],
            [
                'name' => 'update-notification',
                'group' => 'manage-notifications',
            ],
            [
                'name' => 'delete-notification',
                'group' => 'manage-notifications',
            ],
            [
                'name' => 'list-coupons',
                'group' => 'manage-coupons',
            ],
            [
                'name' => 'create-coupons',
                'group' => 'manage-coupons',
            ],
            [
                'name' => 'update-coupons',
                'group' => 'manage-coupons',
            ],
            [
                'name' => 'delete-coupons',
                'group' => 'manage-coupons',
            ],
            [
                'name' => 'list-coupon',
                'group' => 'manage-coupons',
            ],

            // Settings management
            [
                'name' => 'edit-settings',
                'group' => 'manage-settings',
            ],
            [
                'name' => 'list-settings',
                'group' => 'manage-settings',
            ],
        ];
        Permission::query()->delete();
        foreach ($permissions as $permission) {
            $permission = Permission::query()->updateOrCreate(['name' => $permission['name']], $permission);
        }
        // Get all permissions
        $allPermissions = Permission::pluck('id')->toArray();

        // Assign all permissions to admin role
        Role::query()->where('name', 'super-admin')->first()->syncPermissions($allPermissions);

        // Get only the service provider and orders permissions
        // $supervisorPermissions = Permission::whereIn('group', ['manage-service-providers', 'manage-orders'])->pluck('id')->toArray();
    }
}
