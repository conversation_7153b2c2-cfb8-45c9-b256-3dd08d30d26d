<?php

namespace Database\Seeders;

use App\Enums\SettingEnum;
use App\Models\Setting;
use App\Enums\SettingKeyEnum;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run()
    {
        Setting::query()->where('key', 'driver_commission')->delete();
        foreach (SettingEnum::cases() as $setting) {
            Setting::query()->updateOrCreate([
                'key' => $setting->value,
            ], [
                'value' => in_array($setting->types(), ['tel', 'number']) ? 10 : 'https://www.google.com',
            ]);
        }
    }
}
