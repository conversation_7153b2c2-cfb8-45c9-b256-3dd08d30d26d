<?php

namespace Database\Seeders;

use App\Enums\SettingEnum;
use App\Models\Setting;
use App\Enums\SettingKeyEnum;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run()
    {
        Setting::query()->where('key', 'driver_commission')->delete();
        foreach (SettingEnum::cases() as $setting) {
            $type = SettingEnum::getType($setting->value);
            $defaultValue = $this->getDefaultValue($setting->value, $type);

            Setting::query()->updateOrCreate([
                'key' => $setting->value,
            ], [
                'value' => $defaultValue,
            ]);
        }
    }

    private function getDefaultValue($key, $type)
    {
        if (in_array($type, ['tel', 'number'])) {
            return 10;
        }

        if ($type === 'checkbox') {
            return 0;
        }

        if (str_contains($key, 'version')) {
            return '1.0.0';
        }

        return 'https://www.google.com';
    }
}
