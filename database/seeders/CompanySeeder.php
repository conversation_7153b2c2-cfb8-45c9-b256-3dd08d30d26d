<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Wallet;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $company = Company::firstOrCreate(['is_main' => true],
            ['name' => config('app.name')],
            ['is_main' => true]
        );

        // Create main wallet if doesn't exist
        if (!$company->wallet) {
            Wallet::create([
                'walletable_id' => $company->id,
                'walletable_type' => Company::class,
                'balance' => 0,
                'is_active' => true
            ]);
        }
    }
}