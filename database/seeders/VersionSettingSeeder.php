<?php

namespace Database\Seeders;

use App\Enums\SettingEnum;
use App\Models\Setting;
use Illuminate\Database\Seeder;

class VersionSettingSeeder extends Seeder
{
    public function run()
    {
        $versionSettings = [
            SettingEnum::DRIVER_ANDROID_CURRENT_VERSION->value => '1.0.0',
            SettingEnum::DRIVER_ANDROID_MINIMAL_VERSION->value => '1.0.0',
            SettingEnum::DRIVER_ANDROID_FORCE_UPDATE->value => '0',
            
            SettingEnum::DRIVER_IOS_CURRENT_VERSION->value => '1.0.0',
            SettingEnum::DRIVER_IOS_MINIMAL_VERSION->value => '1.0.0',
            SettingEnum::DRIVER_IOS_FORCE_UPDATE->value => '0',
            
            SettingEnum::USER_ANDROID_CURRENT_VERSION->value => '1.0.0',
            SettingEnum::USER_ANDROID_MINIMAL_VERSION->value => '1.0.0',
            SettingEnum::USER_ANDROID_FORCE_UPDATE->value => '0',
            
            SettingEnum::USER_IOS_CURRENT_VERSION->value => '1.0.0',
            SettingEnum::USER_IOS_MINIMAL_VERSION->value => '1.0.0',
            SettingEnum::USER_IOS_FORCE_UPDATE->value => '0',
        ];

        foreach ($versionSettings as $key => $value) {
            Setting::query()->updateOrCreate([
                'key' => $key,
            ], [
                'value' => $value,
            ]);
        }
    }
}
