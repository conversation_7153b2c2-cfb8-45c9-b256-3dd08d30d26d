<?php

namespace Database\Seeders;

use App\Models\CarType;
use Illuminate\Database\Seeder;
use App\Models\Pricing;
use App\Models\Shift;

class PricingSeeder extends Seeder
{
    public function run()
    {
        $shifts = Shift::with('translations')->get();
        $carTypes = CarType::all();

        foreach ($shifts as $shift) {
            $shiftName = $shift->name;
            
            foreach ($carTypes as $carType) {
                Pricing::create([
                    'shift_id' => $shift->id,
                    'car_type_id' => $carType->id,
                    'minimum_fare' => $this->getBasePrice($carType->name, $shiftName, 'minimum'),
                    'flag_down_fee' => $this->getBasePrice($carType->name, $shiftName, 'flag_down'),
                    'km_price' => $this->getBasePrice($carType->name, $shiftName, 'km'),
                    'miu_price' => $this->getBasePrice($carType->name, $shiftName, 'miu'),
                ]);
            }
        }
    }

    private function getBasePrice(string $type, string $shiftName, string $field): float
    {
        $prices = [
            'Classic' => [
                'Morning Shift' => [
                    'minimum' => 14000,
                    'flag_down' => 3376,
                    'km' => 2500,
                    'miu' => 500
                ],
                'Afternoon Shift' => [
                    'minimum' => 16000,
                    'flag_down' => 5500,
                    'km' => 2700,
                    'miu' => 550
                ],
                'Evening Shift' => [
                    'minimum' => 17000,
                    'flag_down' => 6000,
                    'km' => 3000,
                    'miu' => 600
                ],
                'Night Shift' => [
                    'minimum' => 18000,
                    'flag_down' => 6500,
                    'km' => 3300,
                    'miu' => 650
                ]
            ],
            'Comfort' => [
                'Morning Shift' => [
                    'minimum' => 17000,
                    'flag_down' => 7000,
                    'km' => 3000,
                    'miu' => 600
                ],
                'Afternoon Shift' => [
                    'minimum' => 18000,
                    'flag_down' => 7500,
                    'km' => 3200,
                    'miu' => 650
                ],
                'Evening Shift' => [
                    'minimum' => 20000,
                    'flag_down' => 8000,
                    'km' => 3500,
                    'miu' => 700
                ],
                'Night Shift' => [
                    'minimum' => 22000,
                    'flag_down' => 8500,
                    'km' => 3800,
                    'miu' => 750
                ]
            ],
            'VIP' => [
                'Morning Shift' => [
                    'minimum' => 17000,
                    'flag_down' => 7000,
                    'km' => 3000,
                    'miu' => 600
                ],
                'Afternoon Shift' => [
                    'minimum' => 18000,
                    'flag_down' => 7500,
                    'km' => 3200,
                    'miu' => 650
                ],
                'Evening Shift' => [
                    'minimum' => 20000,
                    'flag_down' => 8000,
                    'km' => 3500,
                    'miu' => 700
                ],
                'Night Shift' => [
                    'minimum' => 22000,
                    'flag_down' => 8500,
                    'km' => 3800,
                    'miu' => 750
                ]
            ],
            
            'Motor' => [
                'Morning Shift' => [
                    'minimum' => 17000,
                    'flag_down' => 7000,
                    'km' => 3000,
                    'miu' => 600
                ],
                'Afternoon Shift' => [
                    'minimum' => 18000,
                    'flag_down' => 7500,
                    'km' => 3200,
                    'miu' => 650
                ],
                'Evening Shift' => [
                    'minimum' => 20000,
                    'flag_down' => 8000,
                    'km' => 3500,
                    'miu' => 700
                ],
                'Night Shift' => [
                    'minimum' => 22000,
                    'flag_down' => 8500,
                    'km' => 3800,
                    'miu' => 750
                ]
            ],
            'Normal Taxi' => [
                'Morning Shift' => [
                    'minimum' => 17000,
                    'flag_down' => 7000,
                    'km' => 3000,
                    'miu' => 600
                ],
                'Afternoon Shift' => [
                    'minimum' => 18000,
                    'flag_down' => 7500,
                    'km' => 3200,
                    'miu' => 650
                ],
                'Evening Shift' => [
                    'minimum' => 20000,
                    'flag_down' => 8000,
                    'km' => 3500,
                    'miu' => 700
                ],
                'Night Shift' => [
                    'minimum' => 22000,
                    'flag_down' => 8500,
                    'km' => 3800,
                    'miu' => 750
                ]
            ],
            
        ];

        return $prices[$type][$shiftName][$field] ?? 0.00;
    }
}
