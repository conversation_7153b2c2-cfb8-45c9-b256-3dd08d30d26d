<?php

namespace Database\Seeders;

use App\Models\Company;
use Illuminate\Database\Seeder;
use App\Models\Transaction;

class TransactionSeeder extends Seeder
{
    public function run()
    {
        // Create company wallet if not exists
        $company = Company::create([
            'name' => 'Ride Share Inc',
            'descrption' => 'Ride Share Inc',
            'is_main' => 1,
        ]
        );

        $company->wallet()->create([
            'balance' => 0,
            'is_active' => true
        ]);

        // Create 200 transactions
        Transaction::factory()
            ->count(20)
            ->create();
    }
}
