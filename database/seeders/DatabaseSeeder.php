<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        User::query()->doesntHave('userable')->delete();
        // $this->call([
        //     UserSeeder::class,
        //     RoleSeeder::class,
        //     PermissionSeeder::class,
        //     AdminSeeder::class,
        //     // RegionSeeder::class,
        //     // CarTypeSeeder::class,
        //     // ShiftSeeder::class,
        //     // PricingSeeder::class,
        //     // DriverSeeder::class,
        //     // CompanySeeder::class,
        //     // CouponSeeder::class,
        //     SettingSeeder::class
        // ]);
    }
}
