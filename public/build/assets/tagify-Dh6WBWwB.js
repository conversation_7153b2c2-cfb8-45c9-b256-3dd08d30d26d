import{c as it,g as at}from"./_commonjsHelpers-BosuxZz1.js";var G={exports:{}};(function(B,rt){(function(V,M){B.exports=M()})(it,function(){function V(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),s.push.apply(s,i)}return s}function M(t){for(var e=1;e<arguments.length;e++){var s=arguments[e]!=null?arguments[e]:{};e%2?V(Object(s),!0).forEach(function(i){Q(t,i,s[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):V(Object(s)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(s,i))})}return t}function Q(t,e,s){return(e=function(i){var a=function(o,n){if(typeof o!="object"||o===null)return o;var r=o[Symbol.toPrimitive];if(r!==void 0){var l=r.call(o,n);if(typeof l!="object")return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(o)}(i,"string");return typeof a=="symbol"?a:String(a)}(e))in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}const E=(t,e,s,i)=>(t=""+t,e=""+e,i&&(t=t.trim(),e=e.trim()),s?t==e:t.toLowerCase()==e.toLowerCase()),j=(t,e)=>t&&Array.isArray(t)&&t.map(s=>P(s,e));function P(t,e){var s,i={};for(s in t)e.indexOf(s)<0&&(i[s]=t[s]);return i}function $(t){var e=document.createElement("div");return t.replace(/\&#?[0-9a-z]+;/gi,function(s){return e.innerHTML=s,e.innerText})}function R(t){return new DOMParser().parseFromString(t.trim(),"text/html").body.firstElementChild}function W(t,e){for(e=e||"previous";t=t[e+"Sibling"];)if(t.nodeType==3)return t}function I(t){return typeof t=="string"?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/`|'/g,"&#039;"):t}function O(t){var e=Object.prototype.toString.call(t).split(" ")[1].slice(0,-1);return t===Object(t)&&e!="Array"&&e!="Function"&&e!="RegExp"&&e!="HTMLUnknownElement"}function T(t,e,s){function i(a,o){for(var n in o)if(o.hasOwnProperty(n)){if(O(o[n])){O(a[n])?i(a[n],o[n]):a[n]=Object.assign({},o[n]);continue}if(Array.isArray(o[n])){a[n]=Object.assign([],o[n]);continue}a[n]=o[n]}}return t instanceof Object||(t={}),i(t,e),s&&i(t,s),t}function q(){const t=[],e={};for(let s of arguments)for(let i of s)O(i)?e[i.value]||(t.push(i),e[i.value]=1):t.includes(i)||t.push(i);return t}function C(t){return String.prototype.normalize?typeof t=="string"?t.normalize("NFD").replace(/[\u0300-\u036f]/g,""):void 0:t}var K=()=>/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent);function z(){return("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,t=>(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16))}function _(t){return t&&t.classList&&t.classList.contains(this.settings.classNames.tag)}function U(t,e){var s=window.getSelection();return e=e||s.getRangeAt(0),typeof t=="string"&&(t=document.createTextNode(t)),e&&(e.deleteContents(),e.insertNode(t)),t}function c(t,e,s){return t?(e&&(t.__tagifyTagData=s?e:T({},t.__tagifyTagData||{},e)),t.__tagifyTagData):(console.warn("tag element doesn't exist",t,e),e)}function D(t){if(t&&t.parentNode){var e=t,s=window.getSelection(),i=s.getRangeAt(0);s.rangeCount&&(i.setStartAfter(e),i.collapse(!0),s.removeAllRanges(),s.addRange(i))}}function X(t,e){t.forEach(s=>{if(c(s.previousSibling)||!s.previousSibling){var i=document.createTextNode("​");s.before(i),e&&D(i)}})}var F={delimiters:",",pattern:null,tagTextProp:"value",maxTags:1/0,callbacks:{},addTagOnBlur:!0,addTagOn:["blur","tab","enter"],onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\.|\:|\s/,mixTagsInterpolator:["[[","]]"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:()=>{},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:" "},autoComplete:{enabled:!0,rightKey:!1,tabKey:!1},classNames:{namespace:"tagify",mixMode:"tagify--mix",selectMode:"tagify--select",input:"tagify__input",focus:"tagify--focus",tagNoAnimation:"tagify--noAnim",tagInvalid:"tagify--invalid",tagNotAllowed:"tagify--notAllowed",scopeLoading:"tagify--loading",hasMaxTags:"tagify--hasMaxTags",hasNoTags:"tagify--noTags",empty:"tagify--empty",inputInvalid:"tagify__input--invalid",dropdown:"tagify__dropdown",dropdownWrapper:"tagify__dropdown__wrapper",dropdownHeader:"tagify__dropdown__header",dropdownFooter:"tagify__dropdown__footer",dropdownItem:"tagify__dropdown__item",dropdownItemActive:"tagify__dropdown__item--active",dropdownItemHidden:"tagify__dropdown__item--hidden",dropdownInital:"tagify__dropdown--initial",tag:"tagify__tag",tagText:"tagify__tag-text",tagX:"tagify__tag__removeBtn",tagLoading:"tagify__tag--loading",tagEditing:"tagify__tag--editable",tagFlash:"tagify__tag--flash",tagHide:"tagify__tag--hide"},dropdown:{classname:"",enabled:2,maxItems:10,searchKeys:["value","searchBy"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,escapeHTML:!0,highlightFirst:!1,closeOnSelect:!0,clearOnSelect:!0,position:"all",appendTarget:null},hooks:{beforeRemoveTag:()=>Promise.resolve(),beforePaste:()=>Promise.resolve(),suggestionClick:()=>Promise.resolve(),beforeKeyDown:()=>Promise.resolve()}};function Y(){this.dropdown={};for(let t in this._dropdown)this.dropdown[t]=typeof this._dropdown[t]=="function"?this._dropdown[t].bind(this):this._dropdown[t];this.dropdown.refs()}var Z={refs(){this.DOM.dropdown=this.parseTemplate("dropdown",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-wrapper']")},getHeaderRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-header']")},getFooterRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-footer']")},getAllSuggestionsRefs(){return[...this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector)]},show(t){var e,s,i,a=this.settings,o=a.mode=="mix"&&!a.enforceWhitelist,n=!a.whitelist||!a.whitelist.length,r=a.dropdown.position=="manual";if(t=t===void 0?this.state.inputText:t,!(n&&!o&&!a.templates.dropdownItemNoMatch||a.dropdown.enable===!1||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(t),t&&!this.suggestedListItems.length&&(this.trigger("dropdown:noMatch",t),a.templates.dropdownItemNoMatch&&(i=a.templates.dropdownItemNoMatch.call(this,{value:t}))),!i){if(this.suggestedListItems.length)t&&o&&!this.state.editing.scope&&!E(this.suggestedListItems[0].value,t)&&this.suggestedListItems.unshift({value:t});else{if(!t||!o||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:t}]}s=""+(O(e=this.suggestedListItems[0])?e.value:e),a.autoComplete&&s&&s.indexOf(t)==0&&this.input.autocomplete.suggest.call(this,e)}this.dropdown.fill(i),a.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(a.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=t||!0,this.state.dropdown.query=t,this.setStateSelection(),r||setTimeout(()=>{this.dropdown.position(),this.dropdown.render()}),setTimeout(()=>{this.trigger("dropdown:show",this.DOM.dropdown)})}},hide(t){var e=this.DOM,s=e.scope,i=e.dropdown,a=this.settings.dropdown.position=="manual"&&!t;if(i&&document.body.contains(i)&&!a)return window.removeEventListener("resize",this.dropdown.position),this.dropdown.events.binding.call(this,!1),s.setAttribute("aria-expanded",!1),i.parentNode.removeChild(i),setTimeout(()=>{this.state.dropdown.visible=!1},100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger("dropdown:hide",i),this},toggle(t){this.dropdown[this.state.dropdown.visible&&!t?"hide":"show"]()},render(){var t,e,s,i=(t=this.DOM.dropdown,(s=t.cloneNode(!0)).style.cssText="position:fixed; top:-9999px; opacity:0",document.body.appendChild(s),e=s.clientHeight,s.parentNode.removeChild(s),e),a=this.settings;return typeof a.dropdown.enabled=="number"&&a.dropdown.enabled>=0?(this.DOM.scope.setAttribute("aria-expanded",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(i),a.dropdown.appendTarget.appendChild(this.DOM.dropdown),setTimeout(()=>this.DOM.dropdown.classList.remove(a.classNames.dropdownInital))),this):this},fill(t){t=typeof t=="string"?t:this.dropdown.createListHTML(t||this.suggestedListItems);var e,s=this.settings.templates.dropdownContent.call(this,t);this.DOM.dropdown.content.innerHTML=(e=s)?e.replace(/\>[\r\n ]+\</g,"><").split(/>\s+</).join("><").trim():""},fillHeaderFooter(){var t=this.dropdown.filterListItems(this.state.dropdown.query),e=this.parseTemplate("dropdownHeader",[t]),s=this.parseTemplate("dropdownFooter",[t]),i=this.dropdown.getHeaderRef(),a=this.dropdown.getFooterRef();e&&(i==null||i.parentNode.replaceChild(e,i)),s&&(a==null||a.parentNode.replaceChild(s,a))},refilter(t){t=t||this.state.dropdown.query||"",this.suggestedListItems=this.dropdown.filterListItems(t),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger("dropdown:updated",this.DOM.dropdown)},position(t){var e=this.settings.dropdown;if(e.position!="manual"){var s,i,a,o,n,r,l,d,g,h=this.DOM.dropdown,f=e.RTL,u=e.appendTarget===document.body,m=u?window.pageYOffset:e.appendTarget.scrollTop,p=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,v=p.clientHeight,y=Math.max(p.clientWidth||0,window.innerWidth||0)>480?e.position:"all",N=this.DOM[y=="input"?"input":"scope"];if(t=t||h.clientHeight,this.state.dropdown.visible){if(y=="text"?(a=(s=function(){const w=document.getSelection();if(w.rangeCount){const b=w.getRangeAt(0),x=b.startContainer,H=b.startOffset;let L,k;if(H>0)return k=document.createRange(),k.setStart(x,H-1),k.setEnd(x,H),L=k.getBoundingClientRect(),{left:L.right,top:L.top,bottom:L.bottom};if(x.getBoundingClientRect)return x.getBoundingClientRect()}return{left:-9999,top:-9999}}()).bottom,i=s.top,o=s.left,n="auto"):(r=function(w){for(var b=0,x=0;w&&w!=p;)b+=w.offsetTop||0,x+=w.offsetLeft||0,w=w.parentNode;return{top:b,left:x}}(e.appendTarget),i=(s=N.getBoundingClientRect()).top-r.top,a=s.bottom-1-r.top,o=s.left-r.left,n=s.width+"px"),!u){let w=function(){for(var b=0,x=e.appendTarget.parentNode;x;)b+=x.scrollTop||0,x=x.parentNode;return b}();i+=w,a+=w}i=Math.floor(i),a=Math.ceil(a),d=((l=e.placeAbove??v-s.bottom<t)?i:a)+m,g=`left: ${o+(f&&s.width||0)+window.pageXOffset}px;`,h.style.cssText=`${g}; top: ${d}px; min-width: ${n}; max-width: ${n}`,h.setAttribute("placement",l?"top":"bottom"),h.setAttribute("position",y)}}},events:{binding(){let t=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];var e=this.dropdown.events.callbacks,s=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:e.onKeyDown.bind(this),onMouseOver:e.onMouseOver.bind(this),onMouseLeave:e.onMouseLeave.bind(this),onClick:e.onClick.bind(this),onScroll:e.onScroll.bind(this)},i=t?"addEventListener":"removeEventListener";this.settings.dropdown.position!="manual"&&(document[i]("scroll",s.position,!0),window[i]("resize",s.position),window[i]("keydown",s.onKeyDown)),this.DOM.dropdown[i]("mouseover",s.onMouseOver),this.DOM.dropdown[i]("mouseleave",s.onMouseLeave),this.DOM.dropdown[i]("mousedown",s.onClick),this.DOM.dropdown.content[i]("scroll",s.onScroll)},callbacks:{onKeyDown(t){if(this.state.hasFocus&&!this.state.composing){var e=this.settings,s=this.DOM.dropdown.querySelector(e.classNames.dropdownItemActiveSelector),i=this.dropdown.getSuggestionDataByNode(s),a=e.mode=="mix";e.hooks.beforeKeyDown(t,{tagify:this}).then(o=>{switch(t.key){case"ArrowDown":case"ArrowUp":case"Down":case"Up":t.preventDefault();var n=this.dropdown.getAllSuggestionsRefs(),r=t.key=="ArrowUp"||t.key=="Up";s&&(s=this.dropdown.getNextOrPrevOption(s,!r)),s&&s.matches(e.classNames.dropdownItemSelector)||(s=n[r?n.length-1:0]),this.dropdown.highlightOption(s,!0);break;case"Escape":case"Esc":this.dropdown.hide();break;case"ArrowRight":if(this.state.actions.ArrowLeft)return;case"Tab":{let d=!e.autoComplete.rightKey||!e.autoComplete.tabKey;if(!a&&s&&d&&!this.state.editing){t.preventDefault();var l=this.dropdown.getMappedValue(i);return this.input.autocomplete.set.call(this,l),!1}return!0}case"Enter":t.preventDefault(),e.hooks.suggestionClick(t,{tagify:this,tagData:i,suggestionElm:s}).then(()=>{if(s)return this.dropdown.selectOption(s),s=this.dropdown.getNextOrPrevOption(s,!r),void this.dropdown.highlightOption(s);this.dropdown.hide(),a||this.addTags(this.state.inputText.trim(),!0)}).catch(d=>d);break;case"Backspace":{if(a||this.state.editing.scope)return;const d=this.input.raw.call(this);d!=""&&d.charCodeAt(0)!=8203||(e.backspace===!0?this.removeTags():e.backspace=="edit"&&setTimeout(this.editTag.bind(this),0))}}})}},onMouseOver(t){var e=t.target.closest(this.settings.classNames.dropdownItemSelector);this.dropdown.highlightOption(e)},onMouseLeave(t){this.dropdown.highlightOption()},onClick(t){if(t.button==0&&t.target!=this.DOM.dropdown&&t.target!=this.DOM.dropdown.content){var e=t.target.closest(this.settings.classNames.dropdownItemSelector),s=this.dropdown.getSuggestionDataByNode(e);this.state.actions.selectOption=!0,setTimeout(()=>this.state.actions.selectOption=!1,50),this.settings.hooks.suggestionClick(t,{tagify:this,tagData:s,suggestionElm:e}).then(()=>{e?this.dropdown.selectOption(e,t):this.dropdown.hide()}).catch(i=>console.warn(i))}},onScroll(t){var e=t.target,s=e.scrollTop/(e.scrollHeight-e.parentNode.clientHeight)*100;this.trigger("dropdown:scroll",{percentage:Math.round(s)})}}},getSuggestionDataByNode(t){var e=t&&t.getAttribute("value");return this.suggestedListItems.find(s=>s.value==e)||null},getNextOrPrevOption(t){let e=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];var s=this.dropdown.getAllSuggestionsRefs(),i=s.findIndex(a=>a===t);return e?s[i+1]:s[i-1]},highlightOption(t,e){var s,i=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(i),this.state.ddItemElm.removeAttribute("aria-selected")),!t)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);s=this.dropdown.getSuggestionDataByNode(t),this.state.ddItemData=s,this.state.ddItemElm=t,t.classList.add(i),t.setAttribute("aria-selected",!0),e&&(t.parentNode.scrollTop=t.clientHeight+t.offsetTop-t.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,s),this.dropdown.position())},selectOption(t,e){var s=this.settings,i=s.dropdown,a=i.clearOnSelect,o=i.closeOnSelect;if(!t)return this.addTags(this.state.inputText,!0),void(o&&this.dropdown.hide());e=e||{};var n=t.getAttribute("value"),r=n=="noMatch",l=this.suggestedListItems.find(d=>(d.value??d)==n);if(this.trigger("dropdown:select",{data:l,elm:t,event:e}),n&&(l||r)){if(this.state.editing){let d=this.normalizeTags([l])[0];l=s.transformTag.call(this,d)||d,this.onEditTagDone(null,T({__isValid:!0},l))}else this[s.mode=="mix"?"addMixTags":"addTags"]([l||this.input.raw.call(this)],a);this.DOM.input.parentNode&&(setTimeout(()=>{this.DOM.input.focus(),this.toggleFocusClass(!0)}),o&&setTimeout(this.dropdown.hide.bind(this)),t.addEventListener("transitionend",()=>{this.dropdown.fillHeaderFooter(),setTimeout(()=>t.remove(),100)},{once:!0}),t.classList.add(this.settings.classNames.dropdownItemHidden))}else o&&setTimeout(this.dropdown.hide.bind(this))},selectAll(t){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems("");var e=this.dropdown.filterListItems("");return t||(e=this.state.dropdown.suggestions),this.addTags(e,!0),this},filterListItems(t,e){var s,i,a,o,n,r=this.settings,l=r.dropdown,d=(e=e||{},[]),g=[],h=r.whitelist,f=l.maxItems>=0?l.maxItems:1/0,u=l.searchKeys,m=0;if(!(t=r.mode=="select"&&this.value.length&&this.value[0][r.tagTextProp]==t?"":t)||!u.length)return d=l.includeSelectedTags?h:h.filter(v=>!this.isTagDuplicate(O(v)?v.value:v)),this.state.dropdown.suggestions=d,d.slice(0,f);function p(v,y){return y.toLowerCase().split(" ").every(N=>v.includes(N.toLowerCase()))}for(n=l.caseSensitive?""+t:(""+t).toLowerCase();m<h.length;m++){let v,y;s=h[m]instanceof Object?h[m]:{value:h[m]};let N=Object.keys(s).some(w=>u.includes(w))?u:["value"];l.fuzzySearch&&!e.exact?(a=N.reduce((w,b)=>w+" "+(s[b]||""),"").toLowerCase().trim(),l.accentedSearch&&(a=C(a),n=C(n)),v=a.indexOf(n)==0,y=a===n,i=p(a,n)):(v=!0,i=N.some(w=>{var b=""+(s[w]||"");return l.accentedSearch&&(b=C(b),n=C(n)),l.caseSensitive||(b=b.toLowerCase()),y=b===n,e.exact?b===n:b.indexOf(n)==0})),o=!l.includeSelectedTags&&this.isTagDuplicate(O(s)?s.value:s),i&&!o&&(y&&v?g.push(s):l.sortby=="startsWith"&&v?d.unshift(s):d.push(s))}return this.state.dropdown.suggestions=g.concat(d),typeof l.sortby=="function"?l.sortby(g.concat(d),n):g.concat(d).slice(0,f)},getMappedValue(t){var e=this.settings.dropdown.mapValueTo;return e?typeof e=="function"?e(t):t[e]||t.value:t.value},createListHTML(t){return T([],t).map((e,s)=>{typeof e!="string"&&typeof e!="number"||(e={value:e});var i=this.dropdown.getMappedValue(e);return i=typeof i=="string"&&this.settings.dropdown.escapeHTML?I(i):i,this.settings.templates.dropdownItem.apply(this,[M(M({},e),{},{mappedValue:i}),this])}).join("")}};const S="@yaireo/tagify/";var J,tt={empty:"empty",exceed:"number of tags exceeded",pattern:"pattern mismatch",duplicate:"already exists",notAllowed:"not allowed"},et={wrapper:(t,e)=>`<tags class="${e.classNames.namespace} ${e.mode?`${e.classNames[e.mode+"Mode"]}`:""} ${t.className}"
                    ${e.readonly?"readonly":""}
                    ${e.disabled?"disabled":""}
                    ${e.required?"required":""}
                    ${e.mode==="select"?"spellcheck='false'":""}
                    tabIndex="-1">
            <span ${!e.readonly&&e.userInput?"contenteditable":""} tabIndex="0" data-placeholder="${e.placeholder||"&#8203;"}" aria-placeholder="${e.placeholder||""}"
                class="${e.classNames.input}"
                role="textbox"
                aria-autocomplete="both"
                aria-multiline="${e.mode=="mix"}"></span>
                &#8203;
        </tags>`,tag(t,e){let s=e.settings;return`<tag title="${t.title||t.value}"
                    contenteditable='false'
                    spellcheck='false'
                    tabIndex="${s.a11y.focusableTags?0:-1}"
                    class="${s.classNames.tag} ${t.class||""}"
                    ${this.getAttributes(t)}>
            <x title='' class="${s.classNames.tagX}" role='button' aria-label='remove tag'></x>
            <div>
                <span class="${s.classNames.tagText}">${t[s.tagTextProp]||t.value}</span>
            </div>
        </tag>`},dropdown(t){var e=t.dropdown;return`<div class="${e.position=="manual"?"":t.classNames.dropdown} ${e.classname}" role="listbox" aria-labelledby="dropdown" dir="${e.RTL?"rtl":""}">
                    <div data-selector='tagify-suggestions-wrapper' class="${t.classNames.dropdownWrapper}"></div>
                </div>`},dropdownContent(t){var e=this.settings.templates,s=this.state.dropdown.suggestions;return`
            ${e.dropdownHeader.call(this,s)}
            ${t}
            ${e.dropdownFooter.call(this,s)}
        `},dropdownItem(t){return`<div ${this.getAttributes(t)}
                    class='${this.settings.classNames.dropdownItem} ${t.class||""}'
                    tabindex="0"
                    role="option">${t.mappedValue||t.value}</div>`},dropdownHeader(t){return`<header data-selector='tagify-suggestions-header' class="${this.settings.classNames.dropdownHeader}"></header>`},dropdownFooter(t){var e=t.length-this.settings.dropdown.maxItems;return e>0?`<footer data-selector='tagify-suggestions-footer' class="${this.settings.classNames.dropdownFooter}">
                ${e} more items. Refine your search.
            </footer>`:""},dropdownItemNoMatch:null},st={customBinding(){this.customEventsList.forEach(t=>{this.on(t,this.settings.callbacks[t])})},binding(){let t=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];var e,s=this.events.callbacks,i=t?"addEventListener":"removeEventListener";if(!this.state.mainEvents||!t){for(var a in this.state.mainEvents=t,t&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on("tagify.removeAllTags",this.removeAllTags.bind(this))),e=this.listeners.main=this.listeners.main||{focus:["input",s.onFocusBlur.bind(this)],keydown:["input",s.onKeydown.bind(this)],click:["scope",s.onClickScope.bind(this)],dblclick:["scope",s.onDoubleClickScope.bind(this)],paste:["input",s.onPaste.bind(this)],drop:["input",s.onDrop.bind(this)],compositionstart:["input",s.onCompositionStart.bind(this)],compositionend:["input",s.onCompositionEnd.bind(this)]})this.DOM[e[a][0]][i](a,e[a][1]);clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(s.observeOriginalInputValue.bind(this),500);var o=this.listeners.main.inputMutationObserver||new MutationObserver(s.onInputDOMChange.bind(this));o.disconnect(),this.settings.mode=="mix"&&o.observe(this.DOM.input,{childList:!0})}},bindGlobal(t){var e,s=this.events.callbacks,i=t?"removeEventListener":"addEventListener";if(this.listeners&&(t||!this.listeners.global))for(e of(this.listeners.global=this.listeners.global||[{type:this.isIE?"keydown":"input",target:this.DOM.input,cb:s[this.isIE?"onInputIE":"onInput"].bind(this)},{type:"keydown",target:window,cb:s.onWindowKeyDown.bind(this)},{type:"blur",target:this.DOM.input,cb:s.onFocusBlur.bind(this)},{type:"click",target:document,cb:s.onClickAnywhere.bind(this)}],this.listeners.global))e.target[i](e.type,e.cb)},unbindGlobal(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur(t){var g,h;var e=this.settings,s=t.target?this.trim(t.target.textContent):"",i=(h=(g=this.value)==null?void 0:g[0])==null?void 0:h[e.tagTextProp],a=t.type,o=e.dropdown.enabled>=0,n={relatedTarget:t.relatedTarget},r=this.state.actions.selectOption&&(o||!e.dropdown.closeOnSelect),l=this.state.actions.addNew&&o,d=t.relatedTarget&&_.call(this,t.relatedTarget)&&this.DOM.scope.contains(t.relatedTarget);if(a=="blur"){if(t.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),e.onChangeAfterBlur&&this.triggerChangeEvent()}if(!r&&!l)if(this.state.hasFocus=a=="focus"&&+new Date,this.toggleFocusClass(this.state.hasFocus),e.mode!="mix"){if(a=="focus")return this.trigger("focus",n),void(e.dropdown.enabled!==0&&e.userInput||this.dropdown.show(this.value.length?"":void 0));a=="blur"&&(this.trigger("blur",n),this.loading(!1),e.mode=="select"&&(d&&(this.removeTags(),s=""),i===s&&(s="")),s&&!this.state.actions.selectOption&&e.addTagOnBlur&&e.addTagOn.includes("blur")&&this.addTags(s,!0)),this.DOM.input.removeAttribute("style"),this.dropdown.hide()}else a=="focus"?this.trigger("focus",n):t.type=="blur"&&(this.trigger("blur",n),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart(t){this.state.composing=!0},onCompositionEnd(t){this.state.composing=!1},onWindowKeyDown(t){var e,s=document.activeElement,i=_.call(this,s)&&this.DOM.scope.contains(document.activeElement),a=i&&s.hasAttribute("readonly");if(i&&!a)switch(e=s.nextElementSibling,t.key){case"Backspace":this.settings.readonly||(this.removeTags(s),(e||this.DOM.input).focus());break;case"Enter":setTimeout(this.editTag.bind(this),0,s)}},onKeydown(t){var e=this.settings;if(!this.state.composing&&e.userInput){e.mode=="select"&&e.enforceWhitelist&&this.value.length&&t.key!="Tab"&&t.preventDefault();var s=this.trim(t.target.textContent);this.trigger("keydown",{event:t}),e.hooks.beforeKeyDown(t,{tagify:this}).then(i=>{if(e.mode=="mix"){switch(t.key){case"Left":case"ArrowLeft":this.state.actions.ArrowLeft=!0;break;case"Delete":case"Backspace":if(this.state.editing)return;var a=document.getSelection(),o=t.key=="Delete"&&a.anchorOffset==(a.anchorNode.length||0),n=a.anchorNode.previousSibling,r=a.anchorNode.nodeType==1||!a.anchorOffset&&n&&n.nodeType==1&&a.anchorNode.previousSibling;$(this.DOM.input.innerHTML);var l,d,g,h=this.getTagElms(),f=a.anchorNode.length===1&&a.anchorNode.nodeValue=="​";if(e.backspace=="edit"&&r)return l=a.anchorNode.nodeType==1?null:a.anchorNode.previousElementSibling,setTimeout(this.editTag.bind(this),0,l),void t.preventDefault();if(K()&&r instanceof Element)return g=W(r),r.hasAttribute("readonly")||r.remove(),this.DOM.input.focus(),void setTimeout(()=>{D(g),this.DOM.input.click()});if(a.anchorNode.nodeName=="BR")return;if((o||r)&&a.anchorNode.nodeType==1?d=a.anchorOffset==0?o?h[0]:null:h[Math.min(h.length,a.anchorOffset)-1]:o?d=a.anchorNode.nextElementSibling:r instanceof Element&&(d=r),a.anchorNode.nodeType==3&&!a.anchorNode.nodeValue&&a.anchorNode.previousElementSibling&&t.preventDefault(),(r||o)&&!e.backspace||a.type!="Range"&&!a.anchorOffset&&a.anchorNode==this.DOM.input&&t.key!="Delete")return void t.preventDefault();if(a.type!="Range"&&d&&d.hasAttribute("readonly"))return void D(W(d));t.key=="Delete"&&f&&c(a.anchorNode.nextSibling)&&this.removeTags(a.anchorNode.nextSibling),clearTimeout(J),J=setTimeout(()=>{var m=document.getSelection();$(this.DOM.input.innerHTML),!o&&m.anchorNode.previousSibling,this.value=[].map.call(h,(p,v)=>{var y=c(p);if(p.parentNode||y.readonly)return y;this.trigger("remove",{tag:p,index:v,data:y})}).filter(p=>p)},20)}return!0}var u=e.dropdown.position=="manual";switch(t.key){case"Backspace":e.mode=="select"&&e.enforceWhitelist&&this.value.length?this.removeTags():this.state.dropdown.visible&&e.dropdown.position!="manual"||t.target.textContent!=""&&s.charCodeAt(0)!=8203||(e.backspace===!0?this.removeTags():e.backspace=="edit"&&setTimeout(this.editTag.bind(this),0));break;case"Esc":case"Escape":if(this.state.dropdown.visible)return;t.target.blur();break;case"Down":case"ArrowDown":this.state.dropdown.visible||this.dropdown.show();break;case"ArrowRight":{let m=this.state.inputSuggestion||this.state.ddItemData;if(m&&e.autoComplete.rightKey)return void this.addTags([m],!0);break}case"Tab":{let m=e.mode=="select";if(!s||m)return!0;t.preventDefault()}case"Enter":if(this.state.dropdown.visible&&!u)return;t.preventDefault(),setTimeout(()=>{this.state.dropdown.visible&&!u||this.state.actions.selectOption||!e.addTagOn.includes(t.key.toLowerCase())||this.addTags(s,!0)})}}).catch(i=>i)}},onInput(t){this.postUpdate();var e=this.settings;if(e.mode=="mix")return this.events.callbacks.onMixTagsInput.call(this,t);var s=this.input.normalize.call(this,void 0,{trim:!1}),i=s.length>=e.dropdown.enabled,a={value:s,inputElm:this.DOM.input},o=this.validateTag({value:s});e.mode=="select"&&this.toggleScopeValidation(o),a.isValid=o,this.state.inputText!=s&&(this.input.set.call(this,s,!1),s.search(e.delimiters)!=-1?this.addTags(s)&&this.input.set.call(this):e.dropdown.enabled>=0&&this.dropdown[i?"show":"hide"](s),this.trigger("input",a))},onMixTagsInput(t){var e,s,i,a,o,n,r,l,d=this.settings,g=this.value.length,h=this.getTagElms(),f=document.createDocumentFragment(),u=window.getSelection().getRangeAt(0),m=[].map.call(h,p=>c(p).value);if(t.inputType=="deleteContentBackward"&&K()&&this.events.callbacks.onKeydown.call(this,{target:t.target,key:"Backspace"}),X(this.getTagElms()),this.value.slice().forEach(p=>{p.readonly&&!m.includes(p.value)&&f.appendChild(this.createTagElem(p))}),f.childNodes.length&&(u.insertNode(f),this.setRangeAtStartEnd(!1,f.lastChild)),h.length!=g)return this.value=[].map.call(this.getTagElms(),p=>c(p)),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(n=window.getSelection()).rangeCount>0&&n.anchorNode.nodeType==3){if((u=n.getRangeAt(0).cloneRange()).collapse(!0),u.setStart(n.focusNode,0),i=(e=u.toString().slice(0,u.endOffset)).split(d.pattern).length-1,(s=e.match(d.pattern))&&(a=e.slice(e.lastIndexOf(s[s.length-1]))),a){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:a.match(d.pattern)[0],value:a.replace(d.pattern,"")},this.state.tag.baseOffset=n.baseOffset-this.state.tag.value.length,l=this.state.tag.value.match(d.delimiters))return this.state.tag.value=this.state.tag.value.replace(d.delimiters,""),this.state.tag.delimiters=l[0],this.addTags(this.state.tag.value,d.dropdown.clearOnSelect),void this.dropdown.hide();o=this.state.tag.value.length>=d.dropdown.enabled;try{r=(r=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&r.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch{}(r||i<this.state.mixMode.matchedPatternCount)&&(o=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=i}setTimeout(()=>{this.update({withoutChangeEvent:!0}),this.trigger("input",T({},this.state.tag,{textContent:this.DOM.input.textContent})),this.state.tag&&this.dropdown[o?"show":"hide"](this.state.tag.value)},10)},onInputIE(t){var e=this;setTimeout(function(){e.events.callbacks.onInput.call(e,t)})},observeOriginalInputValue(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickAnywhere(t){t.target==this.DOM.scope||this.DOM.scope.contains(t.target)||(this.toggleFocusClass(!1),this.state.hasFocus=!1)},onClickScope(t){var e=this.settings,s=t.target.closest("."+e.classNames.tag),i=+new Date-this.state.hasFocus;if(t.target!=this.DOM.scope){if(!t.target.classList.contains(e.classNames.tagX))return s?(this.trigger("click",{tag:s,index:this.getNodeIndex(s),data:c(s),event:t}),void(e.editTags!==1&&e.editTags.clicks!==1||this.events.callbacks.onDoubleClickScope.call(this,t))):void(t.target==this.DOM.input&&(e.mode=="mix"&&this.fixFirefoxLastTagNoCaret(),i>500)?this.state.dropdown.visible?this.dropdown.hide():e.dropdown.enabled===0&&e.mode!="mix"&&this.dropdown.show(this.value.length?"":void 0):e.mode!="select"||e.dropdown.enabled!==0||this.state.dropdown.visible||this.dropdown.show());this.removeTags(t.target.parentNode)}else this.DOM.input.focus()},onPaste(t){t.preventDefault();var e,s,i=this.settings;if(i.mode=="select"&&i.enforceWhitelist||!i.userInput)return!1;i.readonly||(e=t.clipboardData||window.clipboardData,s=e.getData("Text"),i.hooks.beforePaste(t,{tagify:this,pastedText:s,clipboardData:e}).then(a=>{a===void 0&&(a=s),a&&(this.injectAtCaret(a,window.getSelection().getRangeAt(0)),this.settings.mode=="mix"?this.events.callbacks.onMixTagsInput.call(this,t):this.settings.pasteAsTags?this.addTags(this.state.inputText+a,!0):(this.state.inputText=a,this.dropdown.show(a)))}).catch(a=>a))},onDrop(t){t.preventDefault()},onEditTagInput(t,e){var s=t.closest("."+this.settings.classNames.tag),i=this.getNodeIndex(s),a=c(s),o=this.input.normalize.call(this,t),n={[this.settings.tagTextProp]:o,__tagId:a.__tagId},r=this.validateTag(n);this.editTagChangeDetected(T(a,n))||t.originalIsValid!==!0||(r=!0),s.classList.toggle(this.settings.classNames.tagInvalid,r!==!0),a.__isValid=r,s.title=r===!0?a.title||a.value:r,o.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=o),this.dropdown.show(o)),this.trigger("edit:input",{tag:s,index:i,data:T({},this.value[i],{newValue:o}),event:e})},onEditTagPaste(t,e){var s=(e.clipboardData||window.clipboardData).getData("Text");e.preventDefault();var i=U(s);this.setRangeAtStartEnd(!1,i)},onEditTagFocus(t){this.state.editing={scope:t,input:t.querySelector("[contenteditable]")}},onEditTagBlur(t){if(this.state.editing&&(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(t))){var e,s,i=this.settings,a=t.closest("."+i.classNames.tag),o=c(a),n=this.input.normalize.call(this,t),r={[i.tagTextProp]:n,__tagId:o.__tagId},l=o.__originalData,d=this.editTagChangeDetected(T(o,r)),g=this.validateTag(r);if(n)if(d){if(e=this.hasMaxTags(),s=T({},l,{[i.tagTextProp]:this.trim(n),__isValid:g}),i.transformTag.call(this,s,l),(g=(!e||l.__isValid===!0)&&this.validateTag(s))!==!0){if(this.trigger("invalid",{data:s,tag:a,message:g}),i.editTags.keepInvalid)return;i.keepInvalidTags?s.__isValid=g:s=l}else i.keepInvalidTags&&(delete s.title,delete s["aria-invalid"],delete s.class);this.onEditTagDone(a,s)}else this.onEditTagDone(a,l);else this.onEditTagDone(a)}},onEditTagkeydown(t,e){if(!this.state.composing)switch(this.trigger("edit:keydown",{event:t}),t.key){case"Esc":case"Escape":this.state.editing=!1,e.__tagifyTagData.__originalData.value?e.parentNode.replaceChild(e.__tagifyTagData.__originalHTML,e):e.remove();break;case"Enter":case"Tab":t.preventDefault(),t.target.blur()}},onDoubleClickScope(t){var e,s,i=t.target.closest("."+this.settings.classNames.tag),a=c(i),o=this.settings;i&&o.userInput&&a.editable!==!1&&(e=i.classList.contains(this.settings.classNames.tagEditing),s=i.hasAttribute("readonly"),o.mode=="select"||o.readonly||e||s||!this.settings.editTags||this.editTag(i),this.toggleFocusClass(!0),this.trigger("dblclick",{tag:i,index:this.getNodeIndex(i),data:c(i)}))},onInputDOMChange(t){t.forEach(s=>{s.addedNodes.forEach(i=>{var a;if(i.outerHTML=="<div><br></div>")i.replaceWith(document.createElement("br"));else if(i.nodeType==1&&i.querySelector(this.settings.classNames.tagSelector)){let o=document.createTextNode("");i.childNodes[0].nodeType==3&&i.previousSibling.nodeName!="BR"&&(o=document.createTextNode(`
`)),i.replaceWith(o,...[...i.childNodes].slice(0,-1)),D(o)}else if(_.call(this,i))if(((a=i.previousSibling)==null?void 0:a.nodeType)!=3||i.previousSibling.textContent||i.previousSibling.remove(),i.previousSibling&&i.previousSibling.nodeName=="BR"){i.previousSibling.replaceWith(`
​`);let o=i.nextSibling,n="";for(;o;)n+=o.textContent,o=o.nextSibling;n.trim()&&D(i.previousSibling)}else i.previousSibling&&!c(i.previousSibling)||i.before("​")}),s.removedNodes.forEach(i=>{i&&i.nodeName=="BR"&&_.call(this,e)&&(this.removeTags(e),this.fixFirefoxLastTagNoCaret())})});var e=this.DOM.input.lastChild;e&&e.nodeValue==""&&e.remove(),e&&e.nodeName=="BR"||this.DOM.input.appendChild(document.createElement("br"))}}};function A(t,e){if(!t){console.warn("Tagify:","input element not found",t);const i=new Proxy(this,{get:()=>()=>i});return i}if(t.__tagify)return console.warn("Tagify: ","input element is already Tagified - Same instance is returned.",t),t.__tagify;var s;T(this,function(i){var a=document.createTextNode("");function o(n,r,l){l&&r.split(/\s+/g).forEach(d=>a[n+"EventListener"].call(a,d,l))}return{off(n,r){return o("remove",n,r),this},on(n,r){return r&&typeof r=="function"&&o("add",n,r),this},trigger(n,r,l){var d;if(l=l||{cloneData:!0},n)if(i.settings.isJQueryPlugin)n=="remove"&&(n="removeTag"),jQuery(i.DOM.originalInput).triggerHandler(n,[r]);else{try{var g=typeof r=="object"?r:{value:r};if((g=l.cloneData?T({},g):g).tagify=this,r.event&&(g.event=this.cloneEvent(r.event)),r instanceof Object)for(var h in r)r[h]instanceof HTMLElement&&(g[h]=r[h]);d=new CustomEvent(n,{detail:g})}catch(f){console.warn(f)}a.dispatchEvent(d)}}}}(this)),this.isFirefox=/firefox|fxios/i.test(navigator.userAgent)&&!/seamonkey/i.test(navigator.userAgent),this.isIE=window.document.documentMode,e=e||{},this.getPersistedData=(s=e.id,i=>{let a,o="/"+i;if(localStorage.getItem(S+s+"/v",1)==1)try{a=JSON.parse(localStorage[S+s+o])}catch{}return a}),this.setPersistedData=(i=>i?(localStorage.setItem(S+i+"/v",1),(a,o)=>{let n="/"+o,r=JSON.stringify(a);a&&o&&(localStorage.setItem(S+i+n,r),dispatchEvent(new Event("storage")))}):()=>{})(e.id),this.clearPersistedData=(i=>a=>{const o=S+"/"+i+"/";if(a)localStorage.removeItem(o+a);else for(let n in localStorage)n.includes(o)&&localStorage.removeItem(n)})(e.id),this.applySettings(t,e),this.state={inputText:"",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(t),Y.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),t.autofocus&&this.DOM.input.focus(),t.__tagify=this}return A.prototype={_dropdown:Z,placeCaretAfterNode:D,getSetTagData:c,helpers:{sameStr:E,removeCollectionProp:j,omit:P,isObject:O,parseHTML:R,escapeHTML:I,extend:T,concatWithoutDups:q,getUID:z,isNodeTag:_},customEventsList:["change","add","remove","invalid","input","click","keydown","focus","blur","edit:input","edit:beforeUpdate","edit:updated","edit:start","edit:keydown","dropdown:show","dropdown:hide","dropdown:select","dropdown:updated","dropdown:noMatch","dropdown:scroll"],dataProps:["__isValid","__removed","__originalData","__originalHTML","__tagId"],trim(t){return this.settings.trim&&t&&typeof t=="string"?t.trim():t},parseHTML:R,templates:et,parseTemplate(t,e){return R((t=this.settings.templates[t]||t).apply(this,e))},set whitelist(t){const e=t&&Array.isArray(t);this.settings.whitelist=e?t:[],this.setPersistedData(e?t:[],"whitelist")},get whitelist(){return this.settings.whitelist},generateClassSelectors(t){for(let e in t){let s=e;Object.defineProperty(t,s+"Selector",{get(){return"."+this[s].split(" ")[0]}})}},applySettings(t,e){var o,n;F.templates=this.templates;var s=T({},F,e.mode=="mix"?{dropdown:{position:"text"}}:{}),i=this.settings=T({},s,e);if(i.disabled=t.hasAttribute("disabled"),i.readonly=i.readonly||t.hasAttribute("readonly"),i.placeholder=I(t.getAttribute("placeholder")||i.placeholder||""),i.required=t.hasAttribute("required"),this.generateClassSelectors(i.classNames),i.dropdown.includeSelectedTags===void 0&&(i.dropdown.includeSelectedTags=i.duplicates),this.isIE&&(i.autoComplete=!1),["whitelist","blacklist"].forEach(r=>{var l=t.getAttribute("data-"+r);l&&(l=l.split(i.delimiters))instanceof Array&&(i[r]=l)}),"autoComplete"in e&&!O(e.autoComplete)&&(i.autoComplete=F.autoComplete,i.autoComplete.enabled=e.autoComplete),i.mode=="mix"&&(i.pattern=i.pattern||/@/,i.autoComplete.rightKey=!0,i.delimiters=e.delimiters||null,i.tagTextProp&&!i.dropdown.searchKeys.includes(i.tagTextProp)&&i.dropdown.searchKeys.push(i.tagTextProp)),t.pattern)try{i.pattern=new RegExp(t.pattern)}catch{}if(i.delimiters){i._delimiters=i.delimiters;try{i.delimiters=new RegExp(this.settings.delimiters,"g")}catch{}}i.disabled&&(i.userInput=!1),this.TEXTS=M(M({},tt),i.texts||{}),(i.mode!="select"||(o=e.dropdown)!=null&&o.enabled)&&i.userInput||(i.dropdown.enabled=0),i.dropdown.appendTarget=((n=e.dropdown)==null?void 0:n.appendTarget)||document.body;let a=this.getPersistedData("whitelist");Array.isArray(a)&&(this.whitelist=Array.isArray(i.whitelist)?q(i.whitelist,a):a)},getAttributes(t){var e,s=this.getCustomAttributes(t),i="";for(e in s)i+=" "+e+(t[e]!==void 0?`="${s[e]}"`:"");return i},getCustomAttributes(t){if(!O(t))return"";var e,s={};for(e in t)e.slice(0,2)!="__"&&e!="class"&&t.hasOwnProperty(e)&&t[e]!==void 0&&(s[e]=I(t[e]));return s},setStateSelection(){var t=window.getSelection(),e={anchorOffset:t.anchorOffset,anchorNode:t.anchorNode,range:t.getRangeAt&&t.rangeCount&&t.getRangeAt(0)};return this.state.selection=e,e},getCSSVars(){var t=getComputedStyle(this.DOM.scope,null),e;this.CSSVars={tagHideTransition:(s=>{let i=s.value;return s.unit=="s"?1e3*i:i})(function(s){if(!s)return{};var i=(s=s.trim().split(" ")[0]).split(/\d+/g).filter(a=>a).pop().trim();return{value:+s.split(i).filter(a=>a)[0].trim(),unit:i}}((e="tag-hide-transition",t.getPropertyValue("--"+e))))}},build(t){var e=this.DOM;this.settings.mixMode.integrated?(e.originalInput=null,e.scope=t,e.input=t):(e.originalInput=t,e.originalInput_tabIndex=t.tabIndex,e.scope=this.parseTemplate("wrapper",[t,this.settings]),e.input=e.scope.querySelector(this.settings.classNames.inputSelector),t.parentNode.insertBefore(e.scope,t),t.tabIndex=-1)},destroy(){this.events.unbindGlobal.call(this),this.DOM.scope.parentNode.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues(t){var e,s=this.settings;if(this.state.blockChangeEvent=!0,t===void 0){const i=this.getPersistedData("value");t=i&&!this.DOM.originalInput.value?i:s.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),t)if(s.mode=="mix")this.parseMixTags(t),(e=this.DOM.input.lastChild)&&e.tagName=="BR"||this.DOM.input.insertAdjacentHTML("beforeend","<br>");else{try{JSON.parse(t)instanceof Array&&(t=JSON.parse(t))}catch{}this.addTags(t,!0).forEach(i=>i&&i.classList.add(s.classNames.tagNoAnimation))}else this.postUpdate();this.state.lastOriginalValueReported=s.mixMode.integrated?"":this.DOM.originalInput.value},cloneEvent(t){var e={};for(var s in t)s!="path"&&(e[s]=t[s]);return e},loading(t){return this.state.isLoading=t,this.DOM.scope.classList[t?"add":"remove"](this.settings.classNames.scopeLoading),this},tagLoading(t,e){return t&&t.classList[e?"add":"remove"](this.settings.classNames.tagLoading),this},toggleClass(t,e){typeof t=="string"&&this.DOM.scope.classList.toggle(t,e)},toggleScopeValidation(t){var e=t===!0||t===void 0;!this.settings.required&&t&&t===this.TEXTS.empty&&(e=!0),this.toggleClass(this.settings.classNames.tagInvalid,!e),this.DOM.scope.title=e?"":t},toggleFocusClass(t){this.toggleClass(this.settings.classNames.focus,!!t)},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var t=this.DOM.originalInput,e=this.state.lastOriginalValueReported!==t.value,s=new CustomEvent("change",{bubbles:!0});e&&(this.state.lastOriginalValueReported=t.value,s.simulated=!0,t._valueTracker&&t._valueTracker.setValue(Math.random()),t.dispatchEvent(s),this.trigger("change",this.state.lastOriginalValueReported),t.value=this.state.lastOriginalValueReported)}},events:st,fixFirefoxLastTagNoCaret(){},setRangeAtStartEnd(t,e){if(e){t=typeof t=="number"?t:!!t,e=e.lastChild||e;var s=document.getSelection();if(s.focusNode instanceof Element&&!this.DOM.input.contains(s.focusNode))return!0;try{s.rangeCount>=1&&["Start","End"].forEach(i=>s.getRangeAt(0)["set"+i](e,t||e.length))}catch(i){console.warn("Tagify: ",i)}}},insertAfterTag(t,e){if(e=e||this.settings.mixMode.insertAfterTag,t&&t.parentNode&&e)return e=typeof e=="string"?document.createTextNode(e):e,t.parentNode.insertBefore(e,t.nextSibling),e},editTagChangeDetected(t){var e=t.__originalData;for(var s in e)if(!this.dataProps.includes(s)&&t[s]!=e[s])return!0;return!1},getTagTextNode(t){return t.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode(t,e){this.getTagTextNode(t).innerHTML=I(e)},editTag(t,e){t=t||this.getLastTag(),e=e||{},this.dropdown.hide();var s=this.settings,i=this.getTagTextNode(t),a=this.getNodeIndex(t),o=c(t),n=this.events.callbacks,r=!0;if(i){if(!(o instanceof Object&&"editable"in o)||o.editable)return o=c(t,{__originalData:T({},o),__originalHTML:t.cloneNode(!0)}),c(o.__originalHTML,o.__originalData),i.setAttribute("contenteditable",!0),t.classList.add(s.classNames.tagEditing),i.addEventListener("focus",n.onEditTagFocus.bind(this,t)),i.addEventListener("blur",n.onEditTagBlur.bind(this,this.getTagTextNode(t))),i.addEventListener("input",n.onEditTagInput.bind(this,i)),i.addEventListener("paste",n.onEditTagPaste.bind(this,i)),i.addEventListener("keydown",l=>n.onEditTagkeydown.call(this,l,t)),i.addEventListener("compositionstart",n.onCompositionStart.bind(this)),i.addEventListener("compositionend",n.onCompositionEnd.bind(this)),e.skipValidation||(r=this.editTagToggleValidity(t)),i.originalIsValid=r,this.trigger("edit:start",{tag:t,index:a,data:o,isValid:r}),i.focus(),this.setRangeAtStartEnd(!1,i),this}else console.warn("Cannot find element in Tag template: .",s.classNames.tagTextSelector)},editTagToggleValidity(t,e){var s;if(e=e||c(t))return(s=!("__isValid"in e)||e.__isValid===!0)||this.removeTagsFromValue(t),this.update(),t.classList.toggle(this.settings.classNames.tagNotAllowed,!s),e.__isValid=s,e.__isValid;console.warn("tag has no data: ",t,e)},onEditTagDone(t,e){t=t||this.state.editing.scope,e=e||{};var s,i={tag:t,index:this.getNodeIndex(t),previousData:c(t),data:e},a=this.settings;this.trigger("edit:beforeUpdate",i,{cloneData:!1}),this.state.editing=!1,delete e.__originalData,delete e.__originalHTML,t&&((s=e[a.tagTextProp])?s.trim()&&s:!(a.tagTextProp in e)&&e.value)?(t=this.replaceTag(t,e),this.editTagToggleValidity(t,e),a.a11y.focusableTags?t.focus():D(t)):t&&this.removeTags(t),this.trigger("edit:updated",i),this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag(t,e){e&&e.value||(e=t.__tagifyTagData),e.__isValid&&e.__isValid!=1&&T(e,this.getInvalidTagAttrs(e,e.__isValid));var s=this.createTagElem(e);return t.parentNode.replaceChild(s,t),this.updateValueByDOMTags(),s},updateValueByDOMTags(){this.value.length=0,[].forEach.call(this.getTagElms(),t=>{t.classList.contains(this.settings.classNames.tagNotAllowed.split(" ")[0])||this.value.push(c(t))}),this.update()},injectAtCaret(t,e){var i;if(!(e=e||((i=this.state.selection)==null?void 0:i.range))&&t)return this.appendMixTags(t),this;let s=U(t,e);return this.setRangeAtStartEnd(!1,s),this.updateValueByDOMTags(),this.update(),this},input:{set(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];var s=this.settings.dropdown.closeOnSelect;this.state.inputText=t,e&&(this.DOM.input.innerHTML=I(""+t)),!t&&s&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw(){return this.DOM.input.textContent},validate(){var t=!this.state.inputText||this.validateTag({value:this.state.inputText})===!0;return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!t),t},normalize(t,e){var s=t||this.DOM.input,i=[];s.childNodes.forEach(a=>a.nodeType==3&&i.push(a.nodeValue)),i=i.join(`
`);try{i=i.replace(/(?:\r\n|\r|\n)/g,this.settings.delimiters.source.charAt(0))}catch{}return i=i.replace(/\s/g," "),e!=null&&e.trim?this.trim(i):i},autocomplete:{suggest(t){if(this.settings.autoComplete.enabled){typeof(t=t||{value:""})=="string"&&(t={value:t});var e=this.dropdown.getMappedValue(t);if(typeof e!="number"){var s=e.substr(0,this.state.inputText.length).toLowerCase(),i=e.substring(this.state.inputText.length);e&&this.state.inputText&&s==this.state.inputText.toLowerCase()?(this.DOM.input.setAttribute("data-suggest",i),this.state.inputSuggestion=t):(this.DOM.input.removeAttribute("data-suggest"),delete this.state.inputSuggestion)}}},set(t){var e=this.DOM.input.getAttribute("data-suggest"),s=t||(e?this.state.inputText+e:null);return!!s&&(this.settings.mode=="mix"?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+s)):(this.input.set.call(this,s),this.setRangeAtStartEnd(!1,this.DOM.input)),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx(t){return this.value.findIndex(e=>e.__tagId==(t||{}).__tagId)},getNodeIndex(t){var e=0;if(t)for(;t=t.previousElementSibling;)e++;return e},getTagElms(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];var i="."+[...this.settings.classNames.tag.split(" "),...e].join(".");return[].slice.call(this.DOM.scope.querySelectorAll(i))},getLastTag(){var t=this.DOM.scope.querySelectorAll(`${this.settings.classNames.tagSelector}:not(.${this.settings.classNames.tagHide}):not([readonly])`);return t[t.length-1]},isTagDuplicate(t,e,s){var i=0;if(this.settings.mode=="select")return!1;for(let a of this.value)E(this.trim(""+t),a.value,e)&&s!=a.__tagId&&i++;return i},getTagIndexByValue(t){var e=[],s=this.settings.dropdown.caseSensitive;return this.getTagElms().forEach((i,a)=>{i.__tagifyTagData&&E(this.trim(i.__tagifyTagData.value),t,s)&&e.push(a)}),e},getTagElmByValue(t){var e=this.getTagIndexByValue(t)[0];return this.getTagElms()[e]},flashTag(t){t&&(t.classList.add(this.settings.classNames.tagFlash),setTimeout(()=>{t.classList.remove(this.settings.classNames.tagFlash)},100))},isTagBlacklisted(t){return t=this.trim(t.toLowerCase()),this.settings.blacklist.filter(e=>(""+e).toLowerCase()==t).length},isTagWhitelisted(t){return!!this.getWhitelistItem(t)},getWhitelistItem(t,e,s){e=e||"value";var i,a=this.settings;return(s=s||a.whitelist).some(o=>{var n=typeof o=="string"?o:o[e]||o.value;if(E(n,t,a.dropdown.caseSensitive,a.trim))return i=typeof o=="string"?{value:o}:o,!0}),i||e!="value"||a.tagTextProp=="value"||(i=this.getWhitelistItem(t,a.tagTextProp,s)),i},validateTag(t){var e=this.settings,s="value"in t?"value":e.tagTextProp,i=this.trim(t[s]+"");return(t[s]+"").trim()?e.mode!="mix"&&e.pattern&&e.pattern instanceof RegExp&&!e.pattern.test(i)?this.TEXTS.pattern:!e.duplicates&&this.isTagDuplicate(i,e.dropdown.caseSensitive,t.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(i)||e.enforceWhitelist&&!this.isTagWhitelisted(i)?this.TEXTS.notAllowed:!e.validate||e.validate(t):this.TEXTS.empty},getInvalidTagAttrs(t,e){return{"aria-invalid":!0,class:`${t.class||""} ${this.settings.classNames.tagNotAllowed}`.trim(),title:e}},hasMaxTags(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly(t,e){var s=this.settings;document.activeElement.blur(),s[e||"readonly"]=t,this.DOM.scope[(t?"set":"remove")+"Attribute"](e||"readonly",!0),this.settings.userInput=!0,this.setContentEditable(!t)},setContentEditable(t){this.settings.userInput&&(this.DOM.input.contentEditable=t,this.DOM.input.tabIndex=t?0:-1)},setDisabled(t){this.setReadonly(t,"disabled")},normalizeTags(t){var e=this.settings,s=e.whitelist,i=e.delimiters,a=e.mode,o=e.tagTextProp,n=[],r=!!s&&s[0]instanceof Object,l=Array.isArray(t),d=l&&t[0].value,g=h=>(h+"").split(i).filter(f=>f).map(f=>({[o]:this.trim(f),value:this.trim(f)}));if(typeof t=="number"&&(t=t.toString()),typeof t=="string"){if(!t.trim())return[];t=g(t)}else l&&(t=[].concat(...t.map(h=>h.value!=null?h:g(h))));return r&&!d&&(t.forEach(h=>{var f=n.map(p=>p.value),u=this.dropdown.filterListItems.call(this,h[o],{exact:!0});this.settings.duplicates||(u=u.filter(p=>!f.includes(p.value)));var m=u.length>1?this.getWhitelistItem(h[o],o,u):u[0];m&&m instanceof Object?n.push(m):a!="mix"&&(h.value==null&&(h.value=h[o]),n.push(h))}),n.length&&(t=n)),t},parseMixTags(t){var e=this.settings,s=e.mixTagsInterpolator,i=e.duplicates,a=e.transformTag,o=e.enforceWhitelist,n=e.maxTags,r=e.tagTextProp,l=[];t=t.split(s[0]).map((g,h)=>{var f,u,m,p=g.split(s[1]),v=p[0],y=l.length==n;try{if(v==+v)throw Error;u=JSON.parse(v)}catch{u=this.normalizeTags(v)[0]||{value:v}}if(a.call(this,u),y||!(p.length>1)||o&&!this.isTagWhitelisted(u.value)||!i&&this.isTagDuplicate(u.value)){if(g)return h?s[0]+g:g}else u[f=u[r]?r:"value"]=this.trim(u[f]),m=this.createTagElem(u),l.push(u),m.classList.add(this.settings.classNames.tagNoAnimation),p[0]=m.outerHTML,this.value.push(u);return p.join("")}).join(""),this.DOM.input.innerHTML=t,this.DOM.input.appendChild(document.createTextNode("")),this.DOM.input.normalize();var d=this.getTagElms();return d.forEach((g,h)=>c(g,l[h])),this.update({withoutChangeEvent:!0}),X(d,this.state.hasFocus),t},replaceTextWithNode(t,e){if(this.state.tag||e){e=e||this.state.tag.prefix+this.state.tag.value;var s,i,a=this.state.selection||window.getSelection(),o=a.anchorNode,n=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return o.splitText(a.anchorOffset-n),(s=o.nodeValue.lastIndexOf(e))==-1||(i=o.splitText(s),t&&o.parentNode.replaceChild(t,i)),!0}},selectTag(t,e){var s=this.settings;if(!s.enforceWhitelist||this.isTagWhitelisted(e.value)){this.input.set.call(this,e[s.tagTextProp]||e.value,!0),this.state.actions.selectOption&&setTimeout(()=>this.setRangeAtStartEnd(!1,this.DOM.input));var i=this.getLastTag();return i?this.replaceTag(i,e):this.appendTag(t),this.value[0]=e,this.update(),this.trigger("add",{tag:t,data:e}),[t]}},addEmptyTag(t){var e=T({value:""},t||{}),s=this.createTagElem(e);c(s,e),this.appendTag(s),this.editTag(s,{skipValidation:!0})},addTags(t,e,s){var i=[],a=this.settings,o=[],n=document.createDocumentFragment();if(s=s||a.skipInvalid,!t||t.length==0)return i;switch(t=this.normalizeTags(t),a.mode){case"mix":return this.addMixTags(t);case"select":e=!1,this.removeAllTags()}return this.DOM.input.removeAttribute("style"),t.forEach(r=>{var l,d={},g=Object.assign({},r,{value:r.value+""});if(r=Object.assign({},g),a.transformTag.call(this,r),r.__isValid=this.hasMaxTags()||this.validateTag(r),r.__isValid!==!0){if(s)return;if(T(d,this.getInvalidTagAttrs(r,r.__isValid),{__preInvalidData:g}),r.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(r.value)),!a.createInvalidTags)return void o.push(r.value)}if("readonly"in r&&(r.readonly?d["aria-readonly"]=!0:delete r.readonly),l=this.createTagElem(r,d),i.push(l),a.mode=="select")return this.selectTag(l,r);n.appendChild(l),r.__isValid&&r.__isValid===!0?(this.value.push(r),this.trigger("add",{tag:l,index:this.value.length-1,data:r})):(this.trigger("invalid",{data:r,index:this.value.length,tag:l,message:r.__isValid}),a.keepInvalidTags||setTimeout(()=>this.removeTags(l,!0),1e3)),this.dropdown.position()}),this.appendTag(n),this.update(),t.length&&e&&(this.input.set.call(this,a.createInvalidTags?"":o.join(a._delimiters)),this.setRangeAtStartEnd(!1,this.DOM.input)),a.dropdown.enabled&&this.dropdown.refilter(),i},addMixTags(t){if((t=this.normalizeTags(t))[0].prefix||this.state.tag)return this.prefixedTextToTag(t[0]);var e=document.createDocumentFragment();return t.forEach(s=>{var i=this.createTagElem(s);e.appendChild(i)}),this.appendMixTags(e),e},appendMixTags(t){var e=!!this.state.selection;e?this.injectAtCaret(t):(this.DOM.input.focus(),(e=this.setStateSelection()).range.setStart(this.DOM.input,e.range.endOffset),e.range.setEnd(this.DOM.input,e.range.endOffset),this.DOM.input.appendChild(t),this.updateValueByDOMTags(),this.update())},prefixedTextToTag(t){var e,s=this.settings,i=this.state.tag.delimiters;if(s.transformTag.call(this,t),t.prefix=t.prefix||this.state.tag?this.state.tag.prefix:(s.pattern.source||s.pattern)[0],e=this.createTagElem(t),this.replaceTextWithNode(e)||this.DOM.input.appendChild(e),setTimeout(()=>e.classList.add(this.settings.classNames.tagNoAnimation),300),this.value.push(t),this.update(),!i){var a=this.insertAfterTag(e)||e;setTimeout(D,0,a)}return this.state.tag=null,this.trigger("add",T({},{tag:e},{data:t})),e},appendTag(t){var e=this.DOM,s=e.input;e.scope.insertBefore(t,s)},createTagElem(t,e){t.__tagId=z();var s,i=T({},t,M({value:I(t.value+"")},e));return function(a){for(var o,n=document.createNodeIterator(a,NodeFilter.SHOW_TEXT,null,!1);o=n.nextNode();)o.textContent.trim()||o.parentNode.removeChild(o)}(s=this.parseTemplate("tag",[i,this])),c(s,t),s},reCheckInvalidTags(){var t=this.settings;this.getTagElms(t.classNames.tagNotAllowed).forEach((e,s)=>{var i=c(e),a=this.hasMaxTags(),o=this.validateTag(i),n=o===!0&&!a;if(t.mode=="select"&&this.toggleScopeValidation(o),n)return i=i.__preInvalidData?i.__preInvalidData:{value:i.value},this.replaceTag(e,i);e.title=a||o})},removeTags(t,e,s){var i,a=this.settings;if(t=t&&t instanceof HTMLElement?[t]:t instanceof Array?t:t?[t]:[this.getLastTag()],i=t.reduce((o,n)=>{n&&typeof n=="string"&&(n=this.getTagElmByValue(n));var r=c(n);return n&&r&&!r.readonly&&o.push({node:n,idx:this.getTagIdx(r),data:c(n,{__removed:!0})}),o},[]),s=typeof s=="number"?s:this.CSSVars.tagHideTransition,a.mode=="select"&&(s=0,this.input.set.call(this)),i.length==1&&a.mode!="select"&&i[0].node.classList.contains(a.classNames.tagNotAllowed)&&(e=!0),i.length)return a.hooks.beforeRemoveTag(i,{tagify:this}).then(()=>{function o(n){n.node.parentNode&&(n.node.parentNode.removeChild(n.node),e?a.keepInvalidTags&&this.trigger("remove",{tag:n.node,index:n.idx}):(this.trigger("remove",{tag:n.node,index:n.idx,data:n.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),a.keepInvalidTags&&this.reCheckInvalidTags()))}s&&s>10&&i.length==1?(function(n){n.node.style.width=parseFloat(window.getComputedStyle(n.node).width)+"px",document.body.clientTop,n.node.classList.add(a.classNames.tagHide),setTimeout(o.bind(this),s,n)}).call(this,i[0]):i.forEach(o.bind(this)),e||(this.removeTagsFromValue(i.map(n=>n.node)),this.update(),a.mode=="select"&&this.setContentEditable(!0))}).catch(o=>{})},removeTagsFromDOM(){[].slice.call(this.getTagElms()).forEach(t=>t.parentNode.removeChild(t))},removeTagsFromValue(t){(t=Array.isArray(t)?t:[t]).forEach(e=>{var s=c(e),i=this.getTagIdx(s);i>-1&&this.value.splice(i,1)})},removeAllTags(t){t=t||{},this.value=[],this.settings.mode=="mix"?this.DOM.input.innerHTML="":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout(()=>{this.DOM.input.focus()}),this.settings.mode=="select"&&(this.input.set.call(this),this.setContentEditable(!0)),this.update(t)},postUpdate(){var i,a;this.state.blockChangeEvent=!1;var t=this.settings,e=t.classNames,s=t.mode=="mix"?t.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(e.hasMaxTags,this.value.length>=t.maxTags),this.toggleClass(e.hasNoTags,!this.value.length),this.toggleClass(e.empty,!s),t.mode=="select"&&this.toggleScopeValidation((a=(i=this.value)==null?void 0:i[0])==null?void 0:a.__isValid)},setOriginalInputValue(t){var e=this.DOM.originalInput;this.settings.mixMode.integrated||(e.value=t,e.tagifyValue=e.value,this.setPersistedData(t,"value"))},update(t){clearTimeout(this.debouncedUpdateTimeout),this.debouncedUpdateTimeout=setTimeout((function(){var e=this.getInputValue();this.setOriginalInputValue(e),this.settings.onChangeAfterBlur&&(t||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent(),this.postUpdate()}).bind(this),100)},getInputValue(){var t=this.getCleanValue();return this.settings.mode=="mix"?this.getMixedTagsAsString(t):t.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(t):JSON.stringify(t):""},getCleanValue(t){return j(t||this.value,this.dataProps)},getMixedTagsAsString(){var t="",e=this,s=this.settings,i=s.originalInputValueFormat||JSON.stringify,a=s.mixTagsInterpolator;return function o(n){n.childNodes.forEach(r=>{if(r.nodeType==1){const l=c(r);if(r.tagName=="BR"&&(t+=`\r
`),l&&_.call(e,r)){if(l.__removed)return;t+=a[0]+i(P(l,e.dataProps))+a[1]}else r.getAttribute("style")||["B","I","U"].includes(r.tagName)?t+=r.textContent:r.tagName!="DIV"&&r.tagName!="P"||(t+=`\r
`,o(r))}else t+=r.textContent})}(this.DOM.input),t}},A.prototype.removeTag=A.prototype.removeTags,A})})(G);var nt=G.exports;const ot=at(nt);try{window.Tagify=ot}catch{}
