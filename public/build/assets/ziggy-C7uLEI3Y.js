const r={url:"http://127.0.0.1:8000",port:8e3,defaults:{},routes:{"sanctum.csrf-cookie":{uri:"sanctum/csrf-cookie",methods:["GET","HEAD"]},telescope:{uri:"telescope/{view?}",methods:["GET","HEAD"],wheres:{view:"(.*)"},parameters:["view"]},"livewire.update":{uri:"livewire/update",methods:["POST"]},"livewire.upload-file":{uri:"livewire/upload-file",methods:["POST"]},"livewire.preview-file":{uri:"livewire/preview-file/{filename}",methods:["GET","HEAD"],parameters:["filename"]},"ignition.healthCheck":{uri:"_ignition/health-check",methods:["GET","HEAD"]},"ignition.executeSolution":{uri:"_ignition/execute-solution",methods:["POST"]},"ignition.updateConfig":{uri:"_ignition/update-config",methods:["POST"]},"regions.index":{uri:"api/regions",methods:["GET","HEAD"]},"regions.store":{uri:"api/regions",methods:["POST"]},"regions.show":{uri:"api/regions/{region}",methods:["GET","HEAD"],parameters:["region"]},"regions.update":{uri:"api/regions/{region}",methods:["PUT","PATCH"],parameters:["region"]},"regions.destroy":{uri:"api/regions/{region}",methods:["DELETE"],parameters:["region"]},"car-types.index":{uri:"api/car-types",methods:["GET","HEAD"]},"car-types.store":{uri:"api/car-types",methods:["POST"]},"car-types.show":{uri:"api/car-types/{car_type}",methods:["GET","HEAD"],parameters:["car_type"]},"car-types.update":{uri:"api/car-types/{car_type}",methods:["PUT","PATCH"],parameters:["car_type"]},"car-types.destroy":{uri:"api/car-types/{car_type}",methods:["DELETE"],parameters:["car_type"]},"shifts.index":{uri:"api/shifts",methods:["GET","HEAD"]},"shifts.store":{uri:"api/shifts",methods:["POST"]},"shifts.show":{uri:"api/shifts/{shift}",methods:["GET","HEAD"],parameters:["shift"]},"shifts.update":{uri:"api/shifts/{shift}",methods:["PUT","PATCH"],parameters:["shift"]},"shifts.destroy":{uri:"api/shifts/{shift}",methods:["DELETE"],parameters:["shift"]},"pricings.index":{uri:"api/pricings",methods:["GET","HEAD"]},"user-addresses.index":{uri:"api/user-addresses",methods:["GET","HEAD"]},"user-addresses.store":{uri:"api/user-addresses",methods:["POST"]},"user-addresses.show":{uri:"api/user-addresses/{user_address}",methods:["GET","HEAD"],parameters:["user_address"],bindings:{user_address:"id"}},"user-addresses.update":{uri:"api/user-addresses/{user_address}",methods:["PUT","PATCH"],parameters:["user_address"]},"user-addresses.destroy":{uri:"api/user-addresses/{user_address}",methods:["DELETE"],parameters:["user_address"],bindings:{user_address:"id"}},"settings.index":{uri:"api/settings",methods:["GET","HEAD"]},"media.store":{uri:"api/media",methods:["POST"]},"pricings.store":{uri:"api/pricings",methods:["POST"]},"pricings.show":{uri:"api/pricings/{pricing}",methods:["GET","HEAD"],parameters:["pricing"]},"pricings.update":{uri:"api/pricings/{pricing}",methods:["PUT","PATCH"],parameters:["pricing"]},"pricings.destroy":{uri:"api/pricings/{pricing}",methods:["DELETE"],parameters:["pricing"]},"dashboard.login":{uri:"dashboard/login",methods:["GET","HEAD"]},"dashboard.login-post":{uri:"dashboard/login",methods:["POST"]},"dashboard.swap_lang":{uri:"dashboard/lang/{locale}",methods:["GET","HEAD"],parameters:["locale"]},"dashboard.main":{uri:"dashboard/dashboard",methods:["GET","HEAD"]},"dashboard.statistics.index":{uri:"dashboard/statistics",methods:["GET","HEAD"]},"dashboard.logout":{uri:"dashboard/logout",methods:["POST"]},"dashboard.user.profile":{uri:"dashboard/profile",methods:["GET","HEAD"]},"dashboard.roles.index":{uri:"dashboard/roles",methods:["GET","HEAD"]},"dashboard.roles.create":{uri:"dashboard/roles/create",methods:["GET","HEAD"]},"dashboard.roles.store":{uri:"dashboard/roles",methods:["POST"]},"dashboard.roles.show":{uri:"dashboard/roles/{role}",methods:["GET","HEAD"],parameters:["role"]},"dashboard.roles.edit":{uri:"dashboard/roles/{role}/edit",methods:["GET","HEAD"],parameters:["role"],bindings:{role:"id"}},"dashboard.roles.update":{uri:"dashboard/roles/{role}",methods:["PUT","PATCH"],parameters:["role"]},"dashboard.roles.destroy":{uri:"dashboard/roles/{role}",methods:["DELETE"],parameters:["role"]},"dashboard.car_types.index":{uri:"dashboard/car_types",methods:["GET","HEAD"]},"dashboard.car_types.create":{uri:"dashboard/car_types/create",methods:["GET","HEAD"]},"dashboard.car_types.store":{uri:"dashboard/car_types",methods:["POST"]},"dashboard.car_types.show":{uri:"dashboard/car_types/{car_type}",methods:["GET","HEAD"],parameters:["car_type"]},"dashboard.car_types.edit":{uri:"dashboard/car_types/{car_type}/edit",methods:["GET","HEAD"],parameters:["car_type"]},"dashboard.car_types.update":{uri:"dashboard/car_types/{car_type}",methods:["PUT","PATCH"],parameters:["car_type"]},"dashboard.car_types.destroy":{uri:"dashboard/car_types/{car_type}",methods:["DELETE"],parameters:["car_type"]},"dashboard.drivers.index":{uri:"dashboard/drivers",methods:["GET","HEAD"]},"dashboard.drivers.create":{uri:"dashboard/drivers/create",methods:["GET","HEAD"]},"dashboard.drivers.store":{uri:"dashboard/drivers",methods:["POST"]},"dashboard.drivers.show":{uri:"dashboard/drivers/{driver}",methods:["GET","HEAD"],parameters:["driver"]},"dashboard.drivers.edit":{uri:"dashboard/drivers/{driver}/edit",methods:["GET","HEAD"],parameters:["driver"],bindings:{driver:"id"}},"dashboard.drivers.update":{uri:"dashboard/drivers/{driver}",methods:["PUT","PATCH"],parameters:["driver"]},"dashboard.drivers.destroy":{uri:"dashboard/drivers/{driver}",methods:["DELETE"],parameters:["driver"]},"dashboard.regions.index":{uri:"dashboard/regions",methods:["GET","HEAD"]},"dashboard.regions.create":{uri:"dashboard/regions/create",methods:["GET","HEAD"]},"dashboard.regions.store":{uri:"dashboard/regions",methods:["POST"]},"dashboard.regions.show":{uri:"dashboard/regions/{region}",methods:["GET","HEAD"],parameters:["region"],bindings:{region:"id"}},"dashboard.regions.edit":{uri:"dashboard/regions/{region}/edit",methods:["GET","HEAD"],parameters:["region"],bindings:{region:"id"}},"dashboard.regions.update":{uri:"dashboard/regions/{region}",methods:["PUT","PATCH"],parameters:["region"]},"dashboard.regions.destroy":{uri:"dashboard/regions/{region}",methods:["DELETE"],parameters:["region"]},"dashboard.users.index":{uri:"dashboard/users",methods:["GET","HEAD"]},"dashboard.users.create":{uri:"dashboard/users/create",methods:["GET","HEAD"]},"dashboard.users.store":{uri:"dashboard/users",methods:["POST"]},"dashboard.users.show":{uri:"dashboard/users/{user}",methods:["GET","HEAD"],parameters:["user"],bindings:{user:"id"}},"dashboard.users.edit":{uri:"dashboard/users/{user}/edit",methods:["GET","HEAD"],parameters:["user"],bindings:{user:"id"}},"dashboard.users.update":{uri:"dashboard/users/{user}",methods:["PUT","PATCH"],parameters:["user"]},"dashboard.users.destroy":{uri:"dashboard/users/{user}",methods:["DELETE"],parameters:["user"]},"dashboard.shifts.index":{uri:"dashboard/shifts",methods:["GET","HEAD"]},"dashboard.shifts.create":{uri:"dashboard/shifts/create",methods:["GET","HEAD"]},"dashboard.shifts.store":{uri:"dashboard/shifts",methods:["POST"]},"dashboard.shifts.show":{uri:"dashboard/shifts/{shift}",methods:["GET","HEAD"],parameters:["shift"],bindings:{shift:"id"}},"dashboard.shifts.edit":{uri:"dashboard/shifts/{shift}/edit",methods:["GET","HEAD"],parameters:["shift"],bindings:{shift:"id"}},"dashboard.shifts.update":{uri:"dashboard/shifts/{shift}",methods:["PUT","PATCH"],parameters:["shift"]},"dashboard.shifts.destroy":{uri:"dashboard/shifts/{shift}",methods:["DELETE"],parameters:["shift"]},"dashboard.rides.index":{uri:"dashboard/rides",methods:["GET","HEAD"]},"dashboard.rides.create":{uri:"dashboard/rides/create",methods:["GET","HEAD"]},"dashboard.rides.store":{uri:"dashboard/rides",methods:["POST"]},"dashboard.rides.show":{uri:"dashboard/rides/{ride}",methods:["GET","HEAD"],parameters:["ride"],bindings:{ride:"id"}},"dashboard.rides.edit":{uri:"dashboard/rides/{ride}/edit",methods:["GET","HEAD"],parameters:["ride"],bindings:{ride:"id"}},"dashboard.rides.update":{uri:"dashboard/rides/{ride}",methods:["PUT","PATCH"],parameters:["ride"]},"dashboard.rides.destroy":{uri:"dashboard/rides/{ride}",methods:["DELETE"],parameters:["ride"]},"dashboard.wallets.index":{uri:"dashboard/wallets",methods:["GET","HEAD"]},"dashboard.wallets.create":{uri:"dashboard/wallets/create",methods:["GET","HEAD"]},"dashboard.wallets.store":{uri:"dashboard/wallets",methods:["POST"]},"dashboard.wallets.show":{uri:"dashboard/wallets/{wallet}",methods:["GET","HEAD"],parameters:["wallet"],bindings:{wallet:"id"}},"dashboard.wallets.edit":{uri:"dashboard/wallets/{wallet}/edit",methods:["GET","HEAD"],parameters:["wallet"],bindings:{wallet:"id"}},"dashboard.wallets.update":{uri:"dashboard/wallets/{wallet}",methods:["PUT","PATCH"],parameters:["wallet"]},"dashboard.wallets.destroy":{uri:"dashboard/wallets/{wallet}",methods:["DELETE"],parameters:["wallet"]},"dashboard.drivers.show_driver":{uri:"dashboard/drivers/{driver}/show_driver",methods:["GET","HEAD"],parameters:["driver"]},"dashboard.drivers.approve_driver":{uri:"dashboard/drivers/{driver}/approve_driver",methods:["GET","HEAD"],parameters:["driver"]},"dashboard.drivers.reject_driver":{uri:"dashboard/drivers/reject_driver",methods:["POST"]},"dashboard.delete_object":{uri:"dashboard/delete-object",methods:["DELETE"]},"dashboard.chacnge_oblect_visibility":{uri:"dashboard/change-visibility-object/{objectId}/{objectType}/{columnName}",methods:["PUT"],parameters:["objectId","objectType","columnName"]},"dashboard.pricings.index":{uri:"dashboard/pricings",methods:["GET","HEAD"]},"dashboard.pricings.create":{uri:"dashboard/pricings/create",methods:["GET","HEAD"]},"dashboard.pricings.store":{uri:"dashboard/pricings",methods:["POST"]},"dashboard.pricings.show":{uri:"dashboard/pricings/{pricing}",methods:["GET","HEAD"],parameters:["pricing"],bindings:{pricing:"id"}},"dashboard.pricings.edit":{uri:"dashboard/pricings/{pricing}/edit",methods:["GET","HEAD"],parameters:["pricing"],bindings:{pricing:"id"}},"dashboard.pricings.update":{uri:"dashboard/pricings/{pricing}",methods:["PUT","PATCH"],parameters:["pricing"]},"dashboard.pricings.destroy":{uri:"dashboard/pricings/{pricing}",methods:["DELETE"],parameters:["pricing"]},"dashboard.settings.edit":{uri:"dashboard/settings/edit",methods:["GET","HEAD"]},"dashboard.settings.update":{uri:"dashboard/settings/update",methods:["POST"]},"dashboard.admins.index":{uri:"dashboard/admins",methods:["GET","HEAD"]},"dashboard.admins.create":{uri:"dashboard/admins/create",methods:["GET","HEAD"]},"dashboard.admins.store":{uri:"dashboard/admins",methods:["POST"]},"dashboard.admins.show":{uri:"dashboard/admins/{admin}",methods:["GET","HEAD"],parameters:["admin"],bindings:{admin:"id"}},"dashboard.admins.edit":{uri:"dashboard/admins/{admin}/edit",methods:["GET","HEAD"],parameters:["admin"],bindings:{admin:"id"}},"dashboard.admins.update":{uri:"dashboard/admins/{admin}",methods:["PUT","PATCH"],parameters:["admin"],bindings:{admin:"id"}},"dashboard.admins.destroy":{uri:"dashboard/admins/{admin}",methods:["DELETE"],parameters:["admin"]},"dashboard.transactions.index":{uri:"dashboard/transactions",methods:["GET","HEAD"]},"dashboard.transactions.create":{uri:"dashboard/transactions/create",methods:["GET","HEAD"]},"dashboard.transactions.store":{uri:"dashboard/transactions",methods:["POST"]},"dashboard.transactions.show":{uri:"dashboard/transactions/{transaction}",methods:["GET","HEAD"],parameters:["transaction"],bindings:{transaction:"id"}},"dashboard.transactions.edit":{uri:"dashboard/transactions/{transaction}/edit",methods:["GET","HEAD"],parameters:["transaction"],bindings:{transaction:"id"}},"dashboard.transactions.update":{uri:"dashboard/transactions/{transaction}",methods:["PUT","PATCH"],parameters:["transaction"]},"dashboard.transactions.destroy":{uri:"dashboard/transactions/{transaction}",methods:["DELETE"],parameters:["transaction"]},"api.":{uri:"api/notifications",methods:["GET","HEAD"]},"dashboard.notifications.create":{uri:"dashboard/notifications/create",methods:["GET","HEAD"]},"dashboard.notifications.store":{uri:"dashboard/notifications",methods:["POST"]},"dashboard.store.token":{uri:"dashboard/store-fcm-token",methods:["POST"]},"dashboard.notifications.seen":{uri:"dashboard/mark-notifications-seen",methods:["POST"]}}};typeof window<"u"&&typeof window.Ziggy<"u"&&Object.assign(r.routes,window.Ziggy.routes);export{r as Z};
