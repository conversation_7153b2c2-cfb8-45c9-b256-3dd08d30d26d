function Be(s){return s!==null&&typeof s=="object"&&"constructor"in s&&s.constructor===Object}function $e(s,e){s===void 0&&(s={}),e===void 0&&(e={}),Object.keys(e).forEach(t=>{typeof s[t]>"u"?s[t]=e[t]:Be(e[t])&&Be(s[t])&&Object.keys(e[t]).length>0&&$e(s[t],e[t])})}const Ve={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function V(){const s=typeof document<"u"?document:{};return $e(s,Ve),s}const Ue={document:Ve,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(s){return typeof setTimeout>"u"?(s(),null):setTimeout(s,0)},cancelAnimationFrame(s){typeof setTimeout>"u"||clearTimeout(s)}};function R(){const s=typeof window<"u"?window:{};return $e(s,Ue),s}function Q(s){return s===void 0&&(s=""),s.trim().split(" ").filter(e=>!!e.trim())}function Ke(s){const e=s;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function se(s,e){return e===void 0&&(e=0),setTimeout(s,e)}function U(){return Date.now()}function Ze(s){const e=R();let t;return e.getComputedStyle&&(t=e.getComputedStyle(s,null)),!t&&s.currentStyle&&(t=s.currentStyle),t||(t=s.style),t}function Ie(s,e){e===void 0&&(e="x");const t=R();let i,a,r;const f=Ze(s);return t.WebKitCSSMatrix?(a=f.transform||f.webkitTransform,a.split(",").length>6&&(a=a.split(", ").map(c=>c.replace(",",".")).join(", ")),r=new t.WebKitCSSMatrix(a==="none"?"":a)):(r=f.MozTransform||f.OTransform||f.MsTransform||f.msTransform||f.transform||f.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=r.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?a=r.m41:i.length===16?a=parseFloat(i[12]):a=parseFloat(i[4])),e==="y"&&(t.WebKitCSSMatrix?a=r.m42:i.length===16?a=parseFloat(i[13]):a=parseFloat(i[5])),a||0}function oe(s){return typeof s=="object"&&s!==null&&s.constructor&&Object.prototype.toString.call(s).slice(8,-1)==="Object"}function Qe(s){return typeof window<"u"&&typeof window.HTMLElement<"u"?s instanceof HTMLElement:s&&(s.nodeType===1||s.nodeType===11)}function _(){const s=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const i=t<0||arguments.length<=t?void 0:arguments[t];if(i!=null&&!Qe(i)){const a=Object.keys(Object(i)).filter(r=>e.indexOf(r)<0);for(let r=0,f=a.length;r<f;r+=1){const c=a[r],n=Object.getOwnPropertyDescriptor(i,c);n!==void 0&&n.enumerable&&(oe(s[c])&&oe(i[c])?i[c].__swiper__?s[c]=i[c]:_(s[c],i[c]):!oe(s[c])&&oe(i[c])?(s[c]={},i[c].__swiper__?s[c]=i[c]:_(s[c],i[c])):s[c]=i[c])}}}return s}function de(s,e,t){s.style.setProperty(e,t)}function Fe(s){let{swiper:e,targetPosition:t,side:i}=s;const a=R(),r=-e.translate;let f=null,c;const n=e.params.speed;e.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(e.cssModeFrameID);const p=t>r?"next":"prev",o=(d,m)=>p==="next"&&d>=m||p==="prev"&&d<=m,l=()=>{c=new Date().getTime(),f===null&&(f=c);const d=Math.max(Math.min((c-f)/n,1),0),m=.5-Math.cos(d*Math.PI)/2;let h=r+m*(t-r);if(o(h,t)&&(h=t),e.wrapperEl.scrollTo({[i]:h}),o(h,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:h})}),a.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=a.requestAnimationFrame(l)};l()}function ie(s){return s.querySelector(".swiper-slide-transform")||s.shadowRoot&&s.shadowRoot.querySelector(".swiper-slide-transform")||s}function Y(s,e){return e===void 0&&(e=""),[...s.children].filter(t=>t.matches(e))}function ge(s){try{console.warn(s);return}catch{}}function q(s,e){e===void 0&&(e=[]);const t=document.createElement(s);return t.classList.add(...Array.isArray(e)?e:Q(e)),t}function ve(s){const e=R(),t=V(),i=s.getBoundingClientRect(),a=t.body,r=s.clientTop||a.clientTop||0,f=s.clientLeft||a.clientLeft||0,c=s===e?e.scrollY:s.scrollTop,n=s===e?e.scrollX:s.scrollLeft;return{top:i.top+c-r,left:i.left+n-f}}function Je(s,e){const t=[];for(;s.previousElementSibling;){const i=s.previousElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function et(s,e){const t=[];for(;s.nextElementSibling;){const i=s.nextElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function J(s,e){return R().getComputedStyle(s,null).getPropertyValue(e)}function fe(s){let e=s,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function te(s,e){const t=[];let i=s.parentElement;for(;i;)e?i.matches(e)&&t.push(i):t.push(i),i=i.parentElement;return t}function ce(s,e){function t(i){i.target===s&&(e.call(s,i),s.removeEventListener("transitionend",t))}e&&s.addEventListener("transitionend",t)}function ze(s,e,t){const i=R();return s[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function B(s){return(Array.isArray(s)?s:[s]).filter(e=>!!e)}let xe;function tt(){const s=R(),e=V();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in s||s.DocumentTouch&&e instanceof s.DocumentTouch)}}function We(){return xe||(xe=tt()),xe}let Te;function st(s){let{userAgent:e}=s===void 0?{}:s;const t=We(),i=R(),a=i.navigator.platform,r=e||i.navigator.userAgent,f={ios:!1,android:!1},c=i.screen.width,n=i.screen.height,p=r.match(/(Android);?[\s\/]+([\d.]+)?/);let o=r.match(/(iPad).*OS\s([\d_]+)/);const l=r.match(/(iPod)(.*OS\s([\d_]+))?/),d=!o&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m=a==="Win32";let h=a==="MacIntel";const w=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!o&&h&&t.touch&&w.indexOf(`${c}x${n}`)>=0&&(o=r.match(/(Version)\/([\d.]+)/),o||(o=[0,1,"13_0_0"]),h=!1),p&&!m&&(f.os="android",f.android=!0),(o||d||l)&&(f.os="ios",f.ios=!0),f}function _e(s){return s===void 0&&(s={}),Te||(Te=st(s)),Te}let Me;function it(){const s=R(),e=_e();let t=!1;function i(){const c=s.navigator.userAgent.toLowerCase();return c.indexOf("safari")>=0&&c.indexOf("chrome")<0&&c.indexOf("android")<0}if(i()){const c=String(s.navigator.userAgent);if(c.includes("Version/")){const[n,p]=c.split("Version/")[1].split(" ")[0].split(".").map(o=>Number(o));t=n<16||n===16&&p<2}}const a=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(s.navigator.userAgent),r=i(),f=r||a&&e.ios;return{isSafari:t||r,needPerspectiveFix:t,need3dFix:f,isWebView:a}}function rt(){return Me||(Me=it()),Me}function at(s){let{swiper:e,on:t,emit:i}=s;const a=R();let r=null,f=null;const c=()=>{!e||e.destroyed||!e.initialized||(i("beforeResize"),i("resize"))},n=()=>{!e||e.destroyed||!e.initialized||(r=new ResizeObserver(l=>{f=a.requestAnimationFrame(()=>{const{width:d,height:m}=e;let h=d,w=m;l.forEach(g=>{let{contentBoxSize:u,contentRect:y,target:v}=g;v&&v!==e.el||(h=y?y.width:(u[0]||u).inlineSize,w=y?y.height:(u[0]||u).blockSize)}),(h!==d||w!==m)&&c()})}),r.observe(e.el))},p=()=>{f&&a.cancelAnimationFrame(f),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null)},o=()=>{!e||e.destroyed||!e.initialized||i("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof a.ResizeObserver<"u"){n();return}a.addEventListener("resize",c),a.addEventListener("orientationchange",o)}),t("destroy",()=>{p(),a.removeEventListener("resize",c),a.removeEventListener("orientationchange",o)})}function nt(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r=[],f=R(),c=function(o,l){l===void 0&&(l={});const d=f.MutationObserver||f.WebkitMutationObserver,m=new d(h=>{if(e.__preventObserver__)return;if(h.length===1){a("observerUpdate",h[0]);return}const w=function(){a("observerUpdate",h[0])};f.requestAnimationFrame?f.requestAnimationFrame(w):f.setTimeout(w,0)});m.observe(o,{attributes:typeof l.attributes>"u"?!0:l.attributes,childList:typeof l.childList>"u"?!0:l.childList,characterData:typeof l.characterData>"u"?!0:l.characterData}),r.push(m)},n=()=>{if(e.params.observer){if(e.params.observeParents){const o=te(e.hostEl);for(let l=0;l<o.length;l+=1)c(o[l])}c(e.hostEl,{childList:e.params.observeSlideChildren}),c(e.wrapperEl,{attributes:!1})}},p=()=>{r.forEach(o=>{o.disconnect()}),r.splice(0,r.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",n),i("destroy",p)}var lt={on(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;const a=t?"unshift":"push";return s.split(" ").forEach(r=>{i.eventsListeners[r]||(i.eventsListeners[r]=[]),i.eventsListeners[r][a](e)}),i},once(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;function a(){i.off(s,a),a.__emitterProxy&&delete a.__emitterProxy;for(var r=arguments.length,f=new Array(r),c=0;c<r;c++)f[c]=arguments[c];e.apply(i,f)}return a.__emitterProxy=e,i.on(s,a,t)},onAny(s,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof s!="function")return t;const i=e?"unshift":"push";return t.eventsAnyListeners.indexOf(s)<0&&t.eventsAnyListeners[i](s),t},offAny(s){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(s);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(s,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||s.split(" ").forEach(i=>{typeof e>"u"?t.eventsListeners[i]=[]:t.eventsListeners[i]&&t.eventsListeners[i].forEach((a,r)=>{(a===e||a.__emitterProxy&&a.__emitterProxy===e)&&t.eventsListeners[i].splice(r,1)})}),t},emit(){const s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;let e,t,i;for(var a=arguments.length,r=new Array(a),f=0;f<a;f++)r[f]=arguments[f];return typeof r[0]=="string"||Array.isArray(r[0])?(e=r[0],t=r.slice(1,r.length),i=s):(e=r[0].events,t=r[0].data,i=r[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(n=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(p=>{p.apply(i,[n,...t])}),s.eventsListeners&&s.eventsListeners[n]&&s.eventsListeners[n].forEach(p=>{p.apply(i,t)})}),s}};function ot(){const s=this;let e,t;const i=s.el;typeof s.params.width<"u"&&s.params.width!==null?e=s.params.width:e=i.clientWidth,typeof s.params.height<"u"&&s.params.height!==null?t=s.params.height:t=i.clientHeight,!(e===0&&s.isHorizontal()||t===0&&s.isVertical())&&(e=e-parseInt(J(i,"padding-left")||0,10)-parseInt(J(i,"padding-right")||0,10),t=t-parseInt(J(i,"padding-top")||0,10)-parseInt(J(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(s,{width:e,height:t,size:s.isHorizontal()?e:t}))}function dt(){const s=this;function e(T,z){return parseFloat(T.getPropertyValue(s.getDirectionLabel(z))||0)}const t=s.params,{wrapperEl:i,slidesEl:a,size:r,rtlTranslate:f,wrongRTL:c}=s,n=s.virtual&&t.virtual.enabled,p=n?s.virtual.slides.length:s.slides.length,o=Y(a,`.${s.params.slideClass}, swiper-slide`),l=n?s.virtual.slides.length:o.length;let d=[];const m=[],h=[];let w=t.slidesOffsetBefore;typeof w=="function"&&(w=t.slidesOffsetBefore.call(s));let g=t.slidesOffsetAfter;typeof g=="function"&&(g=t.slidesOffsetAfter.call(s));const u=s.snapGrid.length,y=s.slidesGrid.length;let v=t.spaceBetween,E=-w,P=0,I=0;if(typeof r>"u")return;typeof v=="string"&&v.indexOf("%")>=0?v=parseFloat(v.replace("%",""))/100*r:typeof v=="string"&&(v=parseFloat(v)),s.virtualSize=-v,o.forEach(T=>{f?T.style.marginLeft="":T.style.marginRight="",T.style.marginBottom="",T.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(de(i,"--swiper-centered-offset-before",""),de(i,"--swiper-centered-offset-after",""));const O=t.grid&&t.grid.rows>1&&s.grid;O?s.grid.initSlides(o):s.grid&&s.grid.unsetSlides();let M;const A=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(T=>typeof t.breakpoints[T].slidesPerView<"u").length>0;for(let T=0;T<l;T+=1){M=0;let z;if(o[T]&&(z=o[T]),O&&s.grid.updateSlide(T,z,o),!(o[T]&&J(z,"display")==="none")){if(t.slidesPerView==="auto"){A&&(o[T].style[s.getDirectionLabel("width")]="");const x=getComputedStyle(z),b=z.style.transform,S=z.style.webkitTransform;if(b&&(z.style.transform="none"),S&&(z.style.webkitTransform="none"),t.roundLengths)M=s.isHorizontal()?ze(z,"width"):ze(z,"height");else{const C=e(x,"width"),k=e(x,"padding-left"),D=e(x,"padding-right"),L=e(x,"margin-left"),$=e(x,"margin-right"),G=x.getPropertyValue("box-sizing");if(G&&G==="border-box")M=C+L+$;else{const{clientWidth:N,offsetWidth:F}=z;M=C+k+D+L+$+(F-N)}}b&&(z.style.transform=b),S&&(z.style.webkitTransform=S),t.roundLengths&&(M=Math.floor(M))}else M=(r-(t.slidesPerView-1)*v)/t.slidesPerView,t.roundLengths&&(M=Math.floor(M)),o[T]&&(o[T].style[s.getDirectionLabel("width")]=`${M}px`);o[T]&&(o[T].swiperSlideSize=M),h.push(M),t.centeredSlides?(E=E+M/2+P/2+v,P===0&&T!==0&&(E=E-r/2-v),T===0&&(E=E-r/2-v),Math.abs(E)<1/1e3&&(E=0),t.roundLengths&&(E=Math.floor(E)),I%t.slidesPerGroup===0&&d.push(E),m.push(E)):(t.roundLengths&&(E=Math.floor(E)),(I-Math.min(s.params.slidesPerGroupSkip,I))%s.params.slidesPerGroup===0&&d.push(E),m.push(E),E=E+M+v),s.virtualSize+=M+v,P=M,I+=1}}if(s.virtualSize=Math.max(s.virtualSize,r)+g,f&&c&&(t.effect==="slide"||t.effect==="coverflow")&&(i.style.width=`${s.virtualSize+v}px`),t.setWrapperSize&&(i.style[s.getDirectionLabel("width")]=`${s.virtualSize+v}px`),O&&s.grid.updateWrapperSize(M,d),!t.centeredSlides){const T=[];for(let z=0;z<d.length;z+=1){let x=d[z];t.roundLengths&&(x=Math.floor(x)),d[z]<=s.virtualSize-r&&T.push(x)}d=T,Math.floor(s.virtualSize-r)-Math.floor(d[d.length-1])>1&&d.push(s.virtualSize-r)}if(n&&t.loop){const T=h[0]+v;if(t.slidesPerGroup>1){const z=Math.ceil((s.virtual.slidesBefore+s.virtual.slidesAfter)/t.slidesPerGroup),x=T*t.slidesPerGroup;for(let b=0;b<z;b+=1)d.push(d[d.length-1]+x)}for(let z=0;z<s.virtual.slidesBefore+s.virtual.slidesAfter;z+=1)t.slidesPerGroup===1&&d.push(d[d.length-1]+T),m.push(m[m.length-1]+T),s.virtualSize+=T}if(d.length===0&&(d=[0]),v!==0){const T=s.isHorizontal()&&f?"marginLeft":s.getDirectionLabel("marginRight");o.filter((z,x)=>!t.cssMode||t.loop?!0:x!==o.length-1).forEach(z=>{z.style[T]=`${v}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let T=0;h.forEach(x=>{T+=x+(v||0)}),T-=v;const z=T-r;d=d.map(x=>x<=0?-w:x>z?z+g:x)}if(t.centerInsufficientSlides){let T=0;if(h.forEach(z=>{T+=z+(v||0)}),T-=v,T<r){const z=(r-T)/2;d.forEach((x,b)=>{d[b]=x-z}),m.forEach((x,b)=>{m[b]=x+z})}}if(Object.assign(s,{slides:o,snapGrid:d,slidesGrid:m,slidesSizesGrid:h}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){de(i,"--swiper-centered-offset-before",`${-d[0]}px`),de(i,"--swiper-centered-offset-after",`${s.size/2-h[h.length-1]/2}px`);const T=-s.snapGrid[0],z=-s.slidesGrid[0];s.snapGrid=s.snapGrid.map(x=>x+T),s.slidesGrid=s.slidesGrid.map(x=>x+z)}if(l!==p&&s.emit("slidesLengthChange"),d.length!==u&&(s.params.watchOverflow&&s.checkOverflow(),s.emit("snapGridLengthChange")),m.length!==y&&s.emit("slidesGridLengthChange"),t.watchSlidesProgress&&s.updateSlidesOffset(),s.emit("slidesUpdated"),!n&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const T=`${t.containerModifierClass}backface-hidden`,z=s.el.classList.contains(T);l<=t.maxBackfaceHiddenSlides?z||s.el.classList.add(T):z&&s.el.classList.remove(T)}}function ct(s){const e=this,t=[],i=e.virtual&&e.params.virtual.enabled;let a=0,r;typeof s=="number"?e.setTransition(s):s===!0&&e.setTransition(e.params.speed);const f=c=>i?e.slides[e.getSlideIndexByData(c)]:e.slides[c];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(c=>{t.push(c)});else for(r=0;r<Math.ceil(e.params.slidesPerView);r+=1){const c=e.activeIndex+r;if(c>e.slides.length&&!i)break;t.push(f(c))}else t.push(f(e.activeIndex));for(r=0;r<t.length;r+=1)if(typeof t[r]<"u"){const c=t[r].offsetHeight;a=c>a?c:a}(a||a===0)&&(e.wrapperEl.style.height=`${a}px`)}function ft(){const s=this,e=s.slides,t=s.isElement?s.isHorizontal()?s.wrapperEl.offsetLeft:s.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(s.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-s.cssOverflowAdjustment()}function ut(s){s===void 0&&(s=this&&this.translate||0);const e=this,t=e.params,{slides:i,rtlTranslate:a,snapGrid:r}=e;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let f=-s;a&&(f=s),i.forEach(n=>{n.classList.remove(t.slideVisibleClass,t.slideFullyVisibleClass)}),e.visibleSlidesIndexes=[],e.visibleSlides=[];let c=t.spaceBetween;typeof c=="string"&&c.indexOf("%")>=0?c=parseFloat(c.replace("%",""))/100*e.size:typeof c=="string"&&(c=parseFloat(c));for(let n=0;n<i.length;n+=1){const p=i[n];let o=p.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);const l=(f+(t.centeredSlides?e.minTranslate():0)-o)/(p.swiperSlideSize+c),d=(f-r[0]+(t.centeredSlides?e.minTranslate():0)-o)/(p.swiperSlideSize+c),m=-(f-o),h=m+e.slidesSizesGrid[n],w=m>=0&&m<=e.size-e.slidesSizesGrid[n];(m>=0&&m<e.size-1||h>1&&h<=e.size||m<=0&&h>=e.size)&&(e.visibleSlides.push(p),e.visibleSlidesIndexes.push(n),i[n].classList.add(t.slideVisibleClass)),w&&i[n].classList.add(t.slideFullyVisibleClass),p.progress=a?-l:l,p.originalProgress=a?-d:d}}function pt(s){const e=this;if(typeof s>"u"){const o=e.rtlTranslate?-1:1;s=e&&e.translate&&e.translate*o||0}const t=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:a,isBeginning:r,isEnd:f,progressLoop:c}=e;const n=r,p=f;if(i===0)a=0,r=!0,f=!0;else{a=(s-e.minTranslate())/i;const o=Math.abs(s-e.minTranslate())<1,l=Math.abs(s-e.maxTranslate())<1;r=o||a<=0,f=l||a>=1,o&&(a=0),l&&(a=1)}if(t.loop){const o=e.getSlideIndexByData(0),l=e.getSlideIndexByData(e.slides.length-1),d=e.slidesGrid[o],m=e.slidesGrid[l],h=e.slidesGrid[e.slidesGrid.length-1],w=Math.abs(s);w>=d?c=(w-d)/h:c=(w+h-m)/h,c>1&&(c-=1)}Object.assign(e,{progress:a,progressLoop:c,isBeginning:r,isEnd:f}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(s),r&&!n&&e.emit("reachBeginning toEdge"),f&&!p&&e.emit("reachEnd toEdge"),(n&&!r||p&&!f)&&e.emit("fromEdge"),e.emit("progress",a)}function mt(){const s=this,{slides:e,params:t,slidesEl:i,activeIndex:a}=s,r=s.virtual&&t.virtual.enabled,f=s.grid&&t.grid&&t.grid.rows>1,c=l=>Y(i,`.${t.slideClass}${l}, swiper-slide${l}`)[0];e.forEach(l=>{l.classList.remove(t.slideActiveClass,t.slideNextClass,t.slidePrevClass)});let n,p,o;if(r)if(t.loop){let l=a-s.virtual.slidesBefore;l<0&&(l=s.virtual.slides.length+l),l>=s.virtual.slides.length&&(l-=s.virtual.slides.length),n=c(`[data-swiper-slide-index="${l}"]`)}else n=c(`[data-swiper-slide-index="${a}"]`);else f?(n=e.filter(l=>l.column===a)[0],o=e.filter(l=>l.column===a+1)[0],p=e.filter(l=>l.column===a-1)[0]):n=e[a];n&&(n.classList.add(t.slideActiveClass),f?(o&&o.classList.add(t.slideNextClass),p&&p.classList.add(t.slidePrevClass)):(o=et(n,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!o&&(o=e[0]),o&&o.classList.add(t.slideNextClass),p=Je(n,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!p===0&&(p=e[e.length-1]),p&&p.classList.add(t.slidePrevClass))),s.emitSlidesClasses()}const he=(s,e)=>{if(!s||s.destroyed||!s.params)return;const t=()=>s.isElement?"swiper-slide":`.${s.params.slideClass}`,i=e.closest(t());if(i){let a=i.querySelector(`.${s.params.lazyPreloaderClass}`);!a&&s.isElement&&(i.shadowRoot?a=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(a=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`),a&&a.remove())})),a&&a.remove()}},Ce=(s,e)=>{if(!s.slides[e])return;const t=s.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},Ae=s=>{if(!s||s.destroyed||!s.params)return;let e=s.params.lazyPreloadPrevNext;const t=s.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const i=s.params.slidesPerView==="auto"?s.slidesPerViewDynamic():Math.ceil(s.params.slidesPerView),a=s.activeIndex;if(s.params.grid&&s.params.grid.rows>1){const f=a,c=[f-e];c.push(...Array.from({length:e}).map((n,p)=>f+i+p)),s.slides.forEach((n,p)=>{c.includes(n.column)&&Ce(s,p)});return}const r=a+i-1;if(s.params.rewind||s.params.loop)for(let f=a-e;f<=r+e;f+=1){const c=(f%t+t)%t;(c<a||c>r)&&Ce(s,c)}else for(let f=Math.max(a-e,0);f<=Math.min(r+e,t-1);f+=1)f!==a&&(f>r||f<a)&&Ce(s,f)};function ht(s){const{slidesGrid:e,params:t}=s,i=s.rtlTranslate?s.translate:-s.translate;let a;for(let r=0;r<e.length;r+=1)typeof e[r+1]<"u"?i>=e[r]&&i<e[r+1]-(e[r+1]-e[r])/2?a=r:i>=e[r]&&i<e[r+1]&&(a=r+1):i>=e[r]&&(a=r);return t.normalizeSlideIndex&&(a<0||typeof a>"u")&&(a=0),a}function gt(s){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:a,activeIndex:r,realIndex:f,snapIndex:c}=e;let n=s,p;const o=m=>{let h=m-e.virtual.slidesBefore;return h<0&&(h=e.virtual.slides.length+h),h>=e.virtual.slides.length&&(h-=e.virtual.slides.length),h};if(typeof n>"u"&&(n=ht(e)),i.indexOf(t)>=0)p=i.indexOf(t);else{const m=Math.min(a.slidesPerGroupSkip,n);p=m+Math.floor((n-m)/a.slidesPerGroup)}if(p>=i.length&&(p=i.length-1),n===r&&!e.params.loop){p!==c&&(e.snapIndex=p,e.emit("snapIndexChange"));return}if(n===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=o(n);return}const l=e.grid&&a.grid&&a.grid.rows>1;let d;if(e.virtual&&a.virtual.enabled&&a.loop)d=o(n);else if(l){const m=e.slides.filter(w=>w.column===n)[0];let h=parseInt(m.getAttribute("data-swiper-slide-index"),10);Number.isNaN(h)&&(h=Math.max(e.slides.indexOf(m),0)),d=Math.floor(h/a.grid.rows)}else if(e.slides[n]){const m=e.slides[n].getAttribute("data-swiper-slide-index");m?d=parseInt(m,10):d=n}else d=n;Object.assign(e,{previousSnapIndex:c,snapIndex:p,previousRealIndex:f,realIndex:d,previousIndex:r,activeIndex:n}),e.initialized&&Ae(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(f!==d&&e.emit("realIndexChange"),e.emit("slideChange"))}function vt(s,e){const t=this,i=t.params;let a=s.closest(`.${i.slideClass}, swiper-slide`);!a&&t.isElement&&e&&e.length>1&&e.includes(s)&&[...e.slice(e.indexOf(s)+1,e.length)].forEach(c=>{!a&&c.matches&&c.matches(`.${i.slideClass}, swiper-slide`)&&(a=c)});let r=!1,f;if(a){for(let c=0;c<t.slides.length;c+=1)if(t.slides[c]===a){r=!0,f=c;break}}if(a&&r)t.clickedSlide=a,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(a.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=f;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}i.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var wt={updateSize:ot,updateSlides:dt,updateAutoHeight:ct,updateSlidesOffset:ft,updateSlidesProgress:ut,updateProgress:pt,updateSlidesClasses:mt,updateActiveIndex:gt,updateClickedSlide:vt};function yt(s){s===void 0&&(s=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:i,translate:a,wrapperEl:r}=e;if(t.virtualTranslate)return i?-a:a;if(t.cssMode)return a;let f=Ie(r,s);return f+=e.cssOverflowAdjustment(),i&&(f=-f),f||0}function bt(s,e){const t=this,{rtlTranslate:i,params:a,wrapperEl:r,progress:f}=t;let c=0,n=0;const p=0;t.isHorizontal()?c=i?-s:s:n=s,a.roundLengths&&(c=Math.floor(c),n=Math.floor(n)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?c:n,a.cssMode?r[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-c:-n:a.virtualTranslate||(t.isHorizontal()?c-=t.cssOverflowAdjustment():n-=t.cssOverflowAdjustment(),r.style.transform=`translate3d(${c}px, ${n}px, ${p}px)`);let o;const l=t.maxTranslate()-t.minTranslate();l===0?o=0:o=(s-t.minTranslate())/l,o!==f&&t.updateProgress(s),t.emit("setTranslate",t.translate,e)}function St(){return-this.snapGrid[0]}function Et(){return-this.snapGrid[this.snapGrid.length-1]}function xt(s,e,t,i,a){s===void 0&&(s=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),i===void 0&&(i=!0);const r=this,{params:f,wrapperEl:c}=r;if(r.animating&&f.preventInteractionOnTransition)return!1;const n=r.minTranslate(),p=r.maxTranslate();let o;if(i&&s>n?o=n:i&&s<p?o=p:o=s,r.updateProgress(o),f.cssMode){const l=r.isHorizontal();if(e===0)c[l?"scrollLeft":"scrollTop"]=-o;else{if(!r.support.smoothScroll)return Fe({swiper:r,targetPosition:-o,side:l?"left":"top"}),!0;c.scrollTo({[l?"left":"top"]:-o,behavior:"smooth"})}return!0}return e===0?(r.setTransition(0),r.setTranslate(o),t&&(r.emit("beforeTransitionStart",e,a),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(o),t&&(r.emit("beforeTransitionStart",e,a),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(d){!r||r.destroyed||d.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,t&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var Tt={getTranslate:yt,setTranslate:bt,minTranslate:St,maxTranslate:Et,translateTo:xt};function Mt(s,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${s}ms`,t.wrapperEl.style.transitionDelay=s===0?"0ms":""),t.emit("setTransition",s,e)}function qe(s){let{swiper:e,runCallbacks:t,direction:i,step:a}=s;const{activeIndex:r,previousIndex:f}=e;let c=i;if(c||(r>f?c="next":r<f?c="prev":c="reset"),e.emit(`transition${a}`),t&&r!==f){if(c==="reset"){e.emit(`slideResetTransition${a}`);return}e.emit(`slideChangeTransition${a}`),c==="next"?e.emit(`slideNextTransition${a}`):e.emit(`slidePrevTransition${a}`)}}function Ct(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;i.cssMode||(i.autoHeight&&t.updateAutoHeight(),qe({swiper:t,runCallbacks:s,direction:e,step:"Start"}))}function Pt(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;t.animating=!1,!i.cssMode&&(t.setTransition(0),qe({swiper:t,runCallbacks:s,direction:e,step:"End"}))}var Lt={setTransition:Mt,transitionStart:Ct,transitionEnd:Pt};function It(s,e,t,i,a){s===void 0&&(s=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const r=this;let f=s;f<0&&(f=0);const{params:c,snapGrid:n,slidesGrid:p,previousIndex:o,activeIndex:l,rtlTranslate:d,wrapperEl:m,enabled:h}=r;if(r.animating&&c.preventInteractionOnTransition||!h&&!i&&!a||r.destroyed)return!1;const w=Math.min(r.params.slidesPerGroupSkip,f);let g=w+Math.floor((f-w)/r.params.slidesPerGroup);g>=n.length&&(g=n.length-1);const u=-n[g];if(c.normalizeSlideIndex)for(let v=0;v<p.length;v+=1){const E=-Math.floor(u*100),P=Math.floor(p[v]*100),I=Math.floor(p[v+1]*100);typeof p[v+1]<"u"?E>=P&&E<I-(I-P)/2?f=v:E>=P&&E<I&&(f=v+1):E>=P&&(f=v)}if(r.initialized&&f!==l&&(!r.allowSlideNext&&(d?u>r.translate&&u>r.minTranslate():u<r.translate&&u<r.minTranslate())||!r.allowSlidePrev&&u>r.translate&&u>r.maxTranslate()&&(l||0)!==f))return!1;f!==(o||0)&&t&&r.emit("beforeSlideChangeStart"),r.updateProgress(u);let y;if(f>l?y="next":f<l?y="prev":y="reset",d&&-u===r.translate||!d&&u===r.translate)return r.updateActiveIndex(f),c.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),c.effect!=="slide"&&r.setTranslate(u),y!=="reset"&&(r.transitionStart(t,y),r.transitionEnd(t,y)),!1;if(c.cssMode){const v=r.isHorizontal(),E=d?u:-u;if(e===0){const P=r.virtual&&r.params.virtual.enabled;P&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),P&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[v?"scrollLeft":"scrollTop"]=E})):m[v?"scrollLeft":"scrollTop"]=E,P&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1})}else{if(!r.support.smoothScroll)return Fe({swiper:r,targetPosition:E,side:v?"left":"top"}),!0;m.scrollTo({[v?"left":"top"]:E,behavior:"smooth"})}return!0}return r.setTransition(e),r.setTranslate(u),r.updateActiveIndex(f),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,i),r.transitionStart(t,y),e===0?r.transitionEnd(t,y):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(E){!r||r.destroyed||E.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(t,y))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function zt(s,e,t,i){s===void 0&&(s=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const a=this;if(a.destroyed)return;const r=a.grid&&a.params.grid&&a.params.grid.rows>1;let f=s;if(a.params.loop)if(a.virtual&&a.params.virtual.enabled)f=f+a.virtual.slidesBefore;else{let c;if(r){const d=f*a.params.grid.rows;c=a.slides.filter(m=>m.getAttribute("data-swiper-slide-index")*1===d)[0].column}else c=a.getSlideIndexByData(f);const n=r?Math.ceil(a.slides.length/a.params.grid.rows):a.slides.length,{centeredSlides:p}=a.params;let o=a.params.slidesPerView;o==="auto"?o=a.slidesPerViewDynamic():(o=Math.ceil(parseFloat(a.params.slidesPerView,10)),p&&o%2===0&&(o=o+1));let l=n-c<o;if(p&&(l=l||c<Math.ceil(o/2)),l){const d=p?c<a.activeIndex?"prev":"next":c-a.activeIndex-1<a.params.slidesPerView?"next":"prev";a.loopFix({direction:d,slideTo:!0,activeSlideIndex:d==="next"?c+1:c-n+1,slideRealIndex:d==="next"?a.realIndex:void 0})}if(r){const d=f*a.params.grid.rows;f=a.slides.filter(m=>m.getAttribute("data-swiper-slide-index")*1===d)[0].column}else f=a.getSlideIndexByData(f)}return requestAnimationFrame(()=>{a.slideTo(f,e,t,i)}),a}function At(s,e,t){s===void 0&&(s=this.params.speed),e===void 0&&(e=!0);const i=this,{enabled:a,params:r,animating:f}=i;if(!a||i.destroyed)return i;let c=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(c=Math.max(i.slidesPerViewDynamic("current",!0),1));const n=i.activeIndex<r.slidesPerGroupSkip?1:c,p=i.virtual&&r.virtual.enabled;if(r.loop){if(f&&!p&&r.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+n,s,e,t)}),!0}return r.rewind&&i.isEnd?i.slideTo(0,s,e,t):i.slideTo(i.activeIndex+n,s,e,t)}function $t(s,e,t){s===void 0&&(s=this.params.speed),e===void 0&&(e=!0);const i=this,{params:a,snapGrid:r,slidesGrid:f,rtlTranslate:c,enabled:n,animating:p}=i;if(!n||i.destroyed)return i;const o=i.virtual&&a.virtual.enabled;if(a.loop){if(p&&!o&&a.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const l=c?i.translate:-i.translate;function d(u){return u<0?-Math.floor(Math.abs(u)):Math.floor(u)}const m=d(l),h=r.map(u=>d(u));let w=r[h.indexOf(m)-1];if(typeof w>"u"&&a.cssMode){let u;r.forEach((y,v)=>{m>=y&&(u=v)}),typeof u<"u"&&(w=r[u>0?u-1:u])}let g=0;if(typeof w<"u"&&(g=f.indexOf(w),g<0&&(g=i.activeIndex-1),a.slidesPerView==="auto"&&a.slidesPerGroup===1&&a.slidesPerGroupAuto&&(g=g-i.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),a.rewind&&i.isBeginning){const u=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(u,s,e,t)}else if(a.loop&&i.activeIndex===0&&a.cssMode)return requestAnimationFrame(()=>{i.slideTo(g,s,e,t)}),!0;return i.slideTo(g,s,e,t)}function Dt(s,e,t){s===void 0&&(s=this.params.speed),e===void 0&&(e=!0);const i=this;if(!i.destroyed)return i.slideTo(i.activeIndex,s,e,t)}function Ot(s,e,t,i){s===void 0&&(s=this.params.speed),e===void 0&&(e=!0),i===void 0&&(i=.5);const a=this;if(a.destroyed)return;let r=a.activeIndex;const f=Math.min(a.params.slidesPerGroupSkip,r),c=f+Math.floor((r-f)/a.params.slidesPerGroup),n=a.rtlTranslate?a.translate:-a.translate;if(n>=a.snapGrid[c]){const p=a.snapGrid[c],o=a.snapGrid[c+1];n-p>(o-p)*i&&(r+=a.params.slidesPerGroup)}else{const p=a.snapGrid[c-1],o=a.snapGrid[c];n-p<=(o-p)*i&&(r-=a.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,a.slidesGrid.length-1),a.slideTo(r,s,e,t)}function kt(){const s=this;if(s.destroyed)return;const{params:e,slidesEl:t}=s,i=e.slidesPerView==="auto"?s.slidesPerViewDynamic():e.slidesPerView;let a=s.clickedIndex,r;const f=s.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(s.animating)return;r=parseInt(s.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?a<s.loopedSlides-i/2||a>s.slides.length-s.loopedSlides+i/2?(s.loopFix(),a=s.getSlideIndex(Y(t,`${f}[data-swiper-slide-index="${r}"]`)[0]),se(()=>{s.slideTo(a)})):s.slideTo(a):a>s.slides.length-i?(s.loopFix(),a=s.getSlideIndex(Y(t,`${f}[data-swiper-slide-index="${r}"]`)[0]),se(()=>{s.slideTo(a)})):s.slideTo(a)}else s.slideTo(a)}var Gt={slideTo:It,slideToLoop:zt,slideNext:At,slidePrev:$t,slideReset:Dt,slideToClosest:Ot,slideToClickedSlide:kt};function Ht(s){const e=this,{params:t,slidesEl:i}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;const a=()=>{Y(i,`.${t.slideClass}, swiper-slide`).forEach((l,d)=>{l.setAttribute("data-swiper-slide-index",d)})},r=e.grid&&t.grid&&t.grid.rows>1,f=t.slidesPerGroup*(r?t.grid.rows:1),c=e.slides.length%f!==0,n=r&&e.slides.length%t.grid.rows!==0,p=o=>{for(let l=0;l<o;l+=1){const d=e.isElement?q("swiper-slide",[t.slideBlankClass]):q("div",[t.slideClass,t.slideBlankClass]);e.slidesEl.append(d)}};if(c){if(t.loopAddBlankSlides){const o=f-e.slides.length%f;p(o),e.recalcSlides(),e.updateSlides()}else ge("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else if(n){if(t.loopAddBlankSlides){const o=t.grid.rows-e.slides.length%t.grid.rows;p(o),e.recalcSlides(),e.updateSlides()}else ge("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else a();e.loopFix({slideRealIndex:s,direction:t.centeredSlides?void 0:"next"})}function Bt(s){let{slideRealIndex:e,slideTo:t=!0,direction:i,setTranslate:a,activeSlideIndex:r,byController:f,byMousewheel:c}=s===void 0?{}:s;const n=this;if(!n.params.loop)return;n.emit("beforeLoopFix");const{slides:p,allowSlidePrev:o,allowSlideNext:l,slidesEl:d,params:m}=n,{centeredSlides:h}=m;if(n.allowSlidePrev=!0,n.allowSlideNext=!0,n.virtual&&m.virtual.enabled){t&&(!m.centeredSlides&&n.snapIndex===0?n.slideTo(n.virtual.slides.length,0,!1,!0):m.centeredSlides&&n.snapIndex<m.slidesPerView?n.slideTo(n.virtual.slides.length+n.snapIndex,0,!1,!0):n.snapIndex===n.snapGrid.length-1&&n.slideTo(n.virtual.slidesBefore,0,!1,!0)),n.allowSlidePrev=o,n.allowSlideNext=l,n.emit("loopFix");return}let w=m.slidesPerView;w==="auto"?w=n.slidesPerViewDynamic():(w=Math.ceil(parseFloat(m.slidesPerView,10)),h&&w%2===0&&(w=w+1));const g=m.slidesPerGroupAuto?w:m.slidesPerGroup;let u=g;u%g!==0&&(u+=g-u%g),u+=m.loopAdditionalSlides,n.loopedSlides=u;const y=n.grid&&m.grid&&m.grid.rows>1;p.length<w+u?ge("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):y&&m.grid.fill==="row"&&ge("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const v=[],E=[];let P=n.activeIndex;typeof r>"u"?r=n.getSlideIndex(p.filter(b=>b.classList.contains(m.slideActiveClass))[0]):P=r;const I=i==="next"||!i,O=i==="prev"||!i;let M=0,A=0;const T=y?Math.ceil(p.length/m.grid.rows):p.length,x=(y?p[r].column:r)+(h&&typeof a>"u"?-w/2+.5:0);if(x<u){M=Math.max(u-x,g);for(let b=0;b<u-x;b+=1){const S=b-Math.floor(b/T)*T;if(y){const C=T-S-1;for(let k=p.length-1;k>=0;k-=1)p[k].column===C&&v.push(k)}else v.push(T-S-1)}}else if(x+w>T-u){A=Math.max(x-(T-u*2),g);for(let b=0;b<A;b+=1){const S=b-Math.floor(b/T)*T;y?p.forEach((C,k)=>{C.column===S&&E.push(k)}):E.push(S)}}if(n.__preventObserver__=!0,requestAnimationFrame(()=>{n.__preventObserver__=!1}),O&&v.forEach(b=>{p[b].swiperLoopMoveDOM=!0,d.prepend(p[b]),p[b].swiperLoopMoveDOM=!1}),I&&E.forEach(b=>{p[b].swiperLoopMoveDOM=!0,d.append(p[b]),p[b].swiperLoopMoveDOM=!1}),n.recalcSlides(),m.slidesPerView==="auto"?n.updateSlides():y&&(v.length>0&&O||E.length>0&&I)&&n.slides.forEach((b,S)=>{n.grid.updateSlide(S,b,n.slides)}),m.watchSlidesProgress&&n.updateSlidesOffset(),t){if(v.length>0&&O){if(typeof e>"u"){const b=n.slidesGrid[P],C=n.slidesGrid[P+M]-b;c?n.setTranslate(n.translate-C):(n.slideTo(P+M,0,!1,!0),a&&(n.touchEventsData.startTranslate=n.touchEventsData.startTranslate-C,n.touchEventsData.currentTranslate=n.touchEventsData.currentTranslate-C))}else if(a){const b=y?v.length/m.grid.rows:v.length;n.slideTo(n.activeIndex+b,0,!1,!0),n.touchEventsData.currentTranslate=n.translate}}else if(E.length>0&&I)if(typeof e>"u"){const b=n.slidesGrid[P],C=n.slidesGrid[P-A]-b;c?n.setTranslate(n.translate-C):(n.slideTo(P-A,0,!1,!0),a&&(n.touchEventsData.startTranslate=n.touchEventsData.startTranslate-C,n.touchEventsData.currentTranslate=n.touchEventsData.currentTranslate-C))}else{const b=y?E.length/m.grid.rows:E.length;n.slideTo(n.activeIndex-b,0,!1,!0)}}if(n.allowSlidePrev=o,n.allowSlideNext=l,n.controller&&n.controller.control&&!f){const b={slideRealIndex:e,direction:i,setTranslate:a,activeSlideIndex:r,byController:!0};Array.isArray(n.controller.control)?n.controller.control.forEach(S=>{!S.destroyed&&S.params.loop&&S.loopFix({...b,slideTo:S.params.slidesPerView===m.slidesPerView?t:!1})}):n.controller.control instanceof n.constructor&&n.controller.control.params.loop&&n.controller.control.loopFix({...b,slideTo:n.controller.control.params.slidesPerView===m.slidesPerView?t:!1})}n.emit("loopFix")}function Xt(){const s=this,{params:e,slidesEl:t}=s;if(!e.loop||s.virtual&&s.params.virtual.enabled)return;s.recalcSlides();const i=[];s.slides.forEach(a=>{const r=typeof a.swiperSlideIndex>"u"?a.getAttribute("data-swiper-slide-index")*1:a.swiperSlideIndex;i[r]=a}),s.slides.forEach(a=>{a.removeAttribute("data-swiper-slide-index")}),i.forEach(a=>{t.append(a)}),s.recalcSlides(),s.slideTo(s.realIndex,0)}var Nt={loopCreate:Ht,loopFix:Bt,loopDestroy:Xt};function Rt(s){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=s?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Yt(){const s=this;s.params.watchOverflow&&s.isLocked||s.params.cssMode||(s.isElement&&(s.__preventObserver__=!0),s[s.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",s.isElement&&requestAnimationFrame(()=>{s.__preventObserver__=!1}))}var Vt={setGrabCursor:Rt,unsetGrabCursor:Yt};function Ft(s,e){e===void 0&&(e=this);function t(i){if(!i||i===V()||i===R())return null;i.assignedSlot&&(i=i.assignedSlot);const a=i.closest(s);return!a&&!i.getRootNode?null:a||t(i.getRootNode().host)}return t(e)}function Xe(s,e,t){const i=R(),{params:a}=s,r=a.edgeSwipeDetection,f=a.edgeSwipeThreshold;return r&&(t<=f||t>=i.innerWidth-f)?r==="prevent"?(e.preventDefault(),!0):!1:!0}function Wt(s){const e=this,t=V();let i=s;i.originalEvent&&(i=i.originalEvent);const a=e.touchEventsData;if(i.type==="pointerdown"){if(a.pointerId!==null&&a.pointerId!==i.pointerId)return;a.pointerId=i.pointerId}else i.type==="touchstart"&&i.targetTouches.length===1&&(a.touchId=i.targetTouches[0].identifier);if(i.type==="touchstart"){Xe(e,i,i.targetTouches[0].pageX);return}const{params:r,touches:f,enabled:c}=e;if(!c||!r.simulateTouch&&i.pointerType==="mouse"||e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let n=i.target;if(r.touchEventsTarget==="wrapper"&&!e.wrapperEl.contains(n)||"which"in i&&i.which===3||"button"in i&&i.button>0||a.isTouched&&a.isMoved)return;const p=!!r.noSwipingClass&&r.noSwipingClass!=="",o=i.composedPath?i.composedPath():i.path;p&&i.target&&i.target.shadowRoot&&o&&(n=o[0]);const l=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,d=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(d?Ft(l,n):n.closest(l))){e.allowClick=!0;return}if(r.swipeHandler&&!n.closest(r.swipeHandler))return;f.currentX=i.pageX,f.currentY=i.pageY;const m=f.currentX,h=f.currentY;if(!Xe(e,i,m))return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),f.startX=m,f.startY=h,a.touchStartTime=U(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(a.allowThresholdMove=!1);let w=!0;n.matches(a.focusableElements)&&(w=!1,n.nodeName==="SELECT"&&(a.isTouched=!1)),t.activeElement&&t.activeElement.matches(a.focusableElements)&&t.activeElement!==n&&t.activeElement.blur();const g=w&&e.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||g)&&!n.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",i)}function _t(s){const e=V(),t=this,i=t.touchEventsData,{params:a,touches:r,rtlTranslate:f,enabled:c}=t;if(!c||!a.simulateTouch&&s.pointerType==="mouse")return;let n=s;if(n.originalEvent&&(n=n.originalEvent),n.type==="pointermove"&&(i.touchId!==null||n.pointerId!==i.pointerId))return;let p;if(n.type==="touchmove"){if(p=[...n.changedTouches].filter(P=>P.identifier===i.touchId)[0],!p||p.identifier!==i.touchId)return}else p=n;if(!i.isTouched){i.startMoving&&i.isScrolling&&t.emit("touchMoveOpposite",n);return}const o=p.pageX,l=p.pageY;if(n.preventedByNestedSwiper){r.startX=o,r.startY=l;return}if(!t.allowTouchMove){n.target.matches(i.focusableElements)||(t.allowClick=!1),i.isTouched&&(Object.assign(r,{startX:o,startY:l,currentX:o,currentY:l}),i.touchStartTime=U());return}if(a.touchReleaseOnEdges&&!a.loop){if(t.isVertical()){if(l<r.startY&&t.translate<=t.maxTranslate()||l>r.startY&&t.translate>=t.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else if(o<r.startX&&t.translate<=t.maxTranslate()||o>r.startX&&t.translate>=t.minTranslate())return}if(e.activeElement&&n.target===e.activeElement&&n.target.matches(i.focusableElements)){i.isMoved=!0,t.allowClick=!1;return}i.allowTouchCallbacks&&t.emit("touchMove",n),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=o,r.currentY=l;const d=r.currentX-r.startX,m=r.currentY-r.startY;if(t.params.threshold&&Math.sqrt(d**2+m**2)<t.params.threshold)return;if(typeof i.isScrolling>"u"){let P;t.isHorizontal()&&r.currentY===r.startY||t.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:d*d+m*m>=25&&(P=Math.atan2(Math.abs(m),Math.abs(d))*180/Math.PI,i.isScrolling=t.isHorizontal()?P>a.touchAngle:90-P>a.touchAngle)}if(i.isScrolling&&t.emit("touchMoveOpposite",n),typeof i.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(i.startMoving=!0),i.isScrolling){i.isTouched=!1;return}if(!i.startMoving)return;t.allowClick=!1,!a.cssMode&&n.cancelable&&n.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&n.stopPropagation();let h=t.isHorizontal()?d:m,w=t.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;a.oneWayMovement&&(h=Math.abs(h)*(f?1:-1),w=Math.abs(w)*(f?1:-1)),r.diff=h,h*=a.touchRatio,f&&(h=-h,w=-w);const g=t.touchesDirection;t.swipeDirection=h>0?"prev":"next",t.touchesDirection=w>0?"prev":"next";const u=t.params.loop&&!a.cssMode,y=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!i.isMoved){if(u&&y&&t.loopFix({direction:t.swipeDirection}),i.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const P=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(P)}i.allowMomentumBounce=!1,a.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",n)}if(new Date().getTime(),i.isMoved&&i.allowThresholdMove&&g!==t.touchesDirection&&u&&y&&Math.abs(h)>=1){Object.assign(r,{startX:o,startY:l,currentX:o,currentY:l,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,i.startTranslate=i.currentTranslate;return}t.emit("sliderMove",n),i.isMoved=!0,i.currentTranslate=h+i.startTranslate;let v=!0,E=a.resistanceRatio;if(a.touchReleaseOnEdges&&(E=0),h>0?(u&&y&&i.allowThresholdMove&&i.currentTranslate>(a.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>t.minTranslate()&&(v=!1,a.resistance&&(i.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+i.startTranslate+h)**E))):h<0&&(u&&y&&i.allowThresholdMove&&i.currentTranslate<(a.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]:t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(a.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),i.currentTranslate<t.maxTranslate()&&(v=!1,a.resistance&&(i.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-i.startTranslate-h)**E))),v&&(n.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(i.currentTranslate=i.startTranslate),a.threshold>0)if(Math.abs(h)>a.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,r.diff=t.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{i.currentTranslate=i.startTranslate;return}!a.followFinger||a.cssMode||((a.freeMode&&a.freeMode.enabled&&t.freeMode||a.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(i.currentTranslate),t.setTranslate(i.currentTranslate))}function qt(s){const e=this,t=e.touchEventsData;let i=s;i.originalEvent&&(i=i.originalEvent);let a;if(i.type==="touchend"||i.type==="touchcancel"){if(a=[...i.changedTouches].filter(P=>P.identifier===t.touchId)[0],!a||a.identifier!==t.touchId)return}else{if(t.touchId!==null||i.pointerId!==t.pointerId)return;a=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&!(["pointercancel","contextmenu"].includes(i.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:f,touches:c,rtlTranslate:n,slidesGrid:p,enabled:o}=e;if(!o||!f.simulateTouch&&i.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",i),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&f.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}f.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const l=U(),d=l-t.touchStartTime;if(e.allowClick){const P=i.path||i.composedPath&&i.composedPath();e.updateClickedSlide(P&&P[0]||i.target,P),e.emit("tap click",i),d<300&&l-t.lastClickTime<300&&e.emit("doubleTap doubleClick",i)}if(t.lastClickTime=U(),se(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||c.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let m;if(f.followFinger?m=n?e.translate:-e.translate:m=-t.currentTranslate,f.cssMode)return;if(f.freeMode&&f.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:m});return}const h=m>=-e.maxTranslate()&&!e.params.loop;let w=0,g=e.slidesSizesGrid[0];for(let P=0;P<p.length;P+=P<f.slidesPerGroupSkip?1:f.slidesPerGroup){const I=P<f.slidesPerGroupSkip-1?1:f.slidesPerGroup;typeof p[P+I]<"u"?(h||m>=p[P]&&m<p[P+I])&&(w=P,g=p[P+I]-p[P]):(h||m>=p[P])&&(w=P,g=p[p.length-1]-p[p.length-2])}let u=null,y=null;f.rewind&&(e.isBeginning?y=f.virtual&&f.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(u=0));const v=(m-p[w])/g,E=w<f.slidesPerGroupSkip-1?1:f.slidesPerGroup;if(d>f.longSwipesMs){if(!f.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(v>=f.longSwipesRatio?e.slideTo(f.rewind&&e.isEnd?u:w+E):e.slideTo(w)),e.swipeDirection==="prev"&&(v>1-f.longSwipesRatio?e.slideTo(w+E):y!==null&&v<0&&Math.abs(v)>f.longSwipesRatio?e.slideTo(y):e.slideTo(w))}else{if(!f.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(i.target===e.navigation.nextEl||i.target===e.navigation.prevEl)?i.target===e.navigation.nextEl?e.slideTo(w+E):e.slideTo(w):(e.swipeDirection==="next"&&e.slideTo(u!==null?u:w+E),e.swipeDirection==="prev"&&e.slideTo(y!==null?y:w))}}function Ne(){const s=this,{params:e,el:t}=s;if(t&&t.offsetWidth===0)return;e.breakpoints&&s.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:a,snapGrid:r}=s,f=s.virtual&&s.params.virtual.enabled;s.allowSlideNext=!0,s.allowSlidePrev=!0,s.updateSize(),s.updateSlides(),s.updateSlidesClasses();const c=f&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&s.isEnd&&!s.isBeginning&&!s.params.centeredSlides&&!c?s.slideTo(s.slides.length-1,0,!1,!0):s.params.loop&&!f?s.slideToLoop(s.realIndex,0,!1,!0):s.slideTo(s.activeIndex,0,!1,!0),s.autoplay&&s.autoplay.running&&s.autoplay.paused&&(clearTimeout(s.autoplay.resizeTimeout),s.autoplay.resizeTimeout=setTimeout(()=>{s.autoplay&&s.autoplay.running&&s.autoplay.paused&&s.autoplay.resume()},500)),s.allowSlidePrev=a,s.allowSlideNext=i,s.params.watchOverflow&&r!==s.snapGrid&&s.checkOverflow()}function jt(s){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&s.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(s.stopPropagation(),s.stopImmediatePropagation())))}function Ut(){const s=this,{wrapperEl:e,rtlTranslate:t,enabled:i}=s;if(!i)return;s.previousTranslate=s.translate,s.isHorizontal()?s.translate=-e.scrollLeft:s.translate=-e.scrollTop,s.translate===0&&(s.translate=0),s.updateActiveIndex(),s.updateSlidesClasses();let a;const r=s.maxTranslate()-s.minTranslate();r===0?a=0:a=(s.translate-s.minTranslate())/r,a!==s.progress&&s.updateProgress(t?-s.translate:s.translate),s.emit("setTranslate",s.translate,!1)}function Kt(s){const e=this;he(e,s.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function Zt(){const s=this;s.documentTouchHandlerProceeded||(s.documentTouchHandlerProceeded=!0,s.params.touchReleaseOnEdges&&(s.el.style.touchAction="auto"))}const je=(s,e)=>{const t=V(),{params:i,el:a,wrapperEl:r,device:f}=s,c=!!i.nested,n=e==="on"?"addEventListener":"removeEventListener",p=e;t[n]("touchstart",s.onDocumentTouchStart,{passive:!1,capture:c}),a[n]("touchstart",s.onTouchStart,{passive:!1}),a[n]("pointerdown",s.onTouchStart,{passive:!1}),t[n]("touchmove",s.onTouchMove,{passive:!1,capture:c}),t[n]("pointermove",s.onTouchMove,{passive:!1,capture:c}),t[n]("touchend",s.onTouchEnd,{passive:!0}),t[n]("pointerup",s.onTouchEnd,{passive:!0}),t[n]("pointercancel",s.onTouchEnd,{passive:!0}),t[n]("touchcancel",s.onTouchEnd,{passive:!0}),t[n]("pointerout",s.onTouchEnd,{passive:!0}),t[n]("pointerleave",s.onTouchEnd,{passive:!0}),t[n]("contextmenu",s.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&a[n]("click",s.onClick,!0),i.cssMode&&r[n]("scroll",s.onScroll),i.updateOnWindowResize?s[p](f.ios||f.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ne,!0):s[p]("observerUpdate",Ne,!0),a[n]("load",s.onLoad,{capture:!0})};function Qt(){const s=this,{params:e}=s;s.onTouchStart=Wt.bind(s),s.onTouchMove=_t.bind(s),s.onTouchEnd=qt.bind(s),s.onDocumentTouchStart=Zt.bind(s),e.cssMode&&(s.onScroll=Ut.bind(s)),s.onClick=jt.bind(s),s.onLoad=Kt.bind(s),je(s,"on")}function Jt(){je(this,"off")}var es={attachEvents:Qt,detachEvents:Jt};const Re=(s,e)=>s.grid&&e.grid&&e.grid.rows>1;function ts(){const s=this,{realIndex:e,initialized:t,params:i,el:a}=s,r=i.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const f=s.getBreakpoint(r,s.params.breakpointsBase,s.el);if(!f||s.currentBreakpoint===f)return;const n=(f in r?r[f]:void 0)||s.originalParams,p=Re(s,i),o=Re(s,n),l=i.enabled;p&&!o?(a.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),s.emitContainerClasses()):!p&&o&&(a.classList.add(`${i.containerModifierClass}grid`),(n.grid.fill&&n.grid.fill==="column"||!n.grid.fill&&i.grid.fill==="column")&&a.classList.add(`${i.containerModifierClass}grid-column`),s.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(u=>{if(typeof n[u]>"u")return;const y=i[u]&&i[u].enabled,v=n[u]&&n[u].enabled;y&&!v&&s[u].disable(),!y&&v&&s[u].enable()});const d=n.direction&&n.direction!==i.direction,m=i.loop&&(n.slidesPerView!==i.slidesPerView||d),h=i.loop;d&&t&&s.changeDirection(),_(s.params,n);const w=s.params.enabled,g=s.params.loop;Object.assign(s,{allowTouchMove:s.params.allowTouchMove,allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev}),l&&!w?s.disable():!l&&w&&s.enable(),s.currentBreakpoint=f,s.emit("_beforeBreakpoint",n),t&&(m?(s.loopDestroy(),s.loopCreate(e),s.updateSlides()):!h&&g?(s.loopCreate(e),s.updateSlides()):h&&!g&&s.loopDestroy()),s.emit("breakpoint",n)}function ss(s,e,t){if(e===void 0&&(e="window"),!s||e==="container"&&!t)return;let i=!1;const a=R(),r=e==="window"?a.innerHeight:t.clientHeight,f=Object.keys(s).map(c=>{if(typeof c=="string"&&c.indexOf("@")===0){const n=parseFloat(c.substr(1));return{value:r*n,point:c}}return{value:c,point:c}});f.sort((c,n)=>parseInt(c.value,10)-parseInt(n.value,10));for(let c=0;c<f.length;c+=1){const{point:n,value:p}=f[c];e==="window"?a.matchMedia(`(min-width: ${p}px)`).matches&&(i=n):p<=t.clientWidth&&(i=n)}return i||"max"}var is={setBreakpoint:ts,getBreakpoint:ss};function rs(s,e){const t=[];return s.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(a=>{i[a]&&t.push(e+a)}):typeof i=="string"&&t.push(e+i)}),t}function as(){const s=this,{classNames:e,params:t,rtl:i,el:a,device:r}=s,f=rs(["initialized",t.direction,{"free-mode":s.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...f),a.classList.add(...e),s.emitContainerClasses()}function ns(){const s=this,{el:e,classNames:t}=s;e.classList.remove(...t),s.emitContainerClasses()}var ls={addClasses:as,removeClasses:ns};function os(){const s=this,{isLocked:e,params:t}=s,{slidesOffsetBefore:i}=t;if(i){const a=s.slides.length-1,r=s.slidesGrid[a]+s.slidesSizesGrid[a]+i*2;s.isLocked=s.size>r}else s.isLocked=s.snapGrid.length===1;t.allowSlideNext===!0&&(s.allowSlideNext=!s.isLocked),t.allowSlidePrev===!0&&(s.allowSlidePrev=!s.isLocked),e&&e!==s.isLocked&&(s.isEnd=!1),e!==s.isLocked&&s.emit(s.isLocked?"lock":"unlock")}var ds={checkOverflow:os},Ye={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function cs(s,e){return function(i){i===void 0&&(i={});const a=Object.keys(i)[0],r=i[a];if(typeof r!="object"||r===null){_(e,i);return}if(s[a]===!0&&(s[a]={enabled:!0}),a==="navigation"&&s[a]&&s[a].enabled&&!s[a].prevEl&&!s[a].nextEl&&(s[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&s[a]&&s[a].enabled&&!s[a].el&&(s[a].auto=!0),!(a in s&&"enabled"in r)){_(e,i);return}typeof s[a]=="object"&&!("enabled"in s[a])&&(s[a].enabled=!0),s[a]||(s[a]={enabled:!1}),_(e,i)}}const Pe={eventsEmitter:lt,update:wt,translate:Tt,transition:Lt,slide:Gt,loop:Nt,grabCursor:Vt,events:es,breakpoints:is,checkOverflow:ds,classes:ls},Le={};class W{constructor(){let e,t;for(var i=arguments.length,a=new Array(i),r=0;r<i;r++)a[r]=arguments[r];a.length===1&&a[0].constructor&&Object.prototype.toString.call(a[0]).slice(8,-1)==="Object"?t=a[0]:[e,t]=a,t||(t={}),t=_({},t),e&&!t.el&&(t.el=e);const f=V();if(t.el&&typeof t.el=="string"&&f.querySelectorAll(t.el).length>1){const o=[];return f.querySelectorAll(t.el).forEach(l=>{const d=_({},t,{el:l});o.push(new W(d))}),o}const c=this;c.__swiper__=!0,c.support=We(),c.device=_e({userAgent:t.userAgent}),c.browser=rt(),c.eventsListeners={},c.eventsAnyListeners=[],c.modules=[...c.__modules__],t.modules&&Array.isArray(t.modules)&&c.modules.push(...t.modules);const n={};c.modules.forEach(o=>{o({params:t,swiper:c,extendParams:cs(t,n),on:c.on.bind(c),once:c.once.bind(c),off:c.off.bind(c),emit:c.emit.bind(c)})});const p=_({},Ye,n);return c.params=_({},p,Le,t),c.originalParams=_({},c.params),c.passedParams=_({},t),c.params&&c.params.on&&Object.keys(c.params.on).forEach(o=>{c.on(o,c.params.on[o])}),c.params&&c.params.onAny&&c.onAny(c.params.onAny),Object.assign(c,{enabled:c.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return c.params.direction==="horizontal"},isVertical(){return c.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:c.params.allowSlideNext,allowSlidePrev:c.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:c.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:c.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),c.emit("_swiper"),c.params.init&&c.init(),c}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:i}=this,a=Y(t,`.${i.slideClass}, swiper-slide`),r=fe(a[0]);return fe(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>t.getAttribute("data-swiper-slide-index")*1===e)[0])}recalcSlides(){const e=this,{slidesEl:t,params:i}=e;e.slides=Y(t,`.${i.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const a=i.minTranslate(),f=(i.maxTranslate()-a)*e+a;i.translateTo(f,typeof t>"u"?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(i=>{const a=e.getSlideClasses(i);t.push({slideEl:i,classNames:a}),e.emit("_slideClass",i,a)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const i=this,{params:a,slides:r,slidesGrid:f,slidesSizesGrid:c,size:n,activeIndex:p}=i;let o=1;if(typeof a.slidesPerView=="number")return a.slidesPerView;if(a.centeredSlides){let l=r[p]?Math.ceil(r[p].swiperSlideSize):0,d;for(let m=p+1;m<r.length;m+=1)r[m]&&!d&&(l+=Math.ceil(r[m].swiperSlideSize),o+=1,l>n&&(d=!0));for(let m=p-1;m>=0;m-=1)r[m]&&!d&&(l+=r[m].swiperSlideSize,o+=1,l>n&&(d=!0))}else if(e==="current")for(let l=p+1;l<r.length;l+=1)(t?f[l]+c[l]-f[p]<n:f[l]-f[p]<n)&&(o+=1);else for(let l=p-1;l>=0;l-=1)f[p]-f[l]<n&&(o+=1);return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(f=>{f.complete&&he(e,f)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function a(){const f=e.rtlTranslate?e.translate*-1:e.translate,c=Math.min(Math.max(f,e.maxTranslate()),e.minTranslate());e.setTranslate(c),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)a(),i.autoHeight&&e.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const f=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(f.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||a()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const i=this,a=i.params.direction;return e||(e=a==="horizontal"?"vertical":"horizontal"),e===a||e!=="horizontal"&&e!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${a}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(r=>{e==="vertical"?r.style.width="":r.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let f=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(a()):Y(i,a())[0];return!f&&t.params.createElements&&(f=q("div",t.params.wrapperClass),i.append(f),Y(i,`.${t.params.slideClass}`).forEach(c=>{f.append(c)})),Object.assign(t,{el:i,wrapperEl:f,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:f,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||J(i,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||J(i,"direction")==="rtl"),wrongRTL:J(f,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();const a=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&a.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),a.forEach(r=>{r.complete?he(t,r):r.addEventListener("load",f=>{he(t,f.target)})}),Ae(t),t.initialized=!0,Ae(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const i=this,{params:a,el:r,wrapperEl:f,slides:c}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),a.loop&&i.loopDestroy(),t&&(i.removeClasses(),r.removeAttribute("style"),f.removeAttribute("style"),c&&c.length&&c.forEach(n=>{n.classList.remove(a.slideVisibleClass,a.slideFullyVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),n.removeAttribute("style"),n.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(n=>{i.off(n)}),e!==!1&&(i.el.swiper=null,Ke(i)),i.destroyed=!0),null}static extendDefaults(e){_(Le,e)}static get extendedDefaults(){return Le}static get defaults(){return Ye}static installModule(e){W.prototype.__modules__||(W.prototype.__modules__=[]);const t=W.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>W.installModule(t)),W):(W.installModule(e),W)}}Object.keys(Pe).forEach(s=>{Object.keys(Pe[s]).forEach(e=>{W.prototype[e]=Pe[s][e]})});W.use([at,nt]);function fs(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;t({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});let r;const f=V();e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const c=f.createElement("div");function n(h,w){const g=e.params.virtual;if(g.cache&&e.virtual.cache[w])return e.virtual.cache[w];let u;return g.renderSlide?(u=g.renderSlide.call(e,h,w),typeof u=="string"&&(c.innerHTML=u,u=c.children[0])):e.isElement?u=q("swiper-slide"):u=q("div",e.params.slideClass),u.setAttribute("data-swiper-slide-index",w),g.renderSlide||(u.innerHTML=h),g.cache&&(e.virtual.cache[w]=u),u}function p(h){const{slidesPerView:w,slidesPerGroup:g,centeredSlides:u,loop:y}=e.params,{addSlidesBefore:v,addSlidesAfter:E}=e.params.virtual,{from:P,to:I,slides:O,slidesGrid:M,offset:A}=e.virtual;e.params.cssMode||e.updateActiveIndex();const T=e.activeIndex||0;let z;e.rtlTranslate?z="right":z=e.isHorizontal()?"left":"top";let x,b;u?(x=Math.floor(w/2)+g+E,b=Math.floor(w/2)+g+v):(x=w+(g-1)+E,b=(y?w:g)+v);let S=T-b,C=T+x;y||(S=Math.max(S,0),C=Math.min(C,O.length-1));let k=(e.slidesGrid[S]||0)-(e.slidesGrid[0]||0);y&&T>=b?(S-=b,u||(k+=e.slidesGrid[0])):y&&T<b&&(S=-b,u&&(k+=e.slidesGrid[0])),Object.assign(e.virtual,{from:S,to:C,offset:k,slidesGrid:e.slidesGrid,slidesBefore:b,slidesAfter:x});function D(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),a("virtualUpdate")}if(P===S&&I===C&&!h){e.slidesGrid!==M&&k!==A&&e.slides.forEach(H=>{H.style[z]=`${k-Math.abs(e.cssOverflowAdjustment())}px`}),e.updateProgress(),a("virtualUpdate");return}if(e.params.virtual.renderExternal){e.params.virtual.renderExternal.call(e,{offset:k,from:S,to:C,slides:function(){const X=[];for(let j=S;j<=C;j+=1)X.push(O[j]);return X}()}),e.params.virtual.renderExternalUpdate?D():a("virtualUpdate");return}const L=[],$=[],G=H=>{let X=H;return H<0?X=O.length+H:X>=O.length&&(X=X-O.length),X};if(h)e.slides.filter(H=>H.matches(`.${e.params.slideClass}, swiper-slide`)).forEach(H=>{H.remove()});else for(let H=P;H<=I;H+=1)if(H<S||H>C){const X=G(H);e.slides.filter(j=>j.matches(`.${e.params.slideClass}[data-swiper-slide-index="${X}"], swiper-slide[data-swiper-slide-index="${X}"]`)).forEach(j=>{j.remove()})}const N=y?-O.length:0,F=y?O.length*2:O.length;for(let H=N;H<F;H+=1)if(H>=S&&H<=C){const X=G(H);typeof I>"u"||h?$.push(X):(H>I&&$.push(X),H<P&&L.push(X))}if($.forEach(H=>{e.slidesEl.append(n(O[H],H))}),y)for(let H=L.length-1;H>=0;H-=1){const X=L[H];e.slidesEl.prepend(n(O[X],X))}else L.sort((H,X)=>X-H),L.forEach(H=>{e.slidesEl.prepend(n(O[H],H))});Y(e.slidesEl,".swiper-slide, swiper-slide").forEach(H=>{H.style[z]=`${k-Math.abs(e.cssOverflowAdjustment())}px`}),D()}function o(h){if(typeof h=="object"&&"length"in h)for(let w=0;w<h.length;w+=1)h[w]&&e.virtual.slides.push(h[w]);else e.virtual.slides.push(h);p(!0)}function l(h){const w=e.activeIndex;let g=w+1,u=1;if(Array.isArray(h)){for(let y=0;y<h.length;y+=1)h[y]&&e.virtual.slides.unshift(h[y]);g=w+h.length,u=h.length}else e.virtual.slides.unshift(h);if(e.params.virtual.cache){const y=e.virtual.cache,v={};Object.keys(y).forEach(E=>{const P=y[E],I=P.getAttribute("data-swiper-slide-index");I&&P.setAttribute("data-swiper-slide-index",parseInt(I,10)+u),v[parseInt(E,10)+u]=P}),e.virtual.cache=v}p(!0),e.slideTo(g,0)}function d(h){if(typeof h>"u"||h===null)return;let w=e.activeIndex;if(Array.isArray(h))for(let g=h.length-1;g>=0;g-=1)e.params.virtual.cache&&(delete e.virtual.cache[h[g]],Object.keys(e.virtual.cache).forEach(u=>{u>h&&(e.virtual.cache[u-1]=e.virtual.cache[u],e.virtual.cache[u-1].setAttribute("data-swiper-slide-index",u-1),delete e.virtual.cache[u])})),e.virtual.slides.splice(h[g],1),h[g]<w&&(w-=1),w=Math.max(w,0);else e.params.virtual.cache&&(delete e.virtual.cache[h],Object.keys(e.virtual.cache).forEach(g=>{g>h&&(e.virtual.cache[g-1]=e.virtual.cache[g],e.virtual.cache[g-1].setAttribute("data-swiper-slide-index",g-1),delete e.virtual.cache[g])})),e.virtual.slides.splice(h,1),h<w&&(w-=1),w=Math.max(w,0);p(!0),e.slideTo(w,0)}function m(){e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),p(!0),e.slideTo(0,0)}i("beforeInit",()=>{if(!e.params.virtual.enabled)return;let h;if(typeof e.passedParams.virtual.slides>"u"){const w=[...e.slidesEl.children].filter(g=>g.matches(`.${e.params.slideClass}, swiper-slide`));w&&w.length&&(e.virtual.slides=[...w],h=!0,w.forEach((g,u)=>{g.setAttribute("data-swiper-slide-index",u),e.virtual.cache[u]=g,g.remove()}))}h||(e.virtual.slides=e.params.virtual.slides),e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,p()}),i("setTranslate",()=>{e.params.virtual.enabled&&(e.params.cssMode&&!e._immediateVirtual?(clearTimeout(r),r=setTimeout(()=>{p()},100)):p())}),i("init update resize",()=>{e.params.virtual.enabled&&e.params.cssMode&&de(e.wrapperEl,"--swiper-virtual-size",`${e.virtualSize}px`)}),Object.assign(e.virtual,{appendSlide:o,prependSlide:l,removeSlide:d,removeAllSlides:m,update:p})}function us(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r=V(),f=R();e.keyboard={enabled:!1},t({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}});function c(o){if(!e.enabled)return;const{rtlTranslate:l}=e;let d=o;d.originalEvent&&(d=d.originalEvent);const m=d.keyCode||d.charCode,h=e.params.keyboard.pageUpDown,w=h&&m===33,g=h&&m===34,u=m===37,y=m===39,v=m===38,E=m===40;if(!e.allowSlideNext&&(e.isHorizontal()&&y||e.isVertical()&&E||g)||!e.allowSlidePrev&&(e.isHorizontal()&&u||e.isVertical()&&v||w))return!1;if(!(d.shiftKey||d.altKey||d.ctrlKey||d.metaKey)&&!(r.activeElement&&r.activeElement.nodeName&&(r.activeElement.nodeName.toLowerCase()==="input"||r.activeElement.nodeName.toLowerCase()==="textarea"))){if(e.params.keyboard.onlyInViewport&&(w||g||u||y||v||E)){let P=!1;if(te(e.el,`.${e.params.slideClass}, swiper-slide`).length>0&&te(e.el,`.${e.params.slideActiveClass}`).length===0)return;const I=e.el,O=I.clientWidth,M=I.clientHeight,A=f.innerWidth,T=f.innerHeight,z=ve(I);l&&(z.left-=I.scrollLeft);const x=[[z.left,z.top],[z.left+O,z.top],[z.left,z.top+M],[z.left+O,z.top+M]];for(let b=0;b<x.length;b+=1){const S=x[b];if(S[0]>=0&&S[0]<=A&&S[1]>=0&&S[1]<=T){if(S[0]===0&&S[1]===0)continue;P=!0}}if(!P)return}e.isHorizontal()?((w||g||u||y)&&(d.preventDefault?d.preventDefault():d.returnValue=!1),((g||y)&&!l||(w||u)&&l)&&e.slideNext(),((w||u)&&!l||(g||y)&&l)&&e.slidePrev()):((w||g||v||E)&&(d.preventDefault?d.preventDefault():d.returnValue=!1),(g||E)&&e.slideNext(),(w||v)&&e.slidePrev()),a("keyPress",m)}}function n(){e.keyboard.enabled||(r.addEventListener("keydown",c),e.keyboard.enabled=!0)}function p(){e.keyboard.enabled&&(r.removeEventListener("keydown",c),e.keyboard.enabled=!1)}i("init",()=>{e.params.keyboard.enabled&&n()}),i("destroy",()=>{e.keyboard.enabled&&p()}),Object.assign(e.keyboard,{enable:n,disable:p})}function ps(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r=R();t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),e.mousewheel={enabled:!1};let f,c=U(),n;const p=[];function o(v){let O=0,M=0,A=0,T=0;return"detail"in v&&(M=v.detail),"wheelDelta"in v&&(M=-v.wheelDelta/120),"wheelDeltaY"in v&&(M=-v.wheelDeltaY/120),"wheelDeltaX"in v&&(O=-v.wheelDeltaX/120),"axis"in v&&v.axis===v.HORIZONTAL_AXIS&&(O=M,M=0),A=O*10,T=M*10,"deltaY"in v&&(T=v.deltaY),"deltaX"in v&&(A=v.deltaX),v.shiftKey&&!A&&(A=T,T=0),(A||T)&&v.deltaMode&&(v.deltaMode===1?(A*=40,T*=40):(A*=800,T*=800)),A&&!O&&(O=A<1?-1:1),T&&!M&&(M=T<1?-1:1),{spinX:O,spinY:M,pixelX:A,pixelY:T}}function l(){e.enabled&&(e.mouseEntered=!0)}function d(){e.enabled&&(e.mouseEntered=!1)}function m(v){return e.params.mousewheel.thresholdDelta&&v.delta<e.params.mousewheel.thresholdDelta||e.params.mousewheel.thresholdTime&&U()-c<e.params.mousewheel.thresholdTime?!1:v.delta>=6&&U()-c<60?!0:(v.direction<0?(!e.isEnd||e.params.loop)&&!e.animating&&(e.slideNext(),a("scroll",v.raw)):(!e.isBeginning||e.params.loop)&&!e.animating&&(e.slidePrev(),a("scroll",v.raw)),c=new r.Date().getTime(),!1)}function h(v){const E=e.params.mousewheel;if(v.direction<0){if(e.isEnd&&!e.params.loop&&E.releaseOnEdges)return!0}else if(e.isBeginning&&!e.params.loop&&E.releaseOnEdges)return!0;return!1}function w(v){let E=v,P=!0;if(!e.enabled||v.target.closest(`.${e.params.mousewheel.noMousewheelClass}`))return;const I=e.params.mousewheel;e.params.cssMode&&E.preventDefault();let O=e.el;e.params.mousewheel.eventsTarget!=="container"&&(O=document.querySelector(e.params.mousewheel.eventsTarget));const M=O&&O.contains(E.target);if(!e.mouseEntered&&!M&&!I.releaseOnEdges)return!0;E.originalEvent&&(E=E.originalEvent);let A=0;const T=e.rtlTranslate?-1:1,z=o(E);if(I.forceToAxis)if(e.isHorizontal())if(Math.abs(z.pixelX)>Math.abs(z.pixelY))A=-z.pixelX*T;else return!0;else if(Math.abs(z.pixelY)>Math.abs(z.pixelX))A=-z.pixelY;else return!0;else A=Math.abs(z.pixelX)>Math.abs(z.pixelY)?-z.pixelX*T:-z.pixelY;if(A===0)return!0;I.invert&&(A=-A);let x=e.getTranslate()+A*I.sensitivity;if(x>=e.minTranslate()&&(x=e.minTranslate()),x<=e.maxTranslate()&&(x=e.maxTranslate()),P=e.params.loop?!0:!(x===e.minTranslate()||x===e.maxTranslate()),P&&e.params.nested&&E.stopPropagation(),!e.params.freeMode||!e.params.freeMode.enabled){const b={time:U(),delta:Math.abs(A),direction:Math.sign(A),raw:v};p.length>=2&&p.shift();const S=p.length?p[p.length-1]:void 0;if(p.push(b),S?(b.direction!==S.direction||b.delta>S.delta||b.time>S.time+150)&&m(b):m(b),h(b))return!0}else{const b={time:U(),delta:Math.abs(A),direction:Math.sign(A)},S=n&&b.time<n.time+500&&b.delta<=n.delta&&b.direction===n.direction;if(!S){n=void 0;let C=e.getTranslate()+A*I.sensitivity;const k=e.isBeginning,D=e.isEnd;if(C>=e.minTranslate()&&(C=e.minTranslate()),C<=e.maxTranslate()&&(C=e.maxTranslate()),e.setTransition(0),e.setTranslate(C),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!k&&e.isBeginning||!D&&e.isEnd)&&e.updateSlidesClasses(),e.params.loop&&e.loopFix({direction:b.direction<0?"next":"prev",byMousewheel:!0}),e.params.freeMode.sticky){clearTimeout(f),f=void 0,p.length>=15&&p.shift();const L=p.length?p[p.length-1]:void 0,$=p[0];if(p.push(b),L&&(b.delta>L.delta||b.direction!==L.direction))p.splice(0);else if(p.length>=15&&b.time-$.time<500&&$.delta-b.delta>=1&&b.delta<=6){const G=A>0?.8:.2;n=b,p.splice(0),f=se(()=>{e.slideToClosest(e.params.speed,!0,void 0,G)},0)}f||(f=se(()=>{n=b,p.splice(0),e.slideToClosest(e.params.speed,!0,void 0,.5)},500))}if(S||a("scroll",E),e.params.autoplay&&e.params.autoplayDisableOnInteraction&&e.autoplay.stop(),I.releaseOnEdges&&(C===e.minTranslate()||C===e.maxTranslate()))return!0}}return E.preventDefault?E.preventDefault():E.returnValue=!1,!1}function g(v){let E=e.el;e.params.mousewheel.eventsTarget!=="container"&&(E=document.querySelector(e.params.mousewheel.eventsTarget)),E[v]("mouseenter",l),E[v]("mouseleave",d),E[v]("wheel",w)}function u(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",w),!0):e.mousewheel.enabled?!1:(g("addEventListener"),e.mousewheel.enabled=!0,!0)}function y(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,w),!0):e.mousewheel.enabled?(g("removeEventListener"),e.mousewheel.enabled=!1,!0):!1}i("init",()=>{!e.params.mousewheel.enabled&&e.params.cssMode&&y(),e.params.mousewheel.enabled&&u()}),i("destroy",()=>{e.params.cssMode&&u(),e.mousewheel.enabled&&y()}),Object.assign(e.mousewheel,{enable:u,disable:y})}function De(s,e,t,i){return s.params.createElements&&Object.keys(i).forEach(a=>{if(!t[a]&&t.auto===!0){let r=Y(s.el,`.${i[a]}`)[0];r||(r=q("div",i[a]),r.className=i[a],s.el.append(r)),t[a]=r,e[a]=r}}),t}function ms(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function r(h){let w;return h&&typeof h=="string"&&e.isElement&&(w=e.el.querySelector(h),w)?w:(h&&(typeof h=="string"&&(w=[...document.querySelectorAll(h)]),e.params.uniqueNavElements&&typeof h=="string"&&w.length>1&&e.el.querySelectorAll(h).length===1&&(w=e.el.querySelector(h))),h&&!w?h:w)}function f(h,w){const g=e.params.navigation;h=B(h),h.forEach(u=>{u&&(u.classList[w?"add":"remove"](...g.disabledClass.split(" ")),u.tagName==="BUTTON"&&(u.disabled=w),e.params.watchOverflow&&e.enabled&&u.classList[e.isLocked?"add":"remove"](g.lockClass))})}function c(){const{nextEl:h,prevEl:w}=e.navigation;if(e.params.loop){f(w,!1),f(h,!1);return}f(w,e.isBeginning&&!e.params.rewind),f(h,e.isEnd&&!e.params.rewind)}function n(h){h.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),a("navigationPrev"))}function p(h){h.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),a("navigationNext"))}function o(){const h=e.params.navigation;if(e.params.navigation=De(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(h.nextEl||h.prevEl))return;let w=r(h.nextEl),g=r(h.prevEl);Object.assign(e.navigation,{nextEl:w,prevEl:g}),w=B(w),g=B(g);const u=(y,v)=>{y&&y.addEventListener("click",v==="next"?p:n),!e.enabled&&y&&y.classList.add(...h.lockClass.split(" "))};w.forEach(y=>u(y,"next")),g.forEach(y=>u(y,"prev"))}function l(){let{nextEl:h,prevEl:w}=e.navigation;h=B(h),w=B(w);const g=(u,y)=>{u.removeEventListener("click",y==="next"?p:n),u.classList.remove(...e.params.navigation.disabledClass.split(" "))};h.forEach(u=>g(u,"next")),w.forEach(u=>g(u,"prev"))}i("init",()=>{e.params.navigation.enabled===!1?m():(o(),c())}),i("toEdge fromEdge lock unlock",()=>{c()}),i("destroy",()=>{l()}),i("enable disable",()=>{let{nextEl:h,prevEl:w}=e.navigation;if(h=B(h),w=B(w),e.enabled){c();return}[...h,...w].filter(g=>!!g).forEach(g=>g.classList.add(e.params.navigation.lockClass))}),i("click",(h,w)=>{let{nextEl:g,prevEl:u}=e.navigation;g=B(g),u=B(u);const y=w.target;if(e.params.navigation.hideOnClick&&!u.includes(y)&&!g.includes(y)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===y||e.pagination.el.contains(y)))return;let v;g.length?v=g[0].classList.contains(e.params.navigation.hiddenClass):u.length&&(v=u[0].classList.contains(e.params.navigation.hiddenClass)),a(v===!0?"navigationShow":"navigationHide"),[...g,...u].filter(E=>!!E).forEach(E=>E.classList.toggle(e.params.navigation.hiddenClass))}});const d=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),o(),c()},m=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),l()};Object.assign(e.navigation,{enable:d,disable:m,update:c,init:o,destroy:l})}function Z(s){return s===void 0&&(s=""),`.${s.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function hs(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:u=>u,formatFractionTotal:u=>u,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),e.pagination={el:null,bullets:[]};let f,c=0;function n(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function p(u,y){const{bulletActiveClass:v}=e.params.pagination;u&&(u=u[`${y==="prev"?"previous":"next"}ElementSibling`],u&&(u.classList.add(`${v}-${y}`),u=u[`${y==="prev"?"previous":"next"}ElementSibling`],u&&u.classList.add(`${v}-${y}-${y}`)))}function o(u){const y=u.target.closest(Z(e.params.pagination.bulletClass));if(!y)return;u.preventDefault();const v=fe(y)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===v)return;e.slideToLoop(v)}else e.slideTo(v)}function l(){const u=e.rtl,y=e.params.pagination;if(n())return;let v=e.pagination.el;v=B(v);let E,P;const I=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,O=e.params.loop?Math.ceil(I/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(P=e.previousRealIndex||0,E=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(E=e.snapIndex,P=e.previousSnapIndex):(P=e.previousIndex||0,E=e.activeIndex||0),y.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const M=e.pagination.bullets;let A,T,z;if(y.dynamicBullets&&(f=ze(M[0],e.isHorizontal()?"width":"height"),v.forEach(x=>{x.style[e.isHorizontal()?"width":"height"]=`${f*(y.dynamicMainBullets+4)}px`}),y.dynamicMainBullets>1&&P!==void 0&&(c+=E-(P||0),c>y.dynamicMainBullets-1?c=y.dynamicMainBullets-1:c<0&&(c=0)),A=Math.max(E-c,0),T=A+(Math.min(M.length,y.dynamicMainBullets)-1),z=(T+A)/2),M.forEach(x=>{const b=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(S=>`${y.bulletActiveClass}${S}`)].map(S=>typeof S=="string"&&S.includes(" ")?S.split(" "):S).flat();x.classList.remove(...b)}),v.length>1)M.forEach(x=>{const b=fe(x);b===E?x.classList.add(...y.bulletActiveClass.split(" ")):e.isElement&&x.setAttribute("part","bullet"),y.dynamicBullets&&(b>=A&&b<=T&&x.classList.add(...`${y.bulletActiveClass}-main`.split(" ")),b===A&&p(x,"prev"),b===T&&p(x,"next"))});else{const x=M[E];if(x&&x.classList.add(...y.bulletActiveClass.split(" ")),e.isElement&&M.forEach((b,S)=>{b.setAttribute("part",S===E?"bullet-active":"bullet")}),y.dynamicBullets){const b=M[A],S=M[T];for(let C=A;C<=T;C+=1)M[C]&&M[C].classList.add(...`${y.bulletActiveClass}-main`.split(" "));p(b,"prev"),p(S,"next")}}if(y.dynamicBullets){const x=Math.min(M.length,y.dynamicMainBullets+4),b=(f*x-f)/2-z*f,S=u?"right":"left";M.forEach(C=>{C.style[e.isHorizontal()?S:"top"]=`${b}px`})}}v.forEach((M,A)=>{if(y.type==="fraction"&&(M.querySelectorAll(Z(y.currentClass)).forEach(T=>{T.textContent=y.formatFractionCurrent(E+1)}),M.querySelectorAll(Z(y.totalClass)).forEach(T=>{T.textContent=y.formatFractionTotal(O)})),y.type==="progressbar"){let T;y.progressbarOpposite?T=e.isHorizontal()?"vertical":"horizontal":T=e.isHorizontal()?"horizontal":"vertical";const z=(E+1)/O;let x=1,b=1;T==="horizontal"?x=z:b=z,M.querySelectorAll(Z(y.progressbarFillClass)).forEach(S=>{S.style.transform=`translate3d(0,0,0) scaleX(${x}) scaleY(${b})`,S.style.transitionDuration=`${e.params.speed}ms`})}y.type==="custom"&&y.renderCustom?(M.innerHTML=y.renderCustom(e,E+1,O),A===0&&a("paginationRender",M)):(A===0&&a("paginationRender",M),a("paginationUpdate",M)),e.params.watchOverflow&&e.enabled&&M.classList[e.isLocked?"add":"remove"](y.lockClass)})}function d(){const u=e.params.pagination;if(n())return;const y=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let v=e.pagination.el;v=B(v);let E="";if(u.type==="bullets"){let P=e.params.loop?Math.ceil(y/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&P>y&&(P=y);for(let I=0;I<P;I+=1)u.renderBullet?E+=u.renderBullet.call(e,I,u.bulletClass):E+=`<${u.bulletElement} ${e.isElement?'part="bullet"':""} class="${u.bulletClass}"></${u.bulletElement}>`}u.type==="fraction"&&(u.renderFraction?E=u.renderFraction.call(e,u.currentClass,u.totalClass):E=`<span class="${u.currentClass}"></span> / <span class="${u.totalClass}"></span>`),u.type==="progressbar"&&(u.renderProgressbar?E=u.renderProgressbar.call(e,u.progressbarFillClass):E=`<span class="${u.progressbarFillClass}"></span>`),e.pagination.bullets=[],v.forEach(P=>{u.type!=="custom"&&(P.innerHTML=E||""),u.type==="bullets"&&e.pagination.bullets.push(...P.querySelectorAll(Z(u.bulletClass)))}),u.type!=="custom"&&a("paginationRender",v[0])}function m(){e.params.pagination=De(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const u=e.params.pagination;if(!u.el)return;let y;typeof u.el=="string"&&e.isElement&&(y=e.el.querySelector(u.el)),!y&&typeof u.el=="string"&&(y=[...document.querySelectorAll(u.el)]),y||(y=u.el),!(!y||y.length===0)&&(e.params.uniqueNavElements&&typeof u.el=="string"&&Array.isArray(y)&&y.length>1&&(y=[...e.el.querySelectorAll(u.el)],y.length>1&&(y=y.filter(v=>te(v,".swiper")[0]===e.el)[0])),Array.isArray(y)&&y.length===1&&(y=y[0]),Object.assign(e.pagination,{el:y}),y=B(y),y.forEach(v=>{u.type==="bullets"&&u.clickable&&v.classList.add(...(u.clickableClass||"").split(" ")),v.classList.add(u.modifierClass+u.type),v.classList.add(e.isHorizontal()?u.horizontalClass:u.verticalClass),u.type==="bullets"&&u.dynamicBullets&&(v.classList.add(`${u.modifierClass}${u.type}-dynamic`),c=0,u.dynamicMainBullets<1&&(u.dynamicMainBullets=1)),u.type==="progressbar"&&u.progressbarOpposite&&v.classList.add(u.progressbarOppositeClass),u.clickable&&v.addEventListener("click",o),e.enabled||v.classList.add(u.lockClass)}))}function h(){const u=e.params.pagination;if(n())return;let y=e.pagination.el;y&&(y=B(y),y.forEach(v=>{v.classList.remove(u.hiddenClass),v.classList.remove(u.modifierClass+u.type),v.classList.remove(e.isHorizontal()?u.horizontalClass:u.verticalClass),u.clickable&&(v.classList.remove(...(u.clickableClass||"").split(" ")),v.removeEventListener("click",o))})),e.pagination.bullets&&e.pagination.bullets.forEach(v=>v.classList.remove(...u.bulletActiveClass.split(" ")))}i("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const u=e.params.pagination;let{el:y}=e.pagination;y=B(y),y.forEach(v=>{v.classList.remove(u.horizontalClass,u.verticalClass),v.classList.add(e.isHorizontal()?u.horizontalClass:u.verticalClass)})}),i("init",()=>{e.params.pagination.enabled===!1?g():(m(),d(),l())}),i("activeIndexChange",()=>{typeof e.snapIndex>"u"&&l()}),i("snapIndexChange",()=>{l()}),i("snapGridLengthChange",()=>{d(),l()}),i("destroy",()=>{h()}),i("enable disable",()=>{let{el:u}=e.pagination;u&&(u=B(u),u.forEach(y=>y.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),i("lock unlock",()=>{l()}),i("click",(u,y)=>{const v=y.target,E=B(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&E&&E.length>0&&!v.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&v===e.navigation.nextEl||e.navigation.prevEl&&v===e.navigation.prevEl))return;const P=E[0].classList.contains(e.params.pagination.hiddenClass);a(P===!0?"paginationShow":"paginationHide"),E.forEach(I=>I.classList.toggle(e.params.pagination.hiddenClass))}});const w=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:u}=e.pagination;u&&(u=B(u),u.forEach(y=>y.classList.remove(e.params.pagination.paginationDisabledClass))),m(),d(),l()},g=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:u}=e.pagination;u&&(u=B(u),u.forEach(y=>y.classList.add(e.params.pagination.paginationDisabledClass))),h()};Object.assign(e.pagination,{enable:w,disable:g,render:d,update:l,init:m,destroy:h})}function gs(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r=V();let f=!1,c=null,n=null,p,o,l,d;t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),e.scrollbar={el:null,dragEl:null};function m(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:x,rtlTranslate:b}=e,{dragEl:S,el:C}=x,k=e.params.scrollbar,D=e.params.loop?e.progressLoop:e.progress;let L=o,$=(l-o)*D;b?($=-$,$>0?(L=o-$,$=0):-$+o>l&&(L=l+$)):$<0?(L=o+$,$=0):$+o>l&&(L=l-$),e.isHorizontal()?(S.style.transform=`translate3d(${$}px, 0, 0)`,S.style.width=`${L}px`):(S.style.transform=`translate3d(0px, ${$}px, 0)`,S.style.height=`${L}px`),k.hide&&(clearTimeout(c),C.style.opacity=1,c=setTimeout(()=>{C.style.opacity=0,C.style.transitionDuration="400ms"},1e3))}function h(x){!e.params.scrollbar.el||!e.scrollbar.el||(e.scrollbar.dragEl.style.transitionDuration=`${x}ms`)}function w(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:x}=e,{dragEl:b,el:S}=x;b.style.width="",b.style.height="",l=e.isHorizontal()?S.offsetWidth:S.offsetHeight,d=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),e.params.scrollbar.dragSize==="auto"?o=l*d:o=parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?b.style.width=`${o}px`:b.style.height=`${o}px`,d>=1?S.style.display="none":S.style.display="",e.params.scrollbar.hide&&(S.style.opacity=0),e.params.watchOverflow&&e.enabled&&x.el.classList[e.isLocked?"add":"remove"](e.params.scrollbar.lockClass)}function g(x){return e.isHorizontal()?x.clientX:x.clientY}function u(x){const{scrollbar:b,rtlTranslate:S}=e,{el:C}=b;let k;k=(g(x)-ve(C)[e.isHorizontal()?"left":"top"]-(p!==null?p:o/2))/(l-o),k=Math.max(Math.min(k,1),0),S&&(k=1-k);const D=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*k;e.updateProgress(D),e.setTranslate(D),e.updateActiveIndex(),e.updateSlidesClasses()}function y(x){const b=e.params.scrollbar,{scrollbar:S,wrapperEl:C}=e,{el:k,dragEl:D}=S;f=!0,p=x.target===D?g(x)-x.target.getBoundingClientRect()[e.isHorizontal()?"left":"top"]:null,x.preventDefault(),x.stopPropagation(),C.style.transitionDuration="100ms",D.style.transitionDuration="100ms",u(x),clearTimeout(n),k.style.transitionDuration="0ms",b.hide&&(k.style.opacity=1),e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="none"),a("scrollbarDragStart",x)}function v(x){const{scrollbar:b,wrapperEl:S}=e,{el:C,dragEl:k}=b;f&&(x.preventDefault?x.preventDefault():x.returnValue=!1,u(x),S.style.transitionDuration="0ms",C.style.transitionDuration="0ms",k.style.transitionDuration="0ms",a("scrollbarDragMove",x))}function E(x){const b=e.params.scrollbar,{scrollbar:S,wrapperEl:C}=e,{el:k}=S;f&&(f=!1,e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="",C.style.transitionDuration=""),b.hide&&(clearTimeout(n),n=se(()=>{k.style.opacity=0,k.style.transitionDuration="400ms"},1e3)),a("scrollbarDragEnd",x),b.snapOnRelease&&e.slideToClosest())}function P(x){const{scrollbar:b,params:S}=e,C=b.el;if(!C)return;const k=C,D=S.passiveListeners?{passive:!1,capture:!1}:!1,L=S.passiveListeners?{passive:!0,capture:!1}:!1;if(!k)return;const $=x==="on"?"addEventListener":"removeEventListener";k[$]("pointerdown",y,D),r[$]("pointermove",v,D),r[$]("pointerup",E,L)}function I(){!e.params.scrollbar.el||!e.scrollbar.el||P("on")}function O(){!e.params.scrollbar.el||!e.scrollbar.el||P("off")}function M(){const{scrollbar:x,el:b}=e;e.params.scrollbar=De(e,e.originalParams.scrollbar,e.params.scrollbar,{el:"swiper-scrollbar"});const S=e.params.scrollbar;if(!S.el)return;let C;if(typeof S.el=="string"&&e.isElement&&(C=e.el.querySelector(S.el)),!C&&typeof S.el=="string"){if(C=r.querySelectorAll(S.el),!C.length)return}else C||(C=S.el);e.params.uniqueNavElements&&typeof S.el=="string"&&C.length>1&&b.querySelectorAll(S.el).length===1&&(C=b.querySelector(S.el)),C.length>0&&(C=C[0]),C.classList.add(e.isHorizontal()?S.horizontalClass:S.verticalClass);let k;C&&(k=C.querySelector(Z(e.params.scrollbar.dragClass)),k||(k=q("div",e.params.scrollbar.dragClass),C.append(k))),Object.assign(x,{el:C,dragEl:k}),S.draggable&&I(),C&&C.classList[e.enabled?"remove":"add"](...Q(e.params.scrollbar.lockClass))}function A(){const x=e.params.scrollbar,b=e.scrollbar.el;b&&b.classList.remove(...Q(e.isHorizontal()?x.horizontalClass:x.verticalClass)),O()}i("changeDirection",()=>{if(!e.scrollbar||!e.scrollbar.el)return;const x=e.params.scrollbar;let{el:b}=e.scrollbar;b=B(b),b.forEach(S=>{S.classList.remove(x.horizontalClass,x.verticalClass),S.classList.add(e.isHorizontal()?x.horizontalClass:x.verticalClass)})}),i("init",()=>{e.params.scrollbar.enabled===!1?z():(M(),w(),m())}),i("update resize observerUpdate lock unlock changeDirection",()=>{w()}),i("setTranslate",()=>{m()}),i("setTransition",(x,b)=>{h(b)}),i("enable disable",()=>{const{el:x}=e.scrollbar;x&&x.classList[e.enabled?"remove":"add"](...Q(e.params.scrollbar.lockClass))}),i("destroy",()=>{A()});const T=()=>{e.el.classList.remove(...Q(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.remove(...Q(e.params.scrollbar.scrollbarDisabledClass)),M(),w(),m()},z=()=>{e.el.classList.add(...Q(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.add(...Q(e.params.scrollbar.scrollbarDisabledClass)),A()};Object.assign(e.scrollbar,{enable:T,disable:z,updateSize:w,setTranslate:m,init:M,destroy:A})}function vs(s){let{swiper:e,extendParams:t,on:i}=s;t({parallax:{enabled:!1}});const a="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",r=(n,p)=>{const{rtl:o}=e,l=o?-1:1,d=n.getAttribute("data-swiper-parallax")||"0";let m=n.getAttribute("data-swiper-parallax-x"),h=n.getAttribute("data-swiper-parallax-y");const w=n.getAttribute("data-swiper-parallax-scale"),g=n.getAttribute("data-swiper-parallax-opacity"),u=n.getAttribute("data-swiper-parallax-rotate");if(m||h?(m=m||"0",h=h||"0"):e.isHorizontal()?(m=d,h="0"):(h=d,m="0"),m.indexOf("%")>=0?m=`${parseInt(m,10)*p*l}%`:m=`${m*p*l}px`,h.indexOf("%")>=0?h=`${parseInt(h,10)*p}%`:h=`${h*p}px`,typeof g<"u"&&g!==null){const v=g-(g-1)*(1-Math.abs(p));n.style.opacity=v}let y=`translate3d(${m}, ${h}, 0px)`;if(typeof w<"u"&&w!==null){const v=w-(w-1)*(1-Math.abs(p));y+=` scale(${v})`}if(u&&typeof u<"u"&&u!==null){const v=u*p*-1;y+=` rotate(${v}deg)`}n.style.transform=y},f=()=>{const{el:n,slides:p,progress:o,snapGrid:l,isElement:d}=e,m=Y(n,a);e.isElement&&m.push(...Y(e.hostEl,a)),m.forEach(h=>{r(h,o)}),p.forEach((h,w)=>{let g=h.progress;e.params.slidesPerGroup>1&&e.params.slidesPerView!=="auto"&&(g+=Math.ceil(w/2)-o*(l.length-1)),g=Math.min(Math.max(g,-1),1),h.querySelectorAll(`${a}, [data-swiper-parallax-rotate]`).forEach(u=>{r(u,g)})})},c=function(n){n===void 0&&(n=e.params.speed);const{el:p,hostEl:o}=e,l=[...p.querySelectorAll(a)];e.isElement&&l.push(...o.querySelectorAll(a)),l.forEach(d=>{let m=parseInt(d.getAttribute("data-swiper-parallax-duration"),10)||n;n===0&&(m=0),d.style.transitionDuration=`${m}ms`})};i("beforeInit",()=>{e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)}),i("init",()=>{e.params.parallax.enabled&&f()}),i("setTranslate",()=>{e.params.parallax.enabled&&f()}),i("setTransition",(n,p)=>{e.params.parallax.enabled&&c(p)})}function ws(s){let{swiper:e,extendParams:t,on:i,emit:a}=s;const r=R();t({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),e.zoom={enabled:!1};let f=1,c=!1,n,p;const o=[],l={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},d={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},m={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let h=1;Object.defineProperty(e.zoom,"scale",{get(){return h},set(D){if(h!==D){const L=l.imageEl,$=l.slideEl;a("zoomChange",D,L,$)}h=D}});function w(){if(o.length<2)return 1;const D=o[0].pageX,L=o[0].pageY,$=o[1].pageX,G=o[1].pageY;return Math.sqrt(($-D)**2+(G-L)**2)}function g(){if(o.length<2)return{x:null,y:null};const D=l.imageEl.getBoundingClientRect();return[(o[0].pageX+(o[1].pageX-o[0].pageX)/2-D.x-r.scrollX)/f,(o[0].pageY+(o[1].pageY-o[0].pageY)/2-D.y-r.scrollY)/f]}function u(){return e.isElement?"swiper-slide":`.${e.params.slideClass}`}function y(D){const L=u();return!!(D.target.matches(L)||e.slides.filter($=>$.contains(D.target)).length>0)}function v(D){const L=`.${e.params.zoom.containerClass}`;return!!(D.target.matches(L)||[...e.hostEl.querySelectorAll(L)].filter($=>$.contains(D.target)).length>0)}function E(D){if(D.pointerType==="mouse"&&o.splice(0,o.length),!y(D))return;const L=e.params.zoom;if(n=!1,p=!1,o.push(D),!(o.length<2)){if(n=!0,l.scaleStart=w(),!l.slideEl){l.slideEl=D.target.closest(`.${e.params.slideClass}, swiper-slide`),l.slideEl||(l.slideEl=e.slides[e.activeIndex]);let $=l.slideEl.querySelector(`.${L.containerClass}`);if($&&($=$.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),l.imageEl=$,$?l.imageWrapEl=te(l.imageEl,`.${L.containerClass}`)[0]:l.imageWrapEl=void 0,!l.imageWrapEl){l.imageEl=void 0;return}l.maxRatio=l.imageWrapEl.getAttribute("data-swiper-zoom")||L.maxRatio}if(l.imageEl){const[$,G]=g();l.originX=$,l.originY=G,l.imageEl.style.transitionDuration="0ms"}c=!0}}function P(D){if(!y(D))return;const L=e.params.zoom,$=e.zoom,G=o.findIndex(N=>N.pointerId===D.pointerId);G>=0&&(o[G]=D),!(o.length<2)&&(p=!0,l.scaleMove=w(),l.imageEl&&($.scale=l.scaleMove/l.scaleStart*f,$.scale>l.maxRatio&&($.scale=l.maxRatio-1+($.scale-l.maxRatio+1)**.5),$.scale<L.minRatio&&($.scale=L.minRatio+1-(L.minRatio-$.scale+1)**.5),l.imageEl.style.transform=`translate3d(0,0,0) scale(${$.scale})`))}function I(D){if(!y(D)||D.pointerType==="mouse"&&D.type==="pointerout")return;const L=e.params.zoom,$=e.zoom,G=o.findIndex(N=>N.pointerId===D.pointerId);G>=0&&o.splice(G,1),!(!n||!p)&&(n=!1,p=!1,l.imageEl&&($.scale=Math.max(Math.min($.scale,l.maxRatio),L.minRatio),l.imageEl.style.transitionDuration=`${e.params.speed}ms`,l.imageEl.style.transform=`translate3d(0,0,0) scale(${$.scale})`,f=$.scale,c=!1,$.scale>1&&l.slideEl?l.slideEl.classList.add(`${L.zoomedSlideClass}`):$.scale<=1&&l.slideEl&&l.slideEl.classList.remove(`${L.zoomedSlideClass}`),$.scale===1&&(l.originX=0,l.originY=0,l.slideEl=void 0)))}function O(D){const L=e.device;if(!l.imageEl||d.isTouched)return;L.android&&D.cancelable&&D.preventDefault(),d.isTouched=!0;const $=o.length>0?o[0]:D;d.touchesStart.x=$.pageX,d.touchesStart.y=$.pageY}function M(D){if(!y(D)||!v(D))return;const L=e.zoom;if(!l.imageEl||!d.isTouched||!l.slideEl)return;d.isMoved||(d.width=l.imageEl.offsetWidth,d.height=l.imageEl.offsetHeight,d.startX=Ie(l.imageWrapEl,"x")||0,d.startY=Ie(l.imageWrapEl,"y")||0,l.slideWidth=l.slideEl.offsetWidth,l.slideHeight=l.slideEl.offsetHeight,l.imageWrapEl.style.transitionDuration="0ms");const $=d.width*L.scale,G=d.height*L.scale;if($<l.slideWidth&&G<l.slideHeight)return;if(d.minX=Math.min(l.slideWidth/2-$/2,0),d.maxX=-d.minX,d.minY=Math.min(l.slideHeight/2-G/2,0),d.maxY=-d.minY,d.touchesCurrent.x=o.length>0?o[0].pageX:D.pageX,d.touchesCurrent.y=o.length>0?o[0].pageY:D.pageY,Math.max(Math.abs(d.touchesCurrent.x-d.touchesStart.x),Math.abs(d.touchesCurrent.y-d.touchesStart.y))>5&&(e.allowClick=!1),!d.isMoved&&!c){if(e.isHorizontal()&&(Math.floor(d.minX)===Math.floor(d.startX)&&d.touchesCurrent.x<d.touchesStart.x||Math.floor(d.maxX)===Math.floor(d.startX)&&d.touchesCurrent.x>d.touchesStart.x)){d.isTouched=!1;return}if(!e.isHorizontal()&&(Math.floor(d.minY)===Math.floor(d.startY)&&d.touchesCurrent.y<d.touchesStart.y||Math.floor(d.maxY)===Math.floor(d.startY)&&d.touchesCurrent.y>d.touchesStart.y)){d.isTouched=!1;return}}D.cancelable&&D.preventDefault(),D.stopPropagation(),d.isMoved=!0;const F=(L.scale-f)/(l.maxRatio-e.params.zoom.minRatio),{originX:H,originY:X}=l;d.currentX=d.touchesCurrent.x-d.touchesStart.x+d.startX+F*(d.width-H*2),d.currentY=d.touchesCurrent.y-d.touchesStart.y+d.startY+F*(d.height-X*2),d.currentX<d.minX&&(d.currentX=d.minX+1-(d.minX-d.currentX+1)**.8),d.currentX>d.maxX&&(d.currentX=d.maxX-1+(d.currentX-d.maxX+1)**.8),d.currentY<d.minY&&(d.currentY=d.minY+1-(d.minY-d.currentY+1)**.8),d.currentY>d.maxY&&(d.currentY=d.maxY-1+(d.currentY-d.maxY+1)**.8),m.prevPositionX||(m.prevPositionX=d.touchesCurrent.x),m.prevPositionY||(m.prevPositionY=d.touchesCurrent.y),m.prevTime||(m.prevTime=Date.now()),m.x=(d.touchesCurrent.x-m.prevPositionX)/(Date.now()-m.prevTime)/2,m.y=(d.touchesCurrent.y-m.prevPositionY)/(Date.now()-m.prevTime)/2,Math.abs(d.touchesCurrent.x-m.prevPositionX)<2&&(m.x=0),Math.abs(d.touchesCurrent.y-m.prevPositionY)<2&&(m.y=0),m.prevPositionX=d.touchesCurrent.x,m.prevPositionY=d.touchesCurrent.y,m.prevTime=Date.now(),l.imageWrapEl.style.transform=`translate3d(${d.currentX}px, ${d.currentY}px,0)`}function A(){const D=e.zoom;if(!l.imageEl)return;if(!d.isTouched||!d.isMoved){d.isTouched=!1,d.isMoved=!1;return}d.isTouched=!1,d.isMoved=!1;let L=300,$=300;const G=m.x*L,N=d.currentX+G,F=m.y*$,H=d.currentY+F;m.x!==0&&(L=Math.abs((N-d.currentX)/m.x)),m.y!==0&&($=Math.abs((H-d.currentY)/m.y));const X=Math.max(L,$);d.currentX=N,d.currentY=H;const j=d.width*D.scale,K=d.height*D.scale;d.minX=Math.min(l.slideWidth/2-j/2,0),d.maxX=-d.minX,d.minY=Math.min(l.slideHeight/2-K/2,0),d.maxY=-d.minY,d.currentX=Math.max(Math.min(d.currentX,d.maxX),d.minX),d.currentY=Math.max(Math.min(d.currentY,d.maxY),d.minY),l.imageWrapEl.style.transitionDuration=`${X}ms`,l.imageWrapEl.style.transform=`translate3d(${d.currentX}px, ${d.currentY}px,0)`}function T(){const D=e.zoom;l.slideEl&&e.activeIndex!==e.slides.indexOf(l.slideEl)&&(l.imageEl&&(l.imageEl.style.transform="translate3d(0,0,0) scale(1)"),l.imageWrapEl&&(l.imageWrapEl.style.transform="translate3d(0,0,0)"),l.slideEl.classList.remove(`${e.params.zoom.zoomedSlideClass}`),D.scale=1,f=1,l.slideEl=void 0,l.imageEl=void 0,l.imageWrapEl=void 0,l.originX=0,l.originY=0)}function z(D){const L=e.zoom,$=e.params.zoom;if(!l.slideEl){D&&D.target&&(l.slideEl=D.target.closest(`.${e.params.slideClass}, swiper-slide`)),l.slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?l.slideEl=Y(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:l.slideEl=e.slides[e.activeIndex]);let le=l.slideEl.querySelector(`.${$.containerClass}`);le&&(le=le.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),l.imageEl=le,le?l.imageWrapEl=te(l.imageEl,`.${$.containerClass}`)[0]:l.imageWrapEl=void 0}if(!l.imageEl||!l.imageWrapEl)return;e.params.cssMode&&(e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.touchAction="none"),l.slideEl.classList.add(`${$.zoomedSlideClass}`);let G,N,F,H,X,j,K,ee,Oe,ke,Ge,He,pe,me,ye,be,Se,Ee;typeof d.touchesStart.x>"u"&&D?(G=D.pageX,N=D.pageY):(G=d.touchesStart.x,N=d.touchesStart.y);const ne=typeof D=="number"?D:null;f===1&&ne&&(G=void 0,N=void 0),L.scale=ne||l.imageWrapEl.getAttribute("data-swiper-zoom")||$.maxRatio,f=ne||l.imageWrapEl.getAttribute("data-swiper-zoom")||$.maxRatio,D&&!(f===1&&ne)?(Se=l.slideEl.offsetWidth,Ee=l.slideEl.offsetHeight,F=ve(l.slideEl).left+r.scrollX,H=ve(l.slideEl).top+r.scrollY,X=F+Se/2-G,j=H+Ee/2-N,Oe=l.imageEl.offsetWidth,ke=l.imageEl.offsetHeight,Ge=Oe*L.scale,He=ke*L.scale,pe=Math.min(Se/2-Ge/2,0),me=Math.min(Ee/2-He/2,0),ye=-pe,be=-me,K=X*L.scale,ee=j*L.scale,K<pe&&(K=pe),K>ye&&(K=ye),ee<me&&(ee=me),ee>be&&(ee=be)):(K=0,ee=0),ne&&L.scale===1&&(l.originX=0,l.originY=0),l.imageWrapEl.style.transitionDuration="300ms",l.imageWrapEl.style.transform=`translate3d(${K}px, ${ee}px,0)`,l.imageEl.style.transitionDuration="300ms",l.imageEl.style.transform=`translate3d(0,0,0) scale(${L.scale})`}function x(){const D=e.zoom,L=e.params.zoom;if(!l.slideEl){e.params.virtual&&e.params.virtual.enabled&&e.virtual?l.slideEl=Y(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:l.slideEl=e.slides[e.activeIndex];let $=l.slideEl.querySelector(`.${L.containerClass}`);$&&($=$.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),l.imageEl=$,$?l.imageWrapEl=te(l.imageEl,`.${L.containerClass}`)[0]:l.imageWrapEl=void 0}!l.imageEl||!l.imageWrapEl||(e.params.cssMode&&(e.wrapperEl.style.overflow="",e.wrapperEl.style.touchAction=""),D.scale=1,f=1,l.imageWrapEl.style.transitionDuration="300ms",l.imageWrapEl.style.transform="translate3d(0,0,0)",l.imageEl.style.transitionDuration="300ms",l.imageEl.style.transform="translate3d(0,0,0) scale(1)",l.slideEl.classList.remove(`${L.zoomedSlideClass}`),l.slideEl=void 0,l.originX=0,l.originY=0)}function b(D){const L=e.zoom;L.scale&&L.scale!==1?x():z(D)}function S(){const D=e.params.passiveListeners?{passive:!0,capture:!1}:!1,L=e.params.passiveListeners?{passive:!1,capture:!0}:!0;return{passiveListener:D,activeListenerWithCapture:L}}function C(){const D=e.zoom;if(D.enabled)return;D.enabled=!0;const{passiveListener:L,activeListenerWithCapture:$}=S();e.wrapperEl.addEventListener("pointerdown",E,L),e.wrapperEl.addEventListener("pointermove",P,$),["pointerup","pointercancel","pointerout"].forEach(G=>{e.wrapperEl.addEventListener(G,I,L)}),e.wrapperEl.addEventListener("pointermove",M,$)}function k(){const D=e.zoom;if(!D.enabled)return;D.enabled=!1;const{passiveListener:L,activeListenerWithCapture:$}=S();e.wrapperEl.removeEventListener("pointerdown",E,L),e.wrapperEl.removeEventListener("pointermove",P,$),["pointerup","pointercancel","pointerout"].forEach(G=>{e.wrapperEl.removeEventListener(G,I,L)}),e.wrapperEl.removeEventListener("pointermove",M,$)}i("init",()=>{e.params.zoom.enabled&&C()}),i("destroy",()=>{k()}),i("touchStart",(D,L)=>{e.zoom.enabled&&O(L)}),i("touchEnd",(D,L)=>{e.zoom.enabled&&A()}),i("doubleTap",(D,L)=>{!e.animating&&e.params.zoom.enabled&&e.zoom.enabled&&e.params.zoom.toggle&&b(L)}),i("transitionEnd",()=>{e.zoom.enabled&&e.params.zoom.enabled&&T()}),i("slideChange",()=>{e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&T()}),Object.assign(e.zoom,{enable:C,disable:k,in:z,out:x,toggle:b})}function ys(s){let{swiper:e,extendParams:t,on:i}=s;t({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0};function a(p,o){const l=function(){let w,g,u;return(y,v)=>{for(g=-1,w=y.length;w-g>1;)u=w+g>>1,y[u]<=v?g=u:w=u;return w}}();this.x=p,this.y=o,this.lastIndex=p.length-1;let d,m;return this.interpolate=function(w){return w?(m=l(this.x,w),d=m-1,(w-this.x[d])*(this.y[m]-this.y[d])/(this.x[m]-this.x[d])+this.y[d]):0},this}function r(p){e.controller.spline=e.params.loop?new a(e.slidesGrid,p.slidesGrid):new a(e.snapGrid,p.snapGrid)}function f(p,o){const l=e.controller.control;let d,m;const h=e.constructor;function w(g){if(g.destroyed)return;const u=e.rtlTranslate?-e.translate:e.translate;e.params.controller.by==="slide"&&(r(g),m=-e.controller.spline.interpolate(-u)),(!m||e.params.controller.by==="container")&&(d=(g.maxTranslate()-g.minTranslate())/(e.maxTranslate()-e.minTranslate()),(Number.isNaN(d)||!Number.isFinite(d))&&(d=1),m=(u-e.minTranslate())*d+g.minTranslate()),e.params.controller.inverse&&(m=g.maxTranslate()-m),g.updateProgress(m),g.setTranslate(m,e),g.updateActiveIndex(),g.updateSlidesClasses()}if(Array.isArray(l))for(let g=0;g<l.length;g+=1)l[g]!==o&&l[g]instanceof h&&w(l[g]);else l instanceof h&&o!==l&&w(l)}function c(p,o){const l=e.constructor,d=e.controller.control;let m;function h(w){w.destroyed||(w.setTransition(p,e),p!==0&&(w.transitionStart(),w.params.autoHeight&&se(()=>{w.updateAutoHeight()}),ce(w.wrapperEl,()=>{d&&w.transitionEnd()})))}if(Array.isArray(d))for(m=0;m<d.length;m+=1)d[m]!==o&&d[m]instanceof l&&h(d[m]);else d instanceof l&&o!==d&&h(d)}function n(){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)}i("beforeInit",()=>{if(typeof window<"u"&&(typeof e.params.controller.control=="string"||e.params.controller.control instanceof HTMLElement)){const p=document.querySelector(e.params.controller.control);if(p&&p.swiper)e.controller.control=p.swiper;else if(p){const o=l=>{e.controller.control=l.detail[0],e.update(),p.removeEventListener("init",o)};p.addEventListener("init",o)}return}e.controller.control=e.params.controller.control}),i("update",()=>{n()}),i("resize",()=>{n()}),i("observerUpdate",()=>{n()}),i("setTranslate",(p,o,l)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTranslate(o,l)}),i("setTransition",(p,o,l)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTransition(o,l)}),Object.assign(e.controller,{setTranslate:f,setTransition:c})}function bs(s){let{swiper:e,extendParams:t,on:i}=s;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}}),e.a11y={clicked:!1};let a=null;function r(b){const S=a;S.length!==0&&(S.innerHTML="",S.innerHTML=b)}function f(b){const S=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(b).replace(/x/g,S)}function c(b){b=B(b),b.forEach(S=>{S.setAttribute("tabIndex","0")})}function n(b){b=B(b),b.forEach(S=>{S.setAttribute("tabIndex","-1")})}function p(b,S){b=B(b),b.forEach(C=>{C.setAttribute("role",S)})}function o(b,S){b=B(b),b.forEach(C=>{C.setAttribute("aria-roledescription",S)})}function l(b,S){b=B(b),b.forEach(C=>{C.setAttribute("aria-controls",S)})}function d(b,S){b=B(b),b.forEach(C=>{C.setAttribute("aria-label",S)})}function m(b,S){b=B(b),b.forEach(C=>{C.setAttribute("id",S)})}function h(b,S){b=B(b),b.forEach(C=>{C.setAttribute("aria-live",S)})}function w(b){b=B(b),b.forEach(S=>{S.setAttribute("aria-disabled",!0)})}function g(b){b=B(b),b.forEach(S=>{S.setAttribute("aria-disabled",!1)})}function u(b){if(b.keyCode!==13&&b.keyCode!==32)return;const S=e.params.a11y,C=b.target;e.pagination&&e.pagination.el&&(C===e.pagination.el||e.pagination.el.contains(b.target))&&!b.target.matches(Z(e.params.pagination.bulletClass))||(e.navigation&&e.navigation.nextEl&&C===e.navigation.nextEl&&(e.isEnd&&!e.params.loop||e.slideNext(),e.isEnd?r(S.lastSlideMessage):r(S.nextSlideMessage)),e.navigation&&e.navigation.prevEl&&C===e.navigation.prevEl&&(e.isBeginning&&!e.params.loop||e.slidePrev(),e.isBeginning?r(S.firstSlideMessage):r(S.prevSlideMessage)),e.pagination&&C.matches(Z(e.params.pagination.bulletClass))&&C.click())}function y(){if(e.params.loop||e.params.rewind||!e.navigation)return;const{nextEl:b,prevEl:S}=e.navigation;S&&(e.isBeginning?(w(S),n(S)):(g(S),c(S))),b&&(e.isEnd?(w(b),n(b)):(g(b),c(b)))}function v(){return e.pagination&&e.pagination.bullets&&e.pagination.bullets.length}function E(){return v()&&e.params.pagination.clickable}function P(){const b=e.params.a11y;v()&&e.pagination.bullets.forEach(S=>{e.params.pagination.clickable&&(c(S),e.params.pagination.renderBullet||(p(S,"button"),d(S,b.paginationBulletMessage.replace(/\{\{index\}\}/,fe(S)+1)))),S.matches(Z(e.params.pagination.bulletActiveClass))?S.setAttribute("aria-current","true"):S.removeAttribute("aria-current")})}const I=(b,S,C)=>{c(b),b.tagName!=="BUTTON"&&(p(b,"button"),b.addEventListener("keydown",u)),d(b,C),l(b,S)},O=()=>{e.a11y.clicked=!0},M=()=>{requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.destroyed||(e.a11y.clicked=!1)})})},A=b=>{if(e.a11y.clicked)return;const S=b.target.closest(`.${e.params.slideClass}, swiper-slide`);if(!S||!e.slides.includes(S))return;const C=e.slides.indexOf(S)===e.activeIndex,k=e.params.watchSlidesProgress&&e.visibleSlides&&e.visibleSlides.includes(S);C||k||b.sourceCapabilities&&b.sourceCapabilities.firesTouchEvents||(e.isHorizontal()?e.el.scrollLeft=0:e.el.scrollTop=0,e.slideTo(e.slides.indexOf(S),0))},T=()=>{const b=e.params.a11y;b.itemRoleDescriptionMessage&&o(e.slides,b.itemRoleDescriptionMessage),b.slideRole&&p(e.slides,b.slideRole);const S=e.slides.length;b.slideLabelMessage&&e.slides.forEach((C,k)=>{const D=e.params.loop?parseInt(C.getAttribute("data-swiper-slide-index"),10):k,L=b.slideLabelMessage.replace(/\{\{index\}\}/,D+1).replace(/\{\{slidesLength\}\}/,S);d(C,L)})},z=()=>{const b=e.params.a11y;e.el.append(a);const S=e.el;b.containerRoleDescriptionMessage&&o(S,b.containerRoleDescriptionMessage),b.containerMessage&&d(S,b.containerMessage);const C=e.wrapperEl,k=b.id||C.getAttribute("id")||`swiper-wrapper-${f(16)}`,D=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";m(C,k),h(C,D),T();let{nextEl:L,prevEl:$}=e.navigation?e.navigation:{};L=B(L),$=B($),L&&L.forEach(G=>I(G,k,b.nextSlideMessage)),$&&$.forEach(G=>I(G,k,b.prevSlideMessage)),E()&&B(e.pagination.el).forEach(N=>{N.addEventListener("keydown",u)}),e.el.addEventListener("focus",A,!0),e.el.addEventListener("pointerdown",O,!0),e.el.addEventListener("pointerup",M,!0)};function x(){a&&a.remove();let{nextEl:b,prevEl:S}=e.navigation?e.navigation:{};b=B(b),S=B(S),b&&b.forEach(C=>C.removeEventListener("keydown",u)),S&&S.forEach(C=>C.removeEventListener("keydown",u)),E()&&B(e.pagination.el).forEach(k=>{k.removeEventListener("keydown",u)}),e.el.removeEventListener("focus",A,!0),e.el.removeEventListener("pointerdown",O,!0),e.el.removeEventListener("pointerup",M,!0)}i("beforeInit",()=>{a=q("span",e.params.a11y.notificationClass),a.setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true")}),i("afterInit",()=>{e.params.a11y.enabled&&z()}),i("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{e.params.a11y.enabled&&T()}),i("fromEdge toEdge afterInit lock unlock",()=>{e.params.a11y.enabled&&y()}),i("paginationUpdate",()=>{e.params.a11y.enabled&&P()}),i("destroy",()=>{e.params.a11y.enabled&&x()})}function Ss(s){let{swiper:e,extendParams:t,on:i}=s;t({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let a=!1,r={};const f=m=>m.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),c=m=>{const h=R();let w;m?w=new URL(m):w=h.location;const g=w.pathname.slice(1).split("/").filter(E=>E!==""),u=g.length,y=g[u-2],v=g[u-1];return{key:y,value:v}},n=(m,h)=>{const w=R();if(!a||!e.params.history.enabled)return;let g;e.params.url?g=new URL(e.params.url):g=w.location;const u=e.slides[h];let y=f(u.getAttribute("data-history"));if(e.params.history.root.length>0){let E=e.params.history.root;E[E.length-1]==="/"&&(E=E.slice(0,E.length-1)),y=`${E}/${m?`${m}/`:""}${y}`}else g.pathname.includes(m)||(y=`${m?`${m}/`:""}${y}`);e.params.history.keepQuery&&(y+=g.search);const v=w.history.state;v&&v.value===y||(e.params.history.replaceState?w.history.replaceState({value:y},null,y):w.history.pushState({value:y},null,y))},p=(m,h,w)=>{if(h)for(let g=0,u=e.slides.length;g<u;g+=1){const y=e.slides[g];if(f(y.getAttribute("data-history"))===h){const E=e.getSlideIndex(y);e.slideTo(E,m,w)}}else e.slideTo(0,m,w)},o=()=>{r=c(e.params.url),p(e.params.speed,r.value,!1)},l=()=>{const m=R();if(e.params.history){if(!m.history||!m.history.pushState){e.params.history.enabled=!1,e.params.hashNavigation.enabled=!0;return}if(a=!0,r=c(e.params.url),!r.key&&!r.value){e.params.history.replaceState||m.addEventListener("popstate",o);return}p(0,r.value,e.params.runCallbacksOnInit),e.params.history.replaceState||m.addEventListener("popstate",o)}},d=()=>{const m=R();e.params.history.replaceState||m.removeEventListener("popstate",o)};i("init",()=>{e.params.history.enabled&&l()}),i("destroy",()=>{e.params.history.enabled&&d()}),i("transitionEnd _freeModeNoMomentumRelease",()=>{a&&n(e.params.history.key,e.activeIndex)}),i("slideChange",()=>{a&&e.params.cssMode&&n(e.params.history.key,e.activeIndex)})}function Es(s){let{swiper:e,extendParams:t,emit:i,on:a}=s,r=!1;const f=V(),c=R();t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(d,m){if(e.virtual&&e.params.virtual.enabled){const h=e.slides.filter(g=>g.getAttribute("data-hash")===m)[0];return h?parseInt(h.getAttribute("data-swiper-slide-index"),10):0}return e.getSlideIndex(Y(e.slidesEl,`.${e.params.slideClass}[data-hash="${m}"], swiper-slide[data-hash="${m}"]`)[0])}}});const n=()=>{i("hashChange");const d=f.location.hash.replace("#",""),m=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],h=m?m.getAttribute("data-hash"):"";if(d!==h){const w=e.params.hashNavigation.getSlideIndex(e,d);if(typeof w>"u"||Number.isNaN(w))return;e.slideTo(w)}},p=()=>{if(!r||!e.params.hashNavigation.enabled)return;const d=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],m=d?d.getAttribute("data-hash")||d.getAttribute("data-history"):"";e.params.hashNavigation.replaceState&&c.history&&c.history.replaceState?(c.history.replaceState(null,null,`#${m}`||""),i("hashSet")):(f.location.hash=m||"",i("hashSet"))},o=()=>{if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;r=!0;const d=f.location.hash.replace("#","");if(d){const h=e.params.hashNavigation.getSlideIndex(e,d);e.slideTo(h||0,0,e.params.runCallbacksOnInit,!0)}e.params.hashNavigation.watchState&&c.addEventListener("hashchange",n)},l=()=>{e.params.hashNavigation.watchState&&c.removeEventListener("hashchange",n)};a("init",()=>{e.params.hashNavigation.enabled&&o()}),a("destroy",()=>{e.params.hashNavigation.enabled&&l()}),a("transitionEnd _freeModeNoMomentumRelease",()=>{r&&p()}),a("slideChange",()=>{r&&e.params.cssMode&&p()})}function xs(s){let{swiper:e,extendParams:t,on:i,emit:a,params:r}=s;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let f,c,n=r&&r.autoplay?r.autoplay.delay:3e3,p=r&&r.autoplay?r.autoplay.delay:3e3,o,l=new Date().getTime(),d,m,h,w,g,u,y;function v(L){!e||e.destroyed||!e.wrapperEl||L.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",v),!y&&T())}const E=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?d=!0:d&&(p=o,d=!1);const L=e.autoplay.paused?o:l+p-new Date().getTime();e.autoplay.timeLeft=L,a("autoplayTimeLeft",L,L/n),c=requestAnimationFrame(()=>{E()})},P=()=>{let L;return e.virtual&&e.params.virtual.enabled?L=e.slides.filter(G=>G.classList.contains("swiper-slide-active"))[0]:L=e.slides[e.activeIndex],L?parseInt(L.getAttribute("data-swiper-autoplay"),10):void 0},I=L=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(c),E();let $=typeof L>"u"?e.params.autoplay.delay:L;n=e.params.autoplay.delay,p=e.params.autoplay.delay;const G=P();!Number.isNaN(G)&&G>0&&typeof L>"u"&&($=G,n=G,p=G),o=$;const N=e.params.speed,F=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(N,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,N,!0,!0),a("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(N,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,N,!0,!0),a("autoplay")),e.params.cssMode&&(l=new Date().getTime(),requestAnimationFrame(()=>{I()})))};return $>0?(clearTimeout(f),f=setTimeout(()=>{F()},$)):requestAnimationFrame(()=>{F()}),$},O=()=>{l=new Date().getTime(),e.autoplay.running=!0,I(),a("autoplayStart")},M=()=>{e.autoplay.running=!1,clearTimeout(f),cancelAnimationFrame(c),a("autoplayStop")},A=(L,$)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(f),L||(u=!0);const G=()=>{a("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",v):T()};if(e.autoplay.paused=!0,$){g&&(o=e.params.autoplay.delay),g=!1,G();return}o=(o||e.params.autoplay.delay)-(new Date().getTime()-l),!(e.isEnd&&o<0&&!e.params.loop)&&(o<0&&(o=0),G())},T=()=>{e.isEnd&&o<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(l=new Date().getTime(),u?(u=!1,I(o)):I(),e.autoplay.paused=!1,a("autoplayResume"))},z=()=>{if(e.destroyed||!e.autoplay.running)return;const L=V();L.visibilityState==="hidden"&&(u=!0,A(!0)),L.visibilityState==="visible"&&T()},x=L=>{L.pointerType==="mouse"&&(u=!0,y=!0,!(e.animating||e.autoplay.paused)&&A(!0))},b=L=>{L.pointerType==="mouse"&&(y=!1,e.autoplay.paused&&T())},S=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",x),e.el.addEventListener("pointerleave",b))},C=()=>{e.el.removeEventListener("pointerenter",x),e.el.removeEventListener("pointerleave",b)},k=()=>{V().addEventListener("visibilitychange",z)},D=()=>{V().removeEventListener("visibilitychange",z)};i("init",()=>{e.params.autoplay.enabled&&(S(),k(),O())}),i("destroy",()=>{C(),D(),e.autoplay.running&&M()}),i("_freeModeStaticRelease",()=>{(h||u)&&T()}),i("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?M():A(!0,!0)}),i("beforeTransitionStart",(L,$,G)=>{e.destroyed||!e.autoplay.running||(G||!e.params.autoplay.disableOnInteraction?A(!0,!0):M())}),i("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){M();return}m=!0,h=!1,u=!1,w=setTimeout(()=>{u=!0,h=!0,A(!0)},200)}}),i("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!m)){if(clearTimeout(w),clearTimeout(f),e.params.autoplay.disableOnInteraction){h=!1,m=!1;return}h&&e.params.cssMode&&T(),h=!1,m=!1}}),i("slideChange",()=>{e.destroyed||!e.autoplay.running||(g=!0)}),Object.assign(e.autoplay,{start:O,stop:M,pause:A,resume:T})}function Ts(s){let{swiper:e,extendParams:t,on:i}=s;t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let a=!1,r=!1;e.thumbs={swiper:null};function f(){const p=e.thumbs.swiper;if(!p||p.destroyed)return;const o=p.clickedIndex,l=p.clickedSlide;if(l&&l.classList.contains(e.params.thumbs.slideThumbActiveClass)||typeof o>"u"||o===null)return;let d;p.params.loop?d=parseInt(p.clickedSlide.getAttribute("data-swiper-slide-index"),10):d=o,e.params.loop?e.slideToLoop(d):e.slideTo(d)}function c(){const{thumbs:p}=e.params;if(a)return!1;a=!0;const o=e.constructor;if(p.swiper instanceof o)e.thumbs.swiper=p.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper.update();else if(oe(p.swiper)){const l=Object.assign({},p.swiper);Object.assign(l,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new o(l),r=!0}return e.thumbs.swiper.el.classList.add(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",f),!0}function n(p){const o=e.thumbs.swiper;if(!o||o.destroyed)return;const l=o.params.slidesPerView==="auto"?o.slidesPerViewDynamic():o.params.slidesPerView;let d=1;const m=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView>1&&!e.params.centeredSlides&&(d=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(d=1),d=Math.floor(d),o.slides.forEach(g=>g.classList.remove(m)),o.params.loop||o.params.virtual&&o.params.virtual.enabled)for(let g=0;g<d;g+=1)Y(o.slidesEl,`[data-swiper-slide-index="${e.realIndex+g}"]`).forEach(u=>{u.classList.add(m)});else for(let g=0;g<d;g+=1)o.slides[e.realIndex+g]&&o.slides[e.realIndex+g].classList.add(m);const h=e.params.thumbs.autoScrollOffset,w=h&&!o.params.loop;if(e.realIndex!==o.realIndex||w){const g=o.activeIndex;let u,y;if(o.params.loop){const v=o.slides.filter(E=>E.getAttribute("data-swiper-slide-index")===`${e.realIndex}`)[0];u=o.slides.indexOf(v),y=e.activeIndex>e.previousIndex?"next":"prev"}else u=e.realIndex,y=u>e.previousIndex?"next":"prev";w&&(u+=y==="next"?h:-1*h),o.visibleSlidesIndexes&&o.visibleSlidesIndexes.indexOf(u)<0&&(o.params.centeredSlides?u>g?u=u-Math.floor(l/2)+1:u=u+Math.floor(l/2)-1:u>g&&o.params.slidesPerGroup,o.slideTo(u,p?0:void 0))}}i("beforeInit",()=>{const{thumbs:p}=e.params;if(!(!p||!p.swiper))if(typeof p.swiper=="string"||p.swiper instanceof HTMLElement){const o=V(),l=()=>{const m=typeof p.swiper=="string"?o.querySelector(p.swiper):p.swiper;if(m&&m.swiper)p.swiper=m.swiper,c(),n(!0);else if(m){const h=w=>{p.swiper=w.detail[0],m.removeEventListener("init",h),c(),n(!0),p.swiper.update(),e.update()};m.addEventListener("init",h)}return m},d=()=>{if(e.destroyed)return;l()||requestAnimationFrame(d)};requestAnimationFrame(d)}else c(),n(!0)}),i("slideChange update resize observerUpdate",()=>{n()}),i("setTransition",(p,o)=>{const l=e.thumbs.swiper;!l||l.destroyed||l.setTransition(o)}),i("beforeDestroy",()=>{const p=e.thumbs.swiper;!p||p.destroyed||r&&p.destroy()}),Object.assign(e.thumbs,{init:c,update:n})}function Ms(s){let{swiper:e,extendParams:t,emit:i,once:a}=s;t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}});function r(){if(e.params.cssMode)return;const n=e.getTranslate();e.setTranslate(n),e.setTransition(0),e.touchEventsData.velocities.length=0,e.freeMode.onTouchEnd({currentPos:e.rtl?e.translate:-e.translate})}function f(){if(e.params.cssMode)return;const{touchEventsData:n,touches:p}=e;n.velocities.length===0&&n.velocities.push({position:p[e.isHorizontal()?"startX":"startY"],time:n.touchStartTime}),n.velocities.push({position:p[e.isHorizontal()?"currentX":"currentY"],time:U()})}function c(n){let{currentPos:p}=n;if(e.params.cssMode)return;const{params:o,wrapperEl:l,rtlTranslate:d,snapGrid:m,touchEventsData:h}=e,g=U()-h.touchStartTime;if(p<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(p>-e.maxTranslate()){e.slides.length<m.length?e.slideTo(m.length-1):e.slideTo(e.slides.length-1);return}if(o.freeMode.momentum){if(h.velocities.length>1){const M=h.velocities.pop(),A=h.velocities.pop(),T=M.position-A.position,z=M.time-A.time;e.velocity=T/z,e.velocity/=2,Math.abs(e.velocity)<o.freeMode.minimumVelocity&&(e.velocity=0),(z>150||U()-M.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=o.freeMode.momentumVelocityRatio,h.velocities.length=0;let u=1e3*o.freeMode.momentumRatio;const y=e.velocity*u;let v=e.translate+y;d&&(v=-v);let E=!1,P;const I=Math.abs(e.velocity)*20*o.freeMode.momentumBounceRatio;let O;if(v<e.maxTranslate())o.freeMode.momentumBounce?(v+e.maxTranslate()<-I&&(v=e.maxTranslate()-I),P=e.maxTranslate(),E=!0,h.allowMomentumBounce=!0):v=e.maxTranslate(),o.loop&&o.centeredSlides&&(O=!0);else if(v>e.minTranslate())o.freeMode.momentumBounce?(v-e.minTranslate()>I&&(v=e.minTranslate()+I),P=e.minTranslate(),E=!0,h.allowMomentumBounce=!0):v=e.minTranslate(),o.loop&&o.centeredSlides&&(O=!0);else if(o.freeMode.sticky){let M;for(let A=0;A<m.length;A+=1)if(m[A]>-v){M=A;break}Math.abs(m[M]-v)<Math.abs(m[M-1]-v)||e.swipeDirection==="next"?v=m[M]:v=m[M-1],v=-v}if(O&&a("transitionEnd",()=>{e.loopFix()}),e.velocity!==0){if(d?u=Math.abs((-v-e.translate)/e.velocity):u=Math.abs((v-e.translate)/e.velocity),o.freeMode.sticky){const M=Math.abs((d?-v:v)-e.translate),A=e.slidesSizesGrid[e.activeIndex];M<A?u=o.speed:M<2*A?u=o.speed*1.5:u=o.speed*2.5}}else if(o.freeMode.sticky){e.slideToClosest();return}o.freeMode.momentumBounce&&E?(e.updateProgress(P),e.setTransition(u),e.setTranslate(v),e.transitionStart(!0,e.swipeDirection),e.animating=!0,ce(l,()=>{!e||e.destroyed||!h.allowMomentumBounce||(i("momentumBounce"),e.setTransition(o.speed),setTimeout(()=>{e.setTranslate(P),ce(l,()=>{!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(i("_freeModeNoMomentumRelease"),e.updateProgress(v),e.setTransition(u),e.setTranslate(v),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,ce(l,()=>{!e||e.destroyed||e.transitionEnd()}))):e.updateProgress(v),e.updateActiveIndex(),e.updateSlidesClasses()}else if(o.freeMode.sticky){e.slideToClosest();return}else o.freeMode&&i("_freeModeNoMomentumRelease");(!o.freeMode.momentum||g>=o.longSwipesMs)&&(i("_freeModeStaticRelease"),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}Object.assign(e,{freeMode:{onTouchStart:r,onTouchMove:f,onTouchEnd:c}})}function Cs(s){let{swiper:e,extendParams:t,on:i}=s;t({grid:{rows:1,fill:"column"}});let a,r,f,c;const n=()=>{let w=e.params.spaceBetween;return typeof w=="string"&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*e.size:typeof w=="string"&&(w=parseFloat(w)),w},p=w=>{const{slidesPerView:g}=e.params,{rows:u,fill:y}=e.params.grid,v=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:w.length;f=Math.floor(v/u),Math.floor(v/u)===v/u?a=v:a=Math.ceil(v/u)*u,g!=="auto"&&y==="row"&&(a=Math.max(a,g*u)),r=a/u},o=()=>{e.slides&&e.slides.forEach(w=>{w.swiperSlideGridSet&&(w.style.height="",w.style[e.getDirectionLabel("margin-top")]="")})},l=(w,g,u)=>{const{slidesPerGroup:y}=e.params,v=n(),{rows:E,fill:P}=e.params.grid,I=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:u.length;let O,M,A;if(P==="row"&&y>1){const T=Math.floor(w/(y*E)),z=w-E*y*T,x=T===0?y:Math.min(Math.ceil((I-T*E*y)/E),y);A=Math.floor(z/x),M=z-A*x+T*y,O=M+A*a/E,g.style.order=O}else P==="column"?(M=Math.floor(w/E),A=w-M*E,(M>f||M===f&&A===E-1)&&(A+=1,A>=E&&(A=0,M+=1))):(A=Math.floor(w/r),M=w-A*r);g.row=A,g.column=M,g.style.height=`calc((100% - ${(E-1)*v}px) / ${E})`,g.style[e.getDirectionLabel("margin-top")]=A!==0?v&&`${v}px`:"",g.swiperSlideGridSet=!0},d=(w,g)=>{const{centeredSlides:u,roundLengths:y}=e.params,v=n(),{rows:E}=e.params.grid;if(e.virtualSize=(w+v)*a,e.virtualSize=Math.ceil(e.virtualSize/E)-v,e.params.cssMode||(e.wrapperEl.style[e.getDirectionLabel("width")]=`${e.virtualSize+v}px`),u){const P=[];for(let I=0;I<g.length;I+=1){let O=g[I];y&&(O=Math.floor(O)),g[I]<e.virtualSize+g[0]&&P.push(O)}g.splice(0,g.length),g.push(...P)}},m=()=>{c=e.params.grid&&e.params.grid.rows>1},h=()=>{const{params:w,el:g}=e,u=w.grid&&w.grid.rows>1;c&&!u?(g.classList.remove(`${w.containerModifierClass}grid`,`${w.containerModifierClass}grid-column`),f=1,e.emitContainerClasses()):!c&&u&&(g.classList.add(`${w.containerModifierClass}grid`),w.grid.fill==="column"&&g.classList.add(`${w.containerModifierClass}grid-column`),e.emitContainerClasses()),c=u};i("init",m),i("update",h),e.grid={initSlides:p,unsetSlides:o,updateSlide:l,updateWrapperSize:d}}function Ps(s){const e=this,{params:t,slidesEl:i}=e;t.loop&&e.loopDestroy();const a=r=>{if(typeof r=="string"){const f=document.createElement("div");f.innerHTML=r,i.append(f.children[0]),f.innerHTML=""}else i.append(r)};if(typeof s=="object"&&"length"in s)for(let r=0;r<s.length;r+=1)s[r]&&a(s[r]);else a(s);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update()}function Ls(s){const e=this,{params:t,activeIndex:i,slidesEl:a}=e;t.loop&&e.loopDestroy();let r=i+1;const f=c=>{if(typeof c=="string"){const n=document.createElement("div");n.innerHTML=c,a.prepend(n.children[0]),n.innerHTML=""}else a.prepend(c)};if(typeof s=="object"&&"length"in s){for(let c=0;c<s.length;c+=1)s[c]&&f(s[c]);r=i+s.length}else f(s);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update(),e.slideTo(r,0,!1)}function Is(s,e){const t=this,{params:i,activeIndex:a,slidesEl:r}=t;let f=a;i.loop&&(f-=t.loopedSlides,t.loopDestroy(),t.recalcSlides());const c=t.slides.length;if(s<=0){t.prependSlide(e);return}if(s>=c){t.appendSlide(e);return}let n=f>s?f+1:f;const p=[];for(let o=c-1;o>=s;o-=1){const l=t.slides[o];l.remove(),p.unshift(l)}if(typeof e=="object"&&"length"in e){for(let o=0;o<e.length;o+=1)e[o]&&r.append(e[o]);n=f>s?f+e.length:f}else r.append(e);for(let o=0;o<p.length;o+=1)r.append(p[o]);t.recalcSlides(),i.loop&&t.loopCreate(),(!i.observer||t.isElement)&&t.update(),i.loop?t.slideTo(n+t.loopedSlides,0,!1):t.slideTo(n,0,!1)}function zs(s){const e=this,{params:t,activeIndex:i}=e;let a=i;t.loop&&(a-=e.loopedSlides,e.loopDestroy());let r=a,f;if(typeof s=="object"&&"length"in s){for(let c=0;c<s.length;c+=1)f=s[c],e.slides[f]&&e.slides[f].remove(),f<r&&(r-=1);r=Math.max(r,0)}else f=s,e.slides[f]&&e.slides[f].remove(),f<r&&(r-=1),r=Math.max(r,0);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update(),t.loop?e.slideTo(r+e.loopedSlides,0,!1):e.slideTo(r,0,!1)}function As(){const s=this,e=[];for(let t=0;t<s.slides.length;t+=1)e.push(t);s.removeSlide(e)}function $s(s){let{swiper:e}=s;Object.assign(e,{appendSlide:Ps.bind(e),prependSlide:Ls.bind(e),addSlide:Is.bind(e),removeSlide:zs.bind(e),removeAllSlides:As.bind(e)})}function ae(s){const{effect:e,swiper:t,on:i,setTranslate:a,setTransition:r,overwriteParams:f,perspective:c,recreateShadows:n,getEffectParams:p}=s;i("beforeInit",()=>{if(t.params.effect!==e)return;t.classNames.push(`${t.params.containerModifierClass}${e}`),c&&c()&&t.classNames.push(`${t.params.containerModifierClass}3d`);const l=f?f():{};Object.assign(t.params,l),Object.assign(t.originalParams,l)}),i("setTranslate",()=>{t.params.effect===e&&a()}),i("setTransition",(l,d)=>{t.params.effect===e&&r(d)}),i("transitionEnd",()=>{if(t.params.effect===e&&n){if(!p||!p().slideShadows)return;t.slides.forEach(l=>{l.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(d=>d.remove())}),n()}});let o;i("virtualUpdate",()=>{t.params.effect===e&&(t.slides.length||(o=!0),requestAnimationFrame(()=>{o&&t.slides&&t.slides.length&&(a(),o=!1)}))})}function ue(s,e){const t=ie(e);return t!==e&&(t.style.backfaceVisibility="hidden",t.style["-webkit-backface-visibility"]="hidden"),t}function we(s){let{swiper:e,duration:t,transformElements:i,allSlides:a}=s;const{activeIndex:r}=e,f=c=>c.parentElement?c.parentElement:e.slides.filter(p=>p.shadowRoot&&p.shadowRoot===c.parentNode)[0];if(e.params.virtualTranslate&&t!==0){let c=!1,n;a?n=i:n=i.filter(p=>{const o=p.classList.contains("swiper-slide-transform")?f(p):p;return e.getSlideIndex(o)===r}),n.forEach(p=>{ce(p,()=>{if(c||!e||e.destroyed)return;c=!0,e.animating=!1;const o=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(o)})})}}function Ds(s){let{swiper:e,extendParams:t,on:i}=s;t({fadeEffect:{crossFade:!1}}),ae({effect:"fade",swiper:e,on:i,setTranslate:()=>{const{slides:f}=e,c=e.params.fadeEffect;for(let n=0;n<f.length;n+=1){const p=e.slides[n];let l=-p.swiperSlideOffset;e.params.virtualTranslate||(l-=e.translate);let d=0;e.isHorizontal()||(d=l,l=0);const m=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(p.progress),0):1+Math.min(Math.max(p.progress,-1),0),h=ue(c,p);h.style.opacity=m,h.style.transform=`translate3d(${l}px, ${d}px, 0px)`}},setTransition:f=>{const c=e.slides.map(n=>ie(n));c.forEach(n=>{n.style.transitionDuration=`${f}ms`}),we({swiper:e,duration:f,transformElements:c,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function Os(s){let{swiper:e,extendParams:t,on:i}=s;t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const a=(n,p,o)=>{let l=o?n.querySelector(".swiper-slide-shadow-left"):n.querySelector(".swiper-slide-shadow-top"),d=o?n.querySelector(".swiper-slide-shadow-right"):n.querySelector(".swiper-slide-shadow-bottom");l||(l=q("div",`swiper-slide-shadow-cube swiper-slide-shadow-${o?"left":"top"}`.split(" ")),n.append(l)),d||(d=q("div",`swiper-slide-shadow-cube swiper-slide-shadow-${o?"right":"bottom"}`.split(" ")),n.append(d)),l&&(l.style.opacity=Math.max(-p,0)),d&&(d.style.opacity=Math.max(p,0))};ae({effect:"cube",swiper:e,on:i,setTranslate:()=>{const{el:n,wrapperEl:p,slides:o,width:l,height:d,rtlTranslate:m,size:h,browser:w}=e,g=e.params.cubeEffect,u=e.isHorizontal(),y=e.virtual&&e.params.virtual.enabled;let v=0,E;g.shadow&&(u?(E=e.wrapperEl.querySelector(".swiper-cube-shadow"),E||(E=q("div","swiper-cube-shadow"),e.wrapperEl.append(E)),E.style.height=`${l}px`):(E=n.querySelector(".swiper-cube-shadow"),E||(E=q("div","swiper-cube-shadow"),n.append(E))));for(let I=0;I<o.length;I+=1){const O=o[I];let M=I;y&&(M=parseInt(O.getAttribute("data-swiper-slide-index"),10));let A=M*90,T=Math.floor(A/360);m&&(A=-A,T=Math.floor(-A/360));const z=Math.max(Math.min(O.progress,1),-1);let x=0,b=0,S=0;M%4===0?(x=-T*4*h,S=0):(M-1)%4===0?(x=0,S=-T*4*h):(M-2)%4===0?(x=h+T*4*h,S=h):(M-3)%4===0&&(x=-h,S=3*h+h*4*T),m&&(x=-x),u||(b=x,x=0);const C=`rotateX(${u?0:-A}deg) rotateY(${u?A:0}deg) translate3d(${x}px, ${b}px, ${S}px)`;z<=1&&z>-1&&(v=M*90+z*90,m&&(v=-M*90-z*90),e.browser&&e.browser.need3dFix&&Math.abs(v)/90%2===1&&(v+=.001)),O.style.transform=C,g.slideShadows&&a(O,z,u)}if(p.style.transformOrigin=`50% 50% -${h/2}px`,p.style["-webkit-transform-origin"]=`50% 50% -${h/2}px`,g.shadow)if(u)E.style.transform=`translate3d(0px, ${l/2+g.shadowOffset}px, ${-l/2}px) rotateX(89.99deg) rotateZ(0deg) scale(${g.shadowScale})`;else{const I=Math.abs(v)-Math.floor(Math.abs(v)/90)*90,O=1.5-(Math.sin(I*2*Math.PI/360)/2+Math.cos(I*2*Math.PI/360)/2),M=g.shadowScale,A=g.shadowScale/O,T=g.shadowOffset;E.style.transform=`scale3d(${M}, 1, ${A}) translate3d(0px, ${d/2+T}px, ${-d/2/A}px) rotateX(-89.99deg)`}const P=(w.isSafari||w.isWebView)&&w.needPerspectiveFix?-h/2:0;p.style.transform=`translate3d(0px,0,${P}px) rotateX(${e.isHorizontal()?0:v}deg) rotateY(${e.isHorizontal()?-v:0}deg)`,p.style.setProperty("--swiper-cube-translate-z",`${P}px`)},setTransition:n=>{const{el:p,slides:o}=e;if(o.forEach(l=>{l.style.transitionDuration=`${n}ms`,l.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(d=>{d.style.transitionDuration=`${n}ms`})}),e.params.cubeEffect.shadow&&!e.isHorizontal()){const l=p.querySelector(".swiper-cube-shadow");l&&(l.style.transitionDuration=`${n}ms`)}},recreateShadows:()=>{const n=e.isHorizontal();e.slides.forEach(p=>{const o=Math.max(Math.min(p.progress,1),-1);a(p,o,n)})},getEffectParams:()=>e.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}function re(s,e,t){const i=`swiper-slide-shadow${t?`-${t}`:""}${s?` swiper-slide-shadow-${s}`:""}`,a=ie(e);let r=a.querySelector(`.${i.split(" ").join(".")}`);return r||(r=q("div",i.split(" ")),a.append(r)),r}function ks(s){let{swiper:e,extendParams:t,on:i}=s;t({flipEffect:{slideShadows:!0,limitRotation:!0}});const a=(n,p)=>{let o=e.isHorizontal()?n.querySelector(".swiper-slide-shadow-left"):n.querySelector(".swiper-slide-shadow-top"),l=e.isHorizontal()?n.querySelector(".swiper-slide-shadow-right"):n.querySelector(".swiper-slide-shadow-bottom");o||(o=re("flip",n,e.isHorizontal()?"left":"top")),l||(l=re("flip",n,e.isHorizontal()?"right":"bottom")),o&&(o.style.opacity=Math.max(-p,0)),l&&(l.style.opacity=Math.max(p,0))};ae({effect:"flip",swiper:e,on:i,setTranslate:()=>{const{slides:n,rtlTranslate:p}=e,o=e.params.flipEffect;for(let l=0;l<n.length;l+=1){const d=n[l];let m=d.progress;e.params.flipEffect.limitRotation&&(m=Math.max(Math.min(d.progress,1),-1));const h=d.swiperSlideOffset;let g=-180*m,u=0,y=e.params.cssMode?-h-e.translate:-h,v=0;e.isHorizontal()?p&&(g=-g):(v=y,y=0,u=-g,g=0),e.browser&&e.browser.need3dFix&&(Math.abs(g)/90%2===1&&(g+=.001),Math.abs(u)/90%2===1&&(u+=.001)),d.style.zIndex=-Math.abs(Math.round(m))+n.length,o.slideShadows&&a(d,m);const E=`translate3d(${y}px, ${v}px, 0px) rotateX(${u}deg) rotateY(${g}deg)`,P=ue(o,d);P.style.transform=E}},setTransition:n=>{const p=e.slides.map(o=>ie(o));p.forEach(o=>{o.style.transitionDuration=`${n}ms`,o.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(l=>{l.style.transitionDuration=`${n}ms`})}),we({swiper:e,duration:n,transformElements:p})},recreateShadows:()=>{e.params.flipEffect,e.slides.forEach(n=>{let p=n.progress;e.params.flipEffect.limitRotation&&(p=Math.max(Math.min(n.progress,1),-1)),a(n,p)})},getEffectParams:()=>e.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function Gs(s){let{swiper:e,extendParams:t,on:i}=s;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),ae({effect:"coverflow",swiper:e,on:i,setTranslate:()=>{const{width:f,height:c,slides:n,slidesSizesGrid:p}=e,o=e.params.coverflowEffect,l=e.isHorizontal(),d=e.translate,m=l?-d+f/2:-d+c/2,h=l?o.rotate:-o.rotate,w=o.depth;for(let g=0,u=n.length;g<u;g+=1){const y=n[g],v=p[g],E=y.swiperSlideOffset,P=(m-E-v/2)/v,I=typeof o.modifier=="function"?o.modifier(P):P*o.modifier;let O=l?h*I:0,M=l?0:h*I,A=-w*Math.abs(I),T=o.stretch;typeof T=="string"&&T.indexOf("%")!==-1&&(T=parseFloat(o.stretch)/100*v);let z=l?0:T*I,x=l?T*I:0,b=1-(1-o.scale)*Math.abs(I);Math.abs(x)<.001&&(x=0),Math.abs(z)<.001&&(z=0),Math.abs(A)<.001&&(A=0),Math.abs(O)<.001&&(O=0),Math.abs(M)<.001&&(M=0),Math.abs(b)<.001&&(b=0),e.browser&&e.browser.need3dFix&&(Math.abs(O)/90%2===1&&(O+=.001),Math.abs(M)/90%2===1&&(M+=.001));const S=`translate3d(${x}px,${z}px,${A}px)  rotateX(${M}deg) rotateY(${O}deg) scale(${b})`,C=ue(o,y);if(C.style.transform=S,y.style.zIndex=-Math.abs(Math.round(I))+1,o.slideShadows){let k=l?y.querySelector(".swiper-slide-shadow-left"):y.querySelector(".swiper-slide-shadow-top"),D=l?y.querySelector(".swiper-slide-shadow-right"):y.querySelector(".swiper-slide-shadow-bottom");k||(k=re("coverflow",y,l?"left":"top")),D||(D=re("coverflow",y,l?"right":"bottom")),k&&(k.style.opacity=I>0?I:0),D&&(D.style.opacity=-I>0?-I:0)}}},setTransition:f=>{e.slides.map(n=>ie(n)).forEach(n=>{n.style.transitionDuration=`${f}ms`,n.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(p=>{p.style.transitionDuration=`${f}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}function Hs(s){let{swiper:e,extendParams:t,on:i}=s;t({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const a=c=>typeof c=="string"?c:`${c}px`;ae({effect:"creative",swiper:e,on:i,setTranslate:()=>{const{slides:c,wrapperEl:n,slidesSizesGrid:p}=e,o=e.params.creativeEffect,{progressMultiplier:l}=o,d=e.params.centeredSlides;if(d){const m=p[0]/2-e.params.slidesOffsetBefore||0;n.style.transform=`translateX(calc(50% - ${m}px))`}for(let m=0;m<c.length;m+=1){const h=c[m],w=h.progress,g=Math.min(Math.max(h.progress,-o.limitProgress),o.limitProgress);let u=g;d||(u=Math.min(Math.max(h.originalProgress,-o.limitProgress),o.limitProgress));const y=h.swiperSlideOffset,v=[e.params.cssMode?-y-e.translate:-y,0,0],E=[0,0,0];let P=!1;e.isHorizontal()||(v[1]=v[0],v[0]=0);let I={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};g<0?(I=o.next,P=!0):g>0&&(I=o.prev,P=!0),v.forEach((b,S)=>{v[S]=`calc(${b}px + (${a(I.translate[S])} * ${Math.abs(g*l)}))`}),E.forEach((b,S)=>{let C=I.rotate[S]*Math.abs(g*l);e.browser&&e.browser.need3dFix&&Math.abs(C)/90%2===1&&(C+=.001),E[S]=C}),h.style.zIndex=-Math.abs(Math.round(w))+c.length;const O=v.join(", "),M=`rotateX(${E[0]}deg) rotateY(${E[1]}deg) rotateZ(${E[2]}deg)`,A=u<0?`scale(${1+(1-I.scale)*u*l})`:`scale(${1-(1-I.scale)*u*l})`,T=u<0?1+(1-I.opacity)*u*l:1-(1-I.opacity)*u*l,z=`translate3d(${O}) ${M} ${A}`;if(P&&I.shadow||!P){let b=h.querySelector(".swiper-slide-shadow");if(!b&&I.shadow&&(b=re("creative",h)),b){const S=o.shadowPerProgress?g*(1/o.limitProgress):g;b.style.opacity=Math.min(Math.max(Math.abs(S),0),1)}}const x=ue(o,h);x.style.transform=z,x.style.opacity=T,I.origin&&(x.style.transformOrigin=I.origin)}},setTransition:c=>{const n=e.slides.map(p=>ie(p));n.forEach(p=>{p.style.transitionDuration=`${c}ms`,p.querySelectorAll(".swiper-slide-shadow").forEach(o=>{o.style.transitionDuration=`${c}ms`})}),we({swiper:e,duration:c,transformElements:n,allSlides:!0})},perspective:()=>e.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}function Bs(s){let{swiper:e,extendParams:t,on:i}=s;t({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),ae({effect:"cards",swiper:e,on:i,setTranslate:()=>{const{slides:f,activeIndex:c,rtlTranslate:n}=e,p=e.params.cardsEffect,{startTranslate:o,isTouched:l}=e.touchEventsData,d=n?-e.translate:e.translate;for(let m=0;m<f.length;m+=1){const h=f[m],w=h.progress,g=Math.min(Math.max(w,-4),4);let u=h.swiperSlideOffset;e.params.centeredSlides&&!e.params.cssMode&&(e.wrapperEl.style.transform=`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&&e.params.cssMode&&(u-=f[0].swiperSlideOffset);let y=e.params.cssMode?-u-e.translate:-u,v=0;const E=-100*Math.abs(g);let P=1,I=-p.perSlideRotate*g,O=p.perSlideOffset-Math.abs(g)*.75;const M=e.virtual&&e.params.virtual.enabled?e.virtual.from+m:m,A=(M===c||M===c-1)&&g>0&&g<1&&(l||e.params.cssMode)&&d<o,T=(M===c||M===c+1)&&g<0&&g>-1&&(l||e.params.cssMode)&&d>o;if(A||T){const S=(1-Math.abs((Math.abs(g)-.5)/.5))**.5;I+=-28*g*S,P+=-.5*S,O+=96*S,v=`${-25*S*Math.abs(g)}%`}if(g<0?y=`calc(${y}px ${n?"-":"+"} (${O*Math.abs(g)}%))`:g>0?y=`calc(${y}px ${n?"-":"+"} (-${O*Math.abs(g)}%))`:y=`${y}px`,!e.isHorizontal()){const S=v;v=y,y=S}const z=g<0?`${1+(1-P)*g}`:`${1-(1-P)*g}`,x=`
        translate3d(${y}, ${v}, ${E}px)
        rotateZ(${p.rotate?n?-I:I:0}deg)
        scale(${z})
      `;if(p.slideShadows){let S=h.querySelector(".swiper-slide-shadow");S||(S=re("cards",h)),S&&(S.style.opacity=Math.min(Math.max((Math.abs(g)-.5)/.5,0),1))}h.style.zIndex=-Math.abs(Math.round(w))+f.length;const b=ue(p,h);b.style.transform=x}},setTransition:f=>{const c=e.slides.map(n=>ie(n));c.forEach(n=>{n.style.transitionDuration=`${f}ms`,n.querySelectorAll(".swiper-slide-shadow").forEach(p=>{p.style.transitionDuration=`${f}ms`})}),we({swiper:e,duration:f,transformElements:c})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}const Xs=[fs,us,ps,ms,hs,gs,vs,ws,ys,bs,Ss,Es,xs,Ts,Ms,Cs,$s,Ds,Os,ks,Gs,Hs,Bs];W.use(Xs);try{window.Swiper=W}catch{}
