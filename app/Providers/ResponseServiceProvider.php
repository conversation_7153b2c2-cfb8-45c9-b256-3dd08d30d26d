<?php

namespace App\Providers;

use Illuminate\Support\Facades\Response;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Collection;

class ResponseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // Define the success response macro
        Response::macro('success', function ($data = null, $message = 'success', $meta = null, $code = 200) {
            $response = [
                'success' => true,
                'message' => $message,
                'data' => (is_array($data) || $data instanceof Collection) ? $data['data'] ?? $data : $data->data ?? $data,
                'meta' => (is_array($data) || $data instanceof Collection) ? $data['meta'] ?? null : $data->meta ?? null,
                'links' => (is_array($data) || $data instanceof Collection) ? $data['links'] ?? null : $data->links ?? null,
    
            ];

            return response()->json($response, $code);
        });

        Response::macro('fail', function ($error, $code = 400) {
            $response = [
                'message' => $error,
                'status_code' => $code,
                'data' => null,
            ];

            return response()->json($response, $code);
        });

        Response::macro('message', function ($message, $code = 200) {
            return response()->success(null, $message, null, $code);
        });
    }
}
