<?php

namespace App\Providers;

use App\Services\DashboardMenuService;
use Illuminate\Support\ServiceProvider;

class MenuServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // $verticalMenuJson = file_get_contents(base_path('resources/menu/verticalMenu.json'));
        // $verticalMenuData = json_decode($verticalMenuJson);
        // $horizontalMenuJson = file_get_contents(base_path('resources/menu/horizontalMenu.json'));
        // $horizontalMenuData = json_decode($horizontalMenuJson);

        // // Share all menuData to all the views
        // \View::share('menuData', [$verticalMenuData, $horizontalMenuData]);

        $dashboardMenuService = new DashboardMenuService;
        [$verticalMenuData, $horizontalMenuData] = $dashboardMenuService->readMenu();

        // Share all menuData to all the views
        \View::share('menuData', [$verticalMenuData, $horizontalMenuData]);
    }
}
