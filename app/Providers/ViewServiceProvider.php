<?php

namespace App\Providers;

use App\Helpers\Helpers;
use Illuminate\Support\Facades\Auth;
// use App\Enums\TopicEnum;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Modules\Notification\App\Enums\TopicEnum;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $_availableLocales = config("app._availableLocale");

        // $topics = TopicEnum::cases();
        view()->composer(['dashboard.*', 'components.*'], function ($view) use ($_availableLocales) {
            $view->with('_availableLocales', $_availableLocales);
            // $view->with('_topics', $topics);
        });


        Blade::if('hasusertype', function ($type) {
            $user = Auth::user();
            return Helpers::getResourceNameFromUserableType($user->userable_type) === $type;
        });

        Blade::if('hasanyusertype', function ($types) {
            $user = Auth::user();
            $userType = Helpers::getResourceNameFromUserableType($user->userable_type);
            return in_array($userType, $types);
        });


     

    }
}
