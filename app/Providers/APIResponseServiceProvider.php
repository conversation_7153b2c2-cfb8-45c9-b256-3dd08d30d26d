<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Contracts\Routing\ResponseFactory;

class APIResponseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(ResponseFactory $factory)
    {
        $factory->macro('success', function ($data = null, $meta = null,$code = 200) use ($factory) {
            if ($data == NULL) {
                $data = collect();
            }

            $format = [
                'code' => $code,
                'message' => __('api.codes.success.message'),
                'data' => $data,
                'meta' => $meta,
            ];

            return $factory->make($format);
        });

        $factory->macro('error', function ($code, $data = null) use ($factory) {
            if ($data == NULL) {
                $data = collect();
            }
            $format = [
                'code' => __('api.codes.' . $code . '.code'),
                'message' => __('api.codes.' . $code . '.message'),
                'data' => (object) $data,
            ];

            return $factory->make($format);
        });

        $factory->macro('customError', function ($code, $message) use ($factory) {
            $format = [
                'code' => $code,
                'message' => ucwords($message),
                'data' => null,
            ];

            return $factory->make($format);
        });

        $factory->macro('httpError', function ($code) use ($factory) {
            $format = [
                'code' => __('api.codes.' . $code . '.code'),
                'message' => __('api.codes.' . $code . '.message'),
                'data' => null,
            ];

            return $factory->make($format, __('api.codes.' . $code . '.code') * -1);
        });

        $factory->macro('datatables', function ($data) use ($factory) {
            if ($data == NULL) {
                $data = collect();
            }
            return $factory->make($data);
        });
    }
}