<?php

// app/Jobs/Ride/SendRideArrivedNotificationJob.php
namespace App\Jobs\Ride;

use App\Models\Ride;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendRideArrivedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Ride $ride
    ) {}

    public function handle(): void
    {
        $this->ride->load([
            'customer.user',
            'driver.user',
            'driver.vehicle',
            'points'
        ]);

        $notificationData = [
            'ride_id' => $this->ride->id,
            'driver_name' => $this->ride->driver->user->fullName,
            'vehicle_model' => $this->ride->driver->vehicle->model,
            'vehicle_color' => $this->ride->driver->vehicle->color,
            'vehicle_plate' => $this->ride->driver->vehicle->plate_number,
            'arrived_at' => now()->format('H:i'),
            'pickup_location' => $this->ride->points->where('type', 'pickup')->first()->address,
        ];

        // Get localized messages
        $englishTitle = __('notifications.ride_arrived.title');
        $englishBody = __('notifications.ride_arrived.body', $notificationData);
        $arabicTitle = __('notifications.ride_arrived.title', [], 'ar');
        $arabicBody = __('notifications.ride_arrived.body', $notificationData, 'ar');

        (new NotificationService())->sendNotificationForUser(
            user: $this->ride->customer->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::RIDE_ARRIVED,
            owner: $this->ride->driver->user,
            data: $notificationData
        );
    }
}
