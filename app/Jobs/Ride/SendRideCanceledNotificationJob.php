<?php

namespace App\Jobs\Ride;

use App\Models\Ride;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendRideCanceledNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Ride $ride
    ) {}

    public function handle(): void
    {
        $this->ride->load([
            'customer.user',
            'driver.user',
            'points'
        ]);

        $notificationData = [
            'ride_id' => $this->ride->id,
            'cancel_reason' => $this->ride->cancel_reason,
            'canceled_at' => $this->ride->canceled_at,
            'pickup_location' => $this->ride->points->where('type', 'pickup')->first()->address,
        ];

        // Get localized messages for customer
        $englishTitle = __('notifications.ride_canceled.title');
        $englishBody = __('notifications.ride_canceled.body', $notificationData);
        $arabicTitle = __('notifications.ride_canceled.title', [], 'ar');
        $arabicBody = __('notifications.ride_canceled.body', $notificationData, 'ar');

        // Send to customer
        (new NotificationService())->sendNotificationForUser(
            user: $this->ride->customer->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::RIDE_CANCELED,
            owner: $this->ride->driver->user,
            data: $notificationData
        );

        // If driver exists, notify them too
        if($this->ride->driver_id) {

            // Get localized messages for driver
            $driverEnglishTitle = __('notifications.ride_canceled.driver_title');
            $driverEnglishBody = __('notifications.ride_canceled.driver_body', $notificationData);
            $driverArabicTitle = __('notifications.ride_canceled.driver_title', [], 'ar');
            $driverArabicBody = __('notifications.ride_canceled.driver_body', $notificationData, 'ar');

            (new NotificationService())->sendNotificationForUser(
                user: $this->ride->driver->user,
                title: $driverEnglishTitle,
                body: $driverEnglishBody,
                titleAr: $driverArabicTitle,
                bodyAr: $driverArabicBody,
                type: NotificationTypeEnum::RIDE_CANCELED,
                owner: $this->ride->driver->user,
                data: $notificationData
            );
        }
    }
}
