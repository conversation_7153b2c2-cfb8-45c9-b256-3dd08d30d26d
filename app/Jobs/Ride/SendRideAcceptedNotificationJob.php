<?php

namespace App\Jobs\Ride;

use App\Models\Rating;
use App\Models\Ride;
use App\Services\GoogleMapsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendRideAcceptedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Ride $ride
    ) {}

    public function handle(): void
    {
        $this->ride->load([
            'customer.user',
            'driver.user',
            'driver.vehicle'
        ]);
        $cacheKey = "ride_{$this->ride->id}_driver_{$this->ride->driver_id}_eta";
        $etaData = Cache::get($cacheKey);
        $mapsService = new GoogleMapsService();

        // Fallback to fresh calculation if cache missing
        if (!$etaData) {
            $etaData = $this->calculateFreshETA($mapsService);
        }

        // Get driver's average rating
        $driverAverageRating = Rating::where('rateable_type', get_class($this->ride->driver))
            ->where('rateable_id', $this->ride->driver->id)
            ->avg('rating') ?? 0;

        // Format the rating to one decimal place
        $formattedRating = number_format($driverAverageRating, 1);

        // Create a more detailed driver object
        $driverData = [
            'id' => $this->ride->driver->id,
            'user_id' => $this->ride->driver->user_id,
            'is_available' => $this->ride->driver->is_available,
            'license_number' => $this->ride->driver->license_number,
            'average_rating' => $driverAverageRating,
            'current_latitude' => $this->ride->driver->current_latitude,
            'current_longitude' => $this->ride->driver->current_longitude,
            'user' => [
                'id' => $this->ride->driver->user->id,
                'first_name' => $this->ride->driver->user->first_name,
                'last_name' => $this->ride->driver->user->last_name,
                'full_name' => $this->ride->driver->user->fullName,
                'email' => $this->ride->driver->user->email,
                'phone' => $this->ride->driver->user->phone,
                'gender' => $this->ride->driver->user->gender->value,
                'profile_picture' => $this->ride->driver->user->getFirstMediaUrl('profile_picture'),
            ],
            'vehicle' => [
                'id' => $this->ride->driver->vehicle->id,
                'model' => $this->ride->driver->vehicle->model,
                'color' => $this->ride->driver->vehicle->color,
                'plate_number' => $this->ride->driver->vehicle->plate_number,
                'seats' => $this->ride->driver->vehicle->seats,
                'manufacturing_year' => $this->ride->driver->vehicle->manufacturing_year ?? null,
            ],
        ];

        $notificationData = [
            'ride_id' => $this->ride->id,
            'driver' => $driverData,
            'driver_name' => $this->ride->driver->user->fullName,
            'driver_phone' => $this->ride->driver->user->phone,
            'driver_rating' => $formattedRating,
            'driver_profile_picture' => $this->ride->driver->user->getFirstMediaUrl('profile_picture'),
            'vehicle_model' => $this->ride->driver->vehicle->model,
            'vehicle_color' => $this->ride->driver->vehicle->color,
            'vehicle_plate' => $this->ride->driver->vehicle->plate_number,
            'vehicle_car' => $this->ride->driver->getFirstMediaUrl('car_image'),
            'estimated_arrival_time' => $this->ride->accepted_at
                ->addMinutes($etaData['duration'])
                ->format('H:i'),

            'estimated_driver_distance' => $etaData['distance'],
            'estimated_driver_duration' => $etaData['duration'],

            'pickup_location' => $this->ride->points->where('type', 'pickup')->first()->address,
        ];

         // Get localized messages
        $englishTitle = __('notifications.ride_accepted.title');
        $englishBody = __('notifications.ride_accepted.body', ['driver_name' => $this->ride->driver->user->fullName]);
        $arabicTitle = __('notifications.ride_accepted.title', [], 'ar');
        $arabicBody = __('notifications.ride_accepted.body', ['driver_name' => $this->ride->driver->user->fullName], 'ar');

        (new NotificationService())->sendNotificationForUser(
            user: $this->ride->customer->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::RIDE_ACCEPTED,
            owner: $this->ride->driver->user,
            data: $notificationData
        );
    }


    private function calculateFreshETA(GoogleMapsService $mapsService): array
    {
        $pickupPoint = $this->ride->points->where('type', 'pickup')->first();
        $driverLocation = [
            'latitude' => $this->ride->driver->current_latitude,
            'longitude' => $this->ride->driver->current_longitude
        ];

        try {
            $route = $mapsService->calculateRoute([$driverLocation, [
                'latitude' => $pickupPoint->latitude,
                'longitude' => $pickupPoint->longitude
            ]]);

            return [
                'distance' => $route['distance'],
                'duration' => $route['duration']
            ];
        } catch (\Exception) {
            // Fallback to average speed calculation
            return [
                'distance' => 50,
                'duration' => 5
            ];
        }
    }
}
