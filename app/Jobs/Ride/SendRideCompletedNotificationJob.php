<?php

namespace App\Jobs\Ride;

use App\Models\Ride;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendRideCompletedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Ride $ride
    ) {}

    public function handle(): void
    {
        $this->ride->load([
            'customer.user',
            'driver.user',
            'driver.vehicle',
            'points'
        ]);

        $finalPrice = $this->ride->final_price;
       info('final price',[$finalPrice]);
        // Format the price for display
        $formattedPrice = number_format($finalPrice, 0, '.', ',');
        // Get dropoff location
        $dropoffPoint = $this->ride->points->where('type', 'dropoff')->first();
        $dropoffLocation = $dropoffPoint ? $dropoffPoint->address : 'Unknown location';

        $notificationData = [
            'ride_id' => $this->ride->id,
            'driver_name' => $this->ride->driver->user->fullName,
            'final_price' => $formattedPrice,
            'dropoff_location' => $dropoffLocation,
            'ride_duration' => $this->ride->actual_duration ?? 0,
            'completed_at' => $this->ride->completed_at,
        ];
        info('compalte notification data',$notificationData);
        // Get localized messages
        $englishTitle = __('notifications.ride_completed.title');
        $englishBody = __('notifications.ride_completed.body', $notificationData);
        $arabicTitle = __('notifications.ride_completed.title', [], 'ar');
        $arabicBody = __('notifications.ride_completed.body', $notificationData, 'ar');

        (new NotificationService())->sendNotificationForUser(
            user: $this->ride->customer->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::RIDE_COMPLETED,
            owner: $this->ride->driver->user,
            data: $notificationData
        );
    }
}
