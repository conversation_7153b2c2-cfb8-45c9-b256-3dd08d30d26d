<?php

namespace App\Jobs;

use App\Models\Ride;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendDriverStartedRideNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Ride $ride
    ) {}

    public function handle(): void
    {
        $this->ride->load([
            'customer.user',
            'driver.user',
            'driver.vehicle',
            'points'
        ]);

        $pickupPoint = $this->ride->points->where('type', 'pickup')->first();
        $dropoffPoint = $this->ride->points->where('type', 'dropoff')->first();

        $notificationData = [
            'ride_id' => $this->ride->id,
            'driver_name' => $this->ride->driver->user->fullName,
            'driver_phone' => $this->ride->driver->user->phone,
            'driver_profile_picture' => $this->ride->driver->user->getFirstMediaUrl('profile_picture'),
            'vehicle_model' => $this->ride->driver->vehicle->model ?? null,
            'vehicle_color' => $this->ride->driver->vehicle->color ?? null,
            'vehicle_plate' => $this->ride->driver->vehicle->plate_number ?? null,
            'started_at' => $this->ride->started_at,
            'pickup_location' => $pickupPoint?->address,
            'dropoff_location' => $dropoffPoint?->address,
        ];

        // Get localized messages
        $englishTitle = __('notifications.ride_started.title');
        $englishBody = __('notifications.ride_started.body', $notificationData);
        $arabicTitle = __('notifications.ride_started.title', [], 'ar');
        $arabicBody = __('notifications.ride_started.body', $notificationData, 'ar');

        (new NotificationService())->sendNotificationForUser(
            user: $this->ride->customer->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::NORMAL, 
            owner: $this->ride->driver->user,
            data: $notificationData
        );
    }
} 