<?php

namespace App\Jobs\Driver;

use App\Models\Driver;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendDriverApprovedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Driver $driver
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Load the driver with user relationship
        $this->driver->load(['user']);

        // Prepare notification data
        $notificationData = [
            'driver_id' => $this->driver->id,
            'driver_name' => $this->driver->user->fullName,
            'approved_at' => now()->format('Y-m-d H:i:s'),
        ];

        // Get localized messages
        $englishTitle = __('notifications.driver_approved.title');
        $englishBody = __('notifications.driver_approved.body', $notificationData);
        $arabicTitle = __('notifications.driver_approved.title', [], 'ar');
        $arabicBody = __('notifications.driver_approved.body', $notificationData, 'ar');

        // Send notification to the driver
        (new NotificationService())->sendNotificationForUser(
            user: $this->driver->user,
            title: $englishTitle,
            body: $englishBody,
            titleAr: $arabicTitle,
            bodyAr: $arabicBody,
            type: NotificationTypeEnum::DRIVER_APPROVED,
            owner: $this->driver->user,
            data: $notificationData
        );
    }
}
