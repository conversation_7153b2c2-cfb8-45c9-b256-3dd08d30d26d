<?php

namespace App\Jobs;

use App\Enums\DriverGenderEnum;
use App\Models\Ride;
use App\Models\Driver;
use App\Models\Rating;
use App\Services\GoogleMapsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Enums\NotificationTypeEnum;
use Modules\Notification\Services\NotificationService;

class SendRideRequestToDriversNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Ride $ride,
        public string $englishTitle,
        public string $englishBody,
        public string $arabicTitle,
        public string $arabicBody
    ) {}

    public function handle(GoogleMapsService $mapsService): void
    {
        $this->ride->load(['customer.user', 'points', 'pricing.carType']);
        $pickupPoint = $this->ride->points->where('type', 'pickup')->first();
        $dropoffPoint = $this->ride->points->where('type', 'dropoff')->first();

        if (!$pickupPoint || !$dropoffPoint || !$this->ride->pricing) {
            return;
        }

        // Get search radius from car type's km_range
        $searchRadius = $this->ride->pricing->carType->km_range ?? 10; // Default 10km

        // Determine car type logic
        $rideCarTypeName = $this->ride->pricing->carType->translate('en')->name;
        $carTypeIds = [];
        Log::info($rideCarTypeName);
        Log::info(strtolower($rideCarTypeName));

        if (strtolower($rideCarTypeName) == 'comfort') {
            $classic = \App\Models\CarType::whereTranslation('name', 'Classic', 'en')->first();
            $comfort = \App\Models\CarType::whereTranslation('name', 'Comfort', 'en')->first();
            if ($classic)
                $carTypeIds[] = $classic->id;
            if ($comfort)
                $carTypeIds[] = $comfort->id;
        } elseif (strtolower($rideCarTypeName) == 'classic') {
            $classic = \App\Models\CarType::whereTranslation('name', 'Classic', 'en')->first();
            if ($classic)
                $carTypeIds[] = $classic->id;
        } else {
            $carTypeIds[] = $this->ride->pricing->car_type_id;
        }

        $drivers = Driver::with(['user', 'carTypes', 'wallet'])
            ->whereAvailable()
            // ->whereHas('user', function ($query): void {
            //     if ($this->ride->driver_gender != DriverGenderEnum::BOTH->value) {
            //         $query->where('gender', $this->ride->driver_gender);
            //     }
            // })
            ->whereHas('carTypes', function ($query) use ($carTypeIds): void {
                $query->whereIn('car_type_id', $carTypeIds);
            })
            // Only include drivers with a wallet and sufficient balance
            ->whereHas('wallet', function ($query): void {
                $query->where('is_active', true);
                //   ->where('balance', '=>', 0);
                //   ->where('balance','>' , $this->ride->estimated_price);
            })
            ->withinRadius(
                $pickupPoint->latitude,
                $pickupPoint->longitude,
                $searchRadius
            )
            ->get();
        foreach ($drivers as $driver) {
            $driverLocation = [
                'latitude' => $driver->current_latitude,
                'longitude' => $driver->current_longitude
            ];

            // Calculate route from driver to pickup point
            try {
                $route = $mapsService->calculateRoute([
                    $driverLocation,
                    [
                        'latitude' => $pickupPoint->latitude,
                        'longitude' => $pickupPoint->longitude
                    ]
                ]);

                $driverDistance = $route['distance'];
                $driverDuration = $route['duration'];

                $cacheKey = "ride_{$this->ride->id}_driver_{$driver->id}_eta";
                Cache::put($cacheKey, [
                    'distance' => $driverDistance,
                    'duration' => $driverDuration
                ], now()->addMinutes(30));
            } catch (\Exception $e) {
                // Log error and skip this driver
                Log::error("Google Maps error for driver {$driver->id}: {$e->getMessage()}");
                continue;
            }
            // Calculate estimated price
            $estimatedPrice = $this->ride->estimated_price;

            $estimatedPrice = max($estimatedPrice, $this->ride->pricing->minimum_fare);

            $formattedPoints = $this->ride->points->map(function ($point) {
                return [
                    'address' => $point->address,
                    'type' => $point->type,
                    'latitude' => $point->latitude,
                    'longitude' => $point->longitude,
                    'sort_order' => $point->sort_order
                ];
            })->toArray();

            $rideData = [
                'id' => $this->ride->id,
                'pricing_id' => $this->ride->pricing_id,
                'customer' => [
                    'id' => $this->ride->customer->id,
                    'user_id' => $this->ride->customer->user_id,
                    'points' => $this->ride->customer->points,
                    'refer_code' => $this->ride->customer->refer_code,
                    'average_rating' => Rating::where('rateable_type', get_class($this->ride->customer))
                        ->where('rateable_id', $this->ride->customer->id)
                        ->avg('rating') ?? 0,
                    'user' => [
                        'id' => $this->ride->customer->user->id,
                        'first_name' => $this->ride->customer->user->first_name,
                        'last_name' => $this->ride->customer->user->last_name,
                        'email' => $this->ride->customer->user->email,
                        'phone' => $this->ride->customer->user->phone,
                        'gender' => $this->ride->customer->user->gender->value, // Get enum value
                        'profile_picture' => $this->ride->customer->user->getFirstMediaUrl('profile_picture'),
                    ]
                ],
                'estimated_distance' => $this->ride->estimated_distance,
                'estimated_duration' => $this->ride->estimated_duration,
                'actual_distance' => $this->ride->actual_distance,
                'actual_duration' => $this->ride->actual_duration,
                'fare' => $this->ride->fare,
                'final_price' => $this->ride->final_price,
                'status' => $this->ride->status->value, // Get enum value
                'type' => $this->ride->type->value, // Get enum value
                'scheduled_at' => $this->ride->scheduled_at,
                'requested_at' => $this->ride->requested_at,
                'completed_at' => $this->ride->completed_at,
                'note' => $this->ride->note ?? null,
                'created_at' => $this->ride->created_at,
            ];

            // Get customer's average rating
            $customerAverageRating = Rating::where('rateable_type', get_class($this->ride->customer))
                ->where('rateable_id', $this->ride->customer->id)
                ->avg('rating') ?? 0;

            // Format the rating to one decimal place
            $formattedRating = number_format($customerAverageRating, 1);

            $notificationData = [
                'ride' => $rideData,
                'customer_first_name' => $this->ride->customer->user->first_name,
                'customer_phone' => $this->ride->customer->user->phone,
                'customer_rating' => $formattedRating,
                'points' => $formattedPoints,
                'estimated_driver_distance' => round($driverDistance, 2),
                'estimated_driver_duration' => round($driverDuration, 2),
                'estimated_price' => number_format($estimatedPrice, 2),
                'pickup_location' => $pickupPoint->address,
                'dropoff_location' => $dropoffPoint->address,
                'car_type' => $this->ride->pricing->carType->name
            ];
            (new NotificationService())->sendNotificationForUser(
                user: $driver->user,
                title: $this->englishTitle,
                body: $this->englishBody,
                titleAr: $this->arabicTitle,
                bodyAr: $this->arabicBody,
                type: NotificationTypeEnum::RIDE_REQUEST,
                owner: $this->ride->customer->user,
                data: $notificationData

            );
        }
    }
}
