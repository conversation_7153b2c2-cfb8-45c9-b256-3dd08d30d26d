<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\DB;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {   
        $this->reportable(function (Throwable $e): void {
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
                logger()->error('Active transaction rolled back due to exception', [
                    'exception' => $e->getMessage(),
                    'transaction_level' => DB::transactionLevel(),
                    'exception_class' => get_class($e),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        });
    }
}
