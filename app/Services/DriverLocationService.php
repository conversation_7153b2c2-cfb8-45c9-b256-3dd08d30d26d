<?php

namespace App\Services;

use App\Events\DriverLocationUpdated;
use App\Models\Driver;
use App\Models\DriverLocation;
use App\Models\Ride;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;

class DriverLocationService extends BaseService
{
    /**
     * Cache TTL in seconds (5 minutes)
     */
    const CACHE_TTL = 300;

    /**
     * Constructor
     */
    public function __construct(DriverLocation $model)
    {
        parent::__construct($model);
    }

    /**
     * Update driver location
     *
     * @param mixed $driver The driver model instance
     * @param array $data
     * @return DriverLocation
     */
    public function updateLocation($driver, array $data): DriverLocation
    {
        // Check if the driver has an active ride
        $activeRide = $this->getActiveRide($driver->id);

        return DB::transaction(function () use ($driver, $data, $activeRide) {
            $now = now();
            $locationData = [
                'driver_id' => $driver->id,
                'ride_id' => $activeRide ? $activeRide->id : null,
                'latitude' => $data['latitude'],
                'longitude' => $data['longitude'],
                'heading' => $data['heading'] ?? null,
                'is_online' => $data['is_online'] ?? true,
                'address' => $data['address'] ?? null,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $locationId = DB::table('driver_locations')->insertGetId($locationData);

            $driverLocation = DriverLocation::find($locationId);

            // Update the driver's current location using raw SQL for better performance
            DB::table('drivers')
                ->where('id', $driver->id)
                ->update([
                    'current_latitude' => $data['latitude'],
                    'current_longitude' => $data['longitude'],
                    'is_available' => $data['is_online'] ?? $driver->is_available,
                    'updated_at' => now(),
                ]);

            // Dispatch the event with logging
            try {
                Log::info('Dispatching DriverLocationUpdated event', [
                    'driver_id' => $driver->id,
                    'location_id' => $driverLocation->id,
                    'latitude' => $driverLocation->latitude,
                    'longitude' => $driverLocation->longitude
                ]);

                event(new DriverLocationUpdated($driverLocation));

                Log::info('DriverLocationUpdated event dispatched successfully');
            } catch (\Exception $e) {
                Log::error('Error dispatching DriverLocationUpdated event', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            $this->updateDriverLocationCache($driver->id, $driverLocation);

            return $driverLocation;
        });
    }

    /**
     * Get location history for a driver or ride
     *
     * @param array $filters
     * @return LengthAwarePaginator|Collection
     */
    public function getLocationHistory(array $filters): LengthAwarePaginator|Collection
    {
        $query = QueryBuilder::for(DriverLocation::query())
            ->allowedFilters(['driver_id', 'ride_id'])
            ->defaultSorts('-created_at');

        // Filter by driver_id if provided
        if (isset($filters['driver_id'])) {
            $query->where('driver_id', $filters['driver_id']);
        }

        // Filter by ride_id if provided
        if (isset($filters['ride_id'])) {
            $query->where('ride_id', $filters['ride_id']);
        }

        // Filter by time range if provided
        if (isset($filters['start_time'])) {
            $query->where('created_at', '>=', $filters['start_time']);
        }

        if (isset($filters['end_time'])) {
            $query->where('created_at', '<=', $filters['end_time']);
        }

        // Limit the results
        $limit = $filters['limit'] ?? config('pagination.page-size');

        return $query->paginate($limit)->withQueryString();
    }

    /**
     * Get the latest location for a driver
     *
     * @param int $driverId
     * @return DriverLocation|null
     */
    public function getLatestLocation(int $driverId): ?DriverLocation
    {
        $cacheKey = "driver:{$driverId}:latest_location";

        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($driverId) {
            $locationData = DB::table('driver_locations')
                ->where('driver_id', $driverId)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$locationData) {
                return null;
            }

            return DriverLocation::find($locationData->id);
        });
    }

    /**
     * Get all online drivers with their latest location
     *
     * @return Collection
     */
    public function getAllOnlineDrivers(): Collection
    {
        $cacheKey = "drivers:online";

        return Cache::remember($cacheKey, self::CACHE_TTL, function() {
            $drivers = Driver::where('is_available', true)
                ->whereNotNull('current_latitude')
                ->whereNotNull('current_longitude')
                ->with([
                    'latestLocation' => function($query) {
                        $query->latest();
                    },
                    'user:id,first_name,email,phone'
                ])
                ->get();

            return $drivers;
        });
    }

    /**
     * Get active ride for a driver
     *
     * @param int $driverId
     * @return Ride|null
     */
    private function getActiveRide(int $driverId): ?Ride
    {
        return Ride::where('driver_id', $driverId)
            ->whereIn('status', ['accepted', 'arrived', 'in_progress'])
            ->latest()
            ->first();
    }

    /**
     * Update driver location cache
     *
     * @param int $driverId
     * @param DriverLocation $location
     * @return void
     */
    private function updateDriverLocationCache(int $driverId, DriverLocation $location): void
    {
        $cacheKey = "driver:{$driverId}:latest_location";

        Cache::remember($cacheKey, self::CACHE_TTL, function() use ($location) {
            return $location;
        });

        if ($location->is_online) {
            Cache::forget("drivers:online");
        }
    }
}
