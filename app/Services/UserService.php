<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class UserService extends BaseService
{
    function __construct(User $user)
    {
        parent::__construct($user);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(User::query())
            ->allowedFilters(["first_name", "last_name", "email", "phone", "password", "remember_token"])
            ->defaultSorts('created_at')

            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): User
    {

        $user = User::updateOrCreate(['id' => $data['id'] ?? null], $data);
        if (isset($data['profile']) && !is_null($data['profile'])) {
            $user->clearMediaCollection('profile');
            $user->addMedia($data['profile'])->preservingOriginal()->toMediaCollection('profile');
        }
        

        return $user;
    }
}
