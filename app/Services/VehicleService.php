<?php

namespace App\Services;

use App\Models\Vehicle;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class VehicleService extends BaseService
{
    function __construct(Vehicle $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Vehicle::query())
                        ->allowedFilters(["name","plate_number","color"])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Vehicle
    {
        $model = Vehicle::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
