<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingService
{
    public function update($data)
    {
        foreach ($data['settings'] as $index => $setting) {
            $dataSetting = Setting::query()->find($index);
            $dataSetting->update([
                'value' => $setting['value']
            ]);
            if ($dataSetting->has_image && isset($setting['image'])) {
                
                upload_media_file($dataSetting, $setting['image'], 'image', true);
            }
            Cache::forever($setting['key'], $setting['value']);
        }
    }
    public function index($keys = [])
    {
        return Setting::with('media')
            ->when(count($keys) > 0, function ($query) use ($keys) {
                $query->whereIn('key', $keys);
            })
            ->get();
    }


}
