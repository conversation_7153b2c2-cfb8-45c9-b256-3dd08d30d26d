<?php

namespace App\Services;

use App\Models\Post;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class PostService extends BaseService
{
    function __construct(Post $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Post::query())
                        ->allowedFilters(["title","category.name"])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Post
    {
        $model = Post::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
