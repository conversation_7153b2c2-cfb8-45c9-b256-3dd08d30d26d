<?php

namespace App\Services;

use App\Models\Category;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class CategoryService extends BaseService
{
    function __construct(Category $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Category::query())
                        ->allowedFilters(["name"])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Category
    {
        $model = Category::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
