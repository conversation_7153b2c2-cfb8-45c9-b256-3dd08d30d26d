<?php

namespace App\Services;


use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\QueryBuilder\QueryBuilder;

abstract class BaseService
{
   /**
     * @var Model The model instance.
     */
    protected Model $model;

    /**
     *
     * @param  Model  $model  The model instance.
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for($this->model::query())
                        ->defaultSorts('created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    public function storeOrUpdate(array $data)
    {
        $model = $this->model::updateOrCreate(['id' => $data['id'] ?? null], $data);
        return $model;
    }

    /**
     * Create a new model instance with the given data.
     *
     * @param  array  $data  The data to create the model with.
     * @return Model The created model instance.
     */
    public function store($data): Model
    {
        return $this->model::create($data);
    }


    /**
     * Update the given model instance with the provided attributes.
     *
     * @param  Model  $model  The model instance to update.
     * @param  array  $attributes  The attributes to update the model with.
     * @return Model The updated model instance.
     */
    public function update(Model $model, $attributes): Model
    {
        $model->update($attributes);
        $model->refresh();

            return $model;
        }

    /**
     * Find a model by its ID or return the model if it's already a model instance.
     *
     * @param int|Model $idOrModel The ID of the model to find or the model instance.
     * @return Model The found model instance.
     */
    public function show($idOrModel): Model
    {
        // If $idOrModel is already a model instance, return it
        if ($idOrModel instanceof Model) {
            return $idOrModel;
        }

        // Otherwise, find the model by ID
        return $this->model::findOrFail($idOrModel);
    }

    public function destroy($id, bool $isForce = false): bool
    {
        $model = $this->model::findOrFail($id);
        return $isForce && in_array(SoftDeletes::class, class_uses($model)) ? $model->forceDelete() : $model->delete();
    }

}