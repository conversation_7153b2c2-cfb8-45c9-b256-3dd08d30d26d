<?php

namespace App\Services;

use App\Enums\SettingEnum;
use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Models\Coupon;
use App\Models\Driver;
use App\Models\Ride;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\Wallet;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\QueryBuilder;

class TransactionService extends BaseService
{
    function __construct(Transaction $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator|Collection
    {
        return QueryBuilder::for(Transaction::query())
            ->allowedFilters(["payment_method", "status", "type", "reference", "purpose"])
            ->defaultSorts('created_at')

            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Transaction
    {
        $model = Transaction::updateOrCreate(['id' => $data['id'] ?? null], $data);

        return $model;
    }

    public function store($data, bool $process = false): Transaction
    {
        return DB::transaction(function () use ($data, $process) {
            // Common transaction setup
            if (Auth::check()) {
                $data['initiator_type'] ??= get_class(Auth::user()->userable);
                $data['initiator_id'] ??= Auth::user()->userable->id;
            }
            $data['status'] ??= TransactionStatusEnum::SUCCESS;
            $data['reference'] ??= Str::uuid();
            // Handle coupon if exists
            if (isset($data['coupon_id'])) {
                $coupon = Coupon::findOrFail($data['coupon_id']);
                $data['discount_type'] = $coupon->type->value;
                $data['discount_amount'] = $coupon->value;
            }

            // Create transaction
            $transaction = parent::store($data);

            // Handle transaction type specific logic
            if ($process) {
                $this->processTransaction($transaction);
            }

            return $transaction;
        });
    }

    protected function processTransaction(Transaction $transaction): void
    {
        $senderWallet = $transaction->senderWallet;
        $receiverWallet = $transaction->receiverWallet;

        switch ($transaction->type) {
            case TransactionTypeEnum::CREDIT:
                $receiverWallet->increment('balance', $transaction->amount);
                break;

            case TransactionTypeEnum::DEBIT:
                $this->handleDebit($senderWallet, $receiverWallet, $transaction->amount);
                break;

            case TransactionTypeEnum::BONUS:
                $receiverWallet->increment('balance', $transaction->amount);
                break;

            case TransactionTypeEnum::FEE:
            case TransactionTypeEnum::PENALTY:
                $this->handleFeeOrPenalty($senderWallet, $transaction->amount);
                break;
        }
    }
    protected function handleDebit(Wallet $sender, Wallet $receiver, int $amount): void
    {
        if ($sender->balance < $amount) {
            throw new Exception(__('Insufficient funds in sender wallet'));
        }

        $sender->decrement('balance', $amount);
        $receiver->increment('balance', $amount);

        // Deduct platform fee (example: 10%)
        // TODO Get this from settings
        $companyCommission = (float) (Setting::where('key', SettingEnum::COMPANY_COMMISSION->value)->first()->value ?? 0.20);
        $fee = $amount * $companyCommission;
        $companyWallet = Wallet::whereHasMorph(
            'walletable',
            [\App\Models\Company::class]
        )->firstOrFail();

        $sender->decrement('balance', $fee);
        $companyWallet->increment('balance', $fee);
    }

    protected function handleFeeOrPenalty(Wallet $sender, int $amount): void
    {
        if ($sender->balance < $amount) {
            throw new Exception(__('Insufficient funds for fee/penalty'));
        }

        $sender->decrement('balance', $amount);
    }

    public function processRidePayment(Ride $ride, Wallet $driverWallet, Wallet $companyWallet, float $amount, string $paymentMethod): void
    {
        DB::transaction(function () use ($ride, $driverWallet, $companyWallet, $amount, $paymentMethod): void {
            // Calculate payment split (80% driver, 20% company)
            $split = $this->calculatePaymentSplit($amount);


            $this->handleCompanyCommission(ride: $ride, driverWallet: $driverWallet, companyWallet: $companyWallet, commissionAmount: $split['company'], paymentMethod: $paymentMethod);
            // $this->recordDriverTransaction(ride: $ride, driverWallet: $driverWallet, amount: $split['driver'], paymentMethod: $paymentMethod);

            $this->handleCouponDiscount(ride: $ride, driverWallet: $driverWallet, companyWallet: $companyWallet, paymentMethod: $paymentMethod);
        });
    }

    protected function handleCompanyCommission(Ride $ride, Wallet $driverWallet, Wallet $companyWallet, float $commissionAmount, string $paymentMethod): void
    {
        $this->store([
            'sender_wallet_id' => $driverWallet->id,
            'receiver_wallet_id' => $companyWallet->id,
            'ride_id' => $ride->id,
            'amount' => $commissionAmount,
            'type' => TransactionTypeEnum::FEE,
            'status' => TransactionStatusEnum::SUCCESS,
            'payment_method' => $paymentMethod,
            'reference' => Str::uuid(),
            'en' => ['description' => 'Commission owed to company'],
            'ar' => ['description' => 'عمولة مستحقة للشركة'],
            'initiator_type' => Driver::class,
            'initiator_id' => $ride->driver->id,
        ]);
        $companyWallet->increment('balance', $commissionAmount);
        $driverWallet->decrement('balance', $commissionAmount);
    }

    protected function recordDriverTransaction(Ride $ride, Wallet $driverWallet, float $amount, string $paymentMethod): void
    {
        $this->store([
            'sender_wallet_id' => null,
            'receiver_wallet_id' => $driverWallet->id,
            'ride_id' => $ride->id,
            'amount' => $amount,
            'type' => TransactionTypeEnum::DEBIT,
            'status' => TransactionStatusEnum::SUCCESS,
            'payment_method' => $paymentMethod,
            'reference' => Str::uuid(),
            'en' => ['description' => 'Cash collected from ride'],
            'ar' => ['description' => 'تحصيل نقدي من الرحلة'],
            'initiator_type' => Driver::class,
            'initiator_id' => $ride->driver->id,
        ]);
        // $driverWallet->decrement('balance', $amount);
        
    }

    protected function handleCouponDiscount(Ride $ride, Wallet $driverWallet, Wallet $companyWallet, string $paymentMethod): void
    {
        if (!$ride->coupon)
            return;

        $discountAmount = $ride->coupon->calculateDiscount($ride->final_price);

        $this->store([
            'sender_wallet_id' => $companyWallet->id,
            'receiver_wallet_id' => $driverWallet->id,
            'ride_id' => $ride->id,
            'coupon_id' => $ride->coupon->id,
            'coupon_type' => $ride->coupon->type,
            'coupon_amount' => $discountAmount,
            'amount' => $discountAmount,
            'type' => TransactionTypeEnum::COUPON,
            'status' => TransactionStatusEnum::SUCCESS,
            'payment_method' => $paymentMethod,
            'reference' => Str::uuid(),
            'en' => ['description' => 'Coupon discount'],
            'ar' => ['description' => 'خصم الكوبون'],
            'initiator_type' => Driver::class,
            'initiator_id' => $ride->driver->id,
        ]);

        $driverWallet->increment('balance', $discountAmount);
        // $companyWallet->decrement('balance', $discountAmount);
        // Company Wallet will be decrement


    }



    public function calculatePaymentSplit(float $amount): array
    {
        //TODO get commission from settings values
        $companyCommission = (float) (Setting::where('key', SettingEnum::COMPANY_COMMISSION->value)->first()->value ?? 20); // 20% commission
        // $driverShare = (float)(Setting::where('key',SettingEnum::DRIVER_COMMISSION->value)->first()->value ?? 0.80); // 80% for driver
        $driverShare = 100 - $companyCommission;

        $toDriver = ($amount * $driverShare) / 100;
        $toCompany = ($amount * $companyCommission) / 100;
        return [
            // 'driver' => $amount * $driverShare,
            // 'company' => $amount * $companyCommission,

            'driver' => $toDriver,
            'company' => $toCompany,
            'total' => $amount
        ];
    }
}
