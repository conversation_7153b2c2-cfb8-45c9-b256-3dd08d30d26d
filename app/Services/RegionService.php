<?php

namespace App\Services;

use App\Models\Region;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class RegionService extends BaseService
{
    function __construct(Region $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Region::query())
                     //   ->allowedFilters([])
            ->defaultSorts('created_at')
            ->allowedFilters(['name', 'status']) //add
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Region
    {
        $model = Region::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
