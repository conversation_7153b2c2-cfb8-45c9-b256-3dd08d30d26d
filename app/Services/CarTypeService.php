<?php

namespace App\Services;

use App\Models\CarType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class CarTypeService extends BaseService{

    function __construct(CarType $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        $query = CarType::query()->where('is_available', true);
            return QueryBuilder::for($query)
            ->allowedFilters([])
            ->defaultSorts('created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): CarType
    {
        $model = CarType::updateOrCreate(['id' => $data['id'] ?? null], $data);



        return $model;
    }

}
