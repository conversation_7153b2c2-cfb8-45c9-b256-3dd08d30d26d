<?php

namespace App\Services;

use App\Enums\OpporunityTypeEnum;
use App\Enums\QuestionTargetEnum;
use App\Enums\StoryTypeEnum;
use App\Enums\TagTypeEnum;
use Illuminate\Support\Facades\Schema;

class DashboardMenuService
{
    public function readMenu()
    {
        $horizontalMenuJson = file_get_contents(base_path('resources/menu/horizontalMenu.json'));
        $horizontalMenuData = json_decode($horizontalMenuJson);

        $verticalMenuJson = file_get_contents(base_path('resources/menu/verticalMenu.json'));
        $verticalMenuData = json_decode($verticalMenuJson);

        // ADD TO MENU IN RUN TIME DYNAMICALLY

        // $tables = $this->allTables;
        // $menu = [];
        // foreach (collect($tables)->sortBy('name') as $table) {
        //     $tableName = $table['name'];
        //     if (!in_array($tableName, ['migrations', 'media']) && !str($tableName)->endsWith('_translations')) {
        //         $tableNameKebab = str($tableName)->studly()->kebab();
        // $menu[] = (object) [
        //     "name" => str($tableName)->studly()->ucfirst()->value,
        //     "params" => [],
        //     "available_permissions" => ["CREATE", "READ", "UPDATE", "DELETE"],
        //     "icon" => "menu-icon tf-icons ti ti-file",
        //     "slug" => "dashboard.$tableNameKebab.index",
        //     "url" => "dashboard.$tableNameKebab.index",
        // ];
        //     }
        // }
        // array_splice($verticalMenuData->menu, 0, 0, $menu);
        // array_splice($horizontalMenuData->menu, 0, 0, $menu);

        return [$verticalMenuData, $horizontalMenuData];
    }
    public function generateMenu(?string $specificTable = null)
    {
        // Get the current menu data
        $horizontalMenuData = $this->readMenu()[1];
        $verticalMenuData = $this->readMenu()[0];
        // Get the list of tables from the database schema
        $tables = $specificTable ? collect([['name' => $specificTable]]) : collect(Schema::getTables());
        $newMenuItems = [];
        $exceptedTables = config('generator.EXCEPTED_TABLES');
        
        // Loop through the tables and create new menu items
        foreach ($tables->sortBy('name') as $table) {
            $tableName = $table['name'];
    
            // Skip certain tables (e.g., excluded tables or those ending with '_translations')
            if (!in_array($tableName, $exceptedTables) && !str($tableName)->endsWith('_translations')) {
                $tableNameKebab = str($tableName)->studly()->kebab();

                // Create a new menu item
                $newMenuItem = (object) [
                    "name" => str($tableName)->studly()->ucfirst()->value(),
                    "params" => [],
                    "available_permissions" => ["CREATE", "READ", "UPDATE", "DELETE"],
                    "icon" => "description",
                    "slug" => "dashboard.$tableNameKebab.index",
                    "url" => "dashboard.$tableNameKebab.index",
                ];

                // Check if the item already exists in the menu (by slug)
                $itemExists = collect($verticalMenuData->menu)->contains('slug', $newMenuItem->slug);

                // Add the new item only if it does not already exist
                if (!$itemExists) {
                    $newMenuItems[] = $newMenuItem;
                } else {
                    info("Menu item with slug {$newMenuItem->slug} already exists. Skipping...");
                }
            }
        }

        // Merge new menu items without overwriting existing ones
        $verticalMenuData->menu = array_merge($verticalMenuData->menu, $newMenuItems);
        $horizontalMenuData->menu = array_merge($horizontalMenuData->menu, $newMenuItems);

        // Write the updated menu data back to the JSON files
        file_put_contents(base_path('resources/menu/verticalMenu.json'), json_encode($verticalMenuData, JSON_PRETTY_PRINT));
        file_put_contents(base_path('resources/menu/horizontalMenu.json'), json_encode($horizontalMenuData, JSON_PRETTY_PRINT));

        return [$verticalMenuData, $horizontalMenuData];
    }
    public function initializeTheMenu()
    {
        return  [
            (object) [
                "name" => "Statistics",
                "params" => [],
                "available_permissions" => ["READ"],
                "icon" => "menu-icon tf-icons ti ti-file",
                "slug" => "dashboard.statistics.index",
                "url" => "dashboard.statistics.index",
            ]
        ];
    }
}
