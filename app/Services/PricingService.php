<?php

namespace App\Services;

use App\Models\CarType;
use App\Models\Pricing;
use App\Models\Shift;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class PricingService extends BaseService
{
    function __construct(
        Pricing $model,
         private CarType $carType, 
         private Shift $shift
         )
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Pricing::query()->with('shift'))
                        ->allowedFilters(['car_type_id'])
            ->defaultSorts('created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Pricing
    {
        $model = Pricing::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }


    public function getCarTypesForDropdown(): array
    {
        return $this->formatForDropdown(
            $this->carType->with('translations')->get(),
        );
    }

    public function getShiftsForDropdown(): array
    {
        return $this->formatForDropdown(
            $this->shift->with('translations')->get(),
        );
    }

    protected function formatForDropdown($collection): array
    {

        return [
            'values' => $collection->pluck('id'),
            'names' => $collection->map(fn($item) => 
               $item->translations->pluck('name')->join(', '))
        ];
    }
}
