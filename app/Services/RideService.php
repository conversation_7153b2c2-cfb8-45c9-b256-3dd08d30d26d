<?php

namespace App\Services;

use App\Enums\RideStatusEnum;
use App\Enums\RideTypeEnum;
use App\Enums\TransactionPaymentMethodEnum;
use App\Jobs\SendRideRequestToDriversNotificationJob;
use App\Models\Company;
use App\Models\Coupon;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\Pricing;
use App\Models\Ride;
use App\Models\RidePoint;
use App\Models\Shift;
use App\Models\User;
use App\Models\Wallet;
use App\Services\GoogleMapsService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\QueryBuilderRequest;

class RideService extends BaseService
{
    public function __construct(
        private GoogleMapsService $mapsService,
        protected Ride $ride
    ) {
        parent::__construct($this->ride);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        $user = auth()->user();
        $query = $this->ride->query();

        /** @var User $user */
        if ($user->isCustomer()) {
            $query = $user->userable->rides()->getQuery();
        } elseif ($user->isDriver()) {
            $query = $user->userable->rides()->getQuery();
        }
        QueryBuilderRequest::setArrayValueDelimiter('|');

        $query->with(['points', 'driver.vehicle', 'driver.user', 'customer', 'points', 'coupon', 'customerRating', 'driverRating']);
        $filters = [
            AllowedFilter::exact('type'),
            AllowedFilter::exact('status'),
        ];
        return QueryBuilder::for($query)
            ->allowedFilters($filters)
            ->defaultSorts('-created_at') // Consider using '-' for descending
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }
    /**
     * Creates a new ride with the given customer and data.
     *
     * @param int $customerId
     * @param array $data
     * @return Ride
     */
    public function createRide(int $customerId, array $data)
    {
        $pricing = Pricing::find($data['pricing_id']);

        if (!$pricing->minimum_fare || $pricing->minimum_fare <= 0) {
            throw new Exception(__('Invalid pricing. The minimum fare must be greater than zero.'));
        }

        if (!$pricing->carType || !$pricing->carType->is_available) {
            throw new Exception(__('This car type is currently unavailable.'));
        }

        if (isset($data['coupon_code'])) {
            $coupon = Coupon::where('code', $data['coupon_code'])->first();
            $user = auth()->user();

            $userCoupon = $user->coupons()
                ->where('coupons.id', $coupon->id)
                ->first();

            if ($coupon->end_at < now()) {
                throw new Exception(__('Coupon has expired'), 422);
            }

            if ($userCoupon) {
                if ($userCoupon->pivot->times_used >= $coupon->usage_limit_per_user) {
                    throw new Exception(__('Coupon usage limit exceeded'), 422);
                }
                $user->coupons()->updateExistingPivot($coupon->id, [
                    'times_used' => DB::raw('times_used + 1')
                ]);
            } else {
                $user->coupons()->attach($coupon->id, ['times_used' => 1]);
            }
        }


        $points = $this->normalizePoints($data['points']);
        $route = $this->getRouteData($points);

        // Calculate estimated price
        $calculated =
            + ($route['distance'] * $pricing->km_price)
                + ($route['duration'] * $pricing->miu_price);
        $calculated = max($pricing->minimum_fare, $calculated);
        $estimatedPrice = ceil($calculated / 100) * 100;

        if ($estimatedPrice < $pricing->minimum_fare) {
            throw new Exception(__("Ride Cost must be greater than: ") . $pricing->minimum_fare);
        }
        $ride = Ride::create([
            'customer_id' => $customerId,
            'estimated_distance' => $route['distance'],
            'estimated_duration' => $route['duration'],
            'pricing_id' => $data['pricing_id'],
            'status' => RideStatusEnum::PENDING,
            'type' => $data['type'],
            'driver_gender' => $data['driver_gender'],
            'scheduled_at' => $data['scheduled_at'] ?? null,
            'coupon_id' => $coupon_id ?? null,
            'requested_at' => now(),
            'note' => $data['note'] ?? null,
            'estimated_price' => $estimatedPrice,
        ]);
        $ride->refresh();
        $this->createRoutePoints($ride, $data['points']);

        $ride = $ride->load(['customer.user', 'points', 'pricing.carType']);
        SendRideRequestToDriversNotificationJob::dispatch(
            $ride,
            'New Ride Request',
            'A new ride is available near you',
            'طلب رحلة جديد',
            'هناك رحلة جديدة بالقرب منك'
        );
        return $ride;
    }

    private function createRoutePoints(Ride $ride, array $points): void
    {
        $totalPoints = count($points);

        $pointsData = collect($points)
            ->map(function (array $point, int $index) use ($ride, $totalPoints) {
                return [
                    'ride_id' => $ride->id,
                    'latitude' => $point['latitude'],
                    'longitude' => $point['longitude'],
                    'address' => $point['address'],
                    'type' => $this->determinePointType($index, $totalPoints),
                    'sort_order' => $index + 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })
            ->toArray();
        RidePoint::insert($pointsData);
    }

    private function determinePointType(int $index, int $total): string
    {
        return match (true) {
            $index === 0 => 'pickup',        // First point is pickup
            $index === $total - 1 => 'dropoff',  // Last point is dropoff
            default => 'via',
        };
    }

    public function calculatePricing(array $points): array
    {

        $points = $this->normalizePoints($points);
        $route = $this->getRouteData($points);
        $currentTime = now('Asia/Damascus')->format('H:i:s');
        $shift = Shift::where(function ($query) use ($currentTime): void {
            $query->whereRaw(
                "CASE
            WHEN start_time < end_time THEN
                ? BETWEEN start_time AND end_time
            ELSE
                ? >= start_time OR ? <= end_time
            END",
                [$currentTime, $currentTime, $currentTime]
            );
        })->with('translations')->first();

        // Format points with types
        $formattedPoints = $this->formatPoints($points);

        if (!$shift) {
            return [
                'pricing_details' => [],
                'points' => $formattedPoints,
                'error' => __('No active shift found for the current time')
            ];
        }
        $pricingDetails = Pricing::whereHas('carType', function ($query) {
            $query->where('is_available', true);
        })
            ->where('shift_id', $shift->id)
            ->get()
            ->map(function ($pricing) use ($route) {
                $calculated =
                    + ($route['distance'] * $pricing->km_price)
                        + ($route['duration'] * $pricing->miu_price);
                $calculated = max($pricing->minimum_fare, $calculated);
                return [
                    'pricing_id' => $pricing->id,
                    'id' => $pricing->id,
                    'car_type_id' => $pricing->carType->id,
                    'car_type' => $pricing->carType->name,
                    'estimated_distance' => $route['distance'],
                    'estimated_duration' => $route['duration'],
                    'price' => number_format($calculated, 0, '.', ','),
                    'details' => [
                        'minimum_fare' => number_format($pricing->minimum_fare),
                        'flag_down_fee' => number_format($pricing?->flag_down_fee),
                        'km_price' => number_format($pricing->km_price),
                        'miu_price' => number_format($pricing->miu_price),
                    ]
                ];
            })->toArray();

        return [
            'pricing_details' => $pricingDetails,
            'points' => $formattedPoints,
        ];
    }

    public function formatPoints($points)
    {
        $totalPoints = count($points);

        $formattedPoints = collect(value: $points)->map(function ($point, $index) use ($totalPoints) {
            return [
                'latitude' => $point['latitude'],
                'longitude' => $point['longitude'],
                'address' => $point['address'],
                'type' => $this->determinePointType($index, $totalPoints),
                'sort_order' => $index + 1
            ];
        })->toArray();

        return $formattedPoints;
    }

    private function getRouteData(array $points): array
    {
        $cacheKey = $this->generateRouteCacheKey($points);

        return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($points, $cacheKey) {
            try {
                return $this->mapsService->calculateRoute($points);
            } catch (\Exception $e) {
                // Cache error state to prevent API spam
                Cache::put($cacheKey, ['error' => true], now()->addSeconds(3));
                throw $e;
            }
        });
    }

    public function generateRouteCacheKey(array $points): string
    {
        $normalized = $this->normalizePoints($points);
        return 'route_' . md5(json_encode($normalized));
    }

    public function normalizePoints(array $points): array
    {
        return collect($points)->map(function ($point) {
            return [
                'latitude' => round($point['latitude'], 6),
                'longitude' => round($point['longitude'], 6),
                'address' => trim($point['address'])
            ];
        })->sortBy('sort_order')->values()->toArray();
    }

    public function applyToRide(Ride $ride, Driver $driver): array
    {
        if (!Gate::allows('apply', [$ride])) {
            return ['status' => 'error', 'message' => __('Unauthorized')];
        }
        return $ride->state()->apply($driver);
    }

    public function startRide(Ride $ride, Driver $driver): array
    {

        if (!Gate::allows('start', [$ride])) {
            return ['status' => 'error', 'message' => __('Unauthorized')];
        }
        return $ride->state()->start();
    }

    public function arriveAtRide(Ride $ride, Driver $driver): array
    {
        if (!Gate::allows('arrive', [$ride])) {
            return ['status' => 'error', 'message' => __('Unauthorized')];
        }
        return $ride->state()->arrive();
    }

    public function completeRide(Ride $ride, Driver $driver, $data): array
    {
        try {

            if (!Gate::allows('complete', [$ride])) {
                return ['status' => 'error', 'message' => __('Unauthorized')];
            }

            return DB::transaction(function () use ($ride) {
                $ride->completed_at = now();
                $ride->save();
                $ride->refresh();
                // $lastPoint = $ride->points()->where('type', 'dropoff')->first()->update([
                //     'longitude' => $ride->driver->current_latitude,
                //     'latitude' => $ride->driver->current_longitude
                // ]);
                // Calculate the final price
                $this->recalculateFinalPrice($ride);

                // Verify final price is not zero
                if (!$ride->final_price || $ride->final_price <= 0) {

                    // Set a minimum price
                    if ($ride->pricing) {
                        $minimumFare = $ride->pricing->minimum_fare > 0 ? $ride->pricing->minimum_fare : 10;
                    } else {
                        $minimumFare = 10; // Default minimum
                    }

                    $ride->update(['final_price' => $minimumFare]);
                    $ride->refresh();
                }
                $ride->state()->complete([
                    'current_latitude' => $ride->driver->current_latitude,
                    'current_longitude' => $ride->driver->current_longitude
                ]);
                // Get wallets with null checks
                try {
                    $driverWallet = $ride->driver->wallet;
                    if (!$driverWallet) {

                        throw new \Exception('Driver wallet not found');
                    }

                    $companyWallet = Company::where('is_main', true)->first()->wallet;
                    if (!$companyWallet) {

                        throw new \Exception('Company wallet not found');
                    }
                    // Process the payment
                    app(TransactionService::class)->processRidePayment(
                        $ride,
                        $driverWallet,
                        $companyWallet,
                        $ride->final_price,
                        TransactionPaymentMethodEnum::CASH->value
                    );


                    return ['status' => 'success'];
                } catch (\Exception $e) {
                    Log::error('Error processing ride payment', [
                        'ride_id' => $ride->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            });
        } catch (\Exception $e) {
            Log::error('Error completing ride', [
                'ride_id' => $ride->id ?? 'unknown',
                'driver_id' => $driver->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['status' => 'error', 'message' => 'An error occurred while completing the ride: ' . $e->getMessage()];
        }
    }

    public function cancelRide(Ride $ride, Driver $driver, string $cancelReason)
    {
        $ride->update([
            'canceled_at' => now(),
            'status' => RideStatusEnum::CANCELED->value,
            'cancel_reason' => $cancelReason,
        ]);

        if ($ride->driver) {
            $ride->driver->update([
                'is_available' => true,
            ]);
        }
        if ($ride->type->value === RideTypeEnum::SCHEDULED->value && $driver !== null) {
            $this->recreateScheduledRide($ride);
        }
        return $ride;
    }

    protected function recreateScheduledRide(Ride $originalRide): void
    {
        $rideData = [
            'points' => $this->extractPointsData($originalRide),
            'pricing_id' => $originalRide->pricing_id,
            'type' => $originalRide->type,
            'scheduled_at' => $originalRide->scheduled_at,
            'requested_at' => $originalRide->requested_at,
            'coupon_code' => optional($originalRide->coupon)->code,
            'driver_gender' => $originalRide->driver_gender,
            'note' => $originalRide->note,
        ];
        // Convert to immediate if within 15 minutes
        if ($this->shouldConvertToImmediate($originalRide)) {
            $rideData['type'] = RideTypeEnum::IMMEDIATE->value;
            $rideData['scheduled_at'] = null;
        }

        $this->createRide($originalRide->customer_id, $rideData);
    }

    private function shouldConvertToImmediate(Ride $ride): bool
    {
        // Convert string to Carbon instance if needed
        $scheduledAt = $ride->scheduled_at instanceof \Carbon\Carbon
            ? $ride->scheduled_at
            : \Carbon\Carbon::parse($ride->scheduled_at);

        return $scheduledAt->diffInMinutes(now(), true) < 15;
    }

    public function extractPointsData(Ride $ride): array
    {
        return $ride->points->map(function ($point) {
            return [
                'latitude' => $point->latitude,
                'longitude' => $point->longitude,
                'address' => $point->address,
            ];
        })->toArray();
    }


    public function  recalculateFinalPrice(Ride $ride): void
    {
        try {

            if (!$ride->started_at || !$ride->completed_at) {
                Log::error('Missing timestamps for ride', [
                    'ride_id' => $ride->id,
                    'started_at' => $ride->started_at,
                    'completed_at' => $ride->completed_at
                ]);
                // Set price to minimum fare
                $ride->update(['final_price' => $ride->pricing->minimum_fare]);
                return;
            }

            // Calculate actual duration from timestamps (in minutes)
            // Convert string dates to Carbon instances if needed
            $startedAt = $ride->started_at instanceof \Carbon\Carbon
                ? $ride->started_at
                : \Carbon\Carbon::parse($ride->started_at);


            $completedAt = $ride->completed_at instanceof \Carbon\Carbon
                ? $ride->completed_at
                : \Carbon\Carbon::parse($ride->completed_at);

            $actualDuration = max(1, $startedAt->diffInMinutes($completedAt));

            $points = $ride->points->toArray();

            // Check if points exist
            if (empty($points)) {
                Log::error('No route points found for ride', ['ride_id' => $ride->id]);
                // Set price to minimum fare
                $ride->update(['final_price' => $ride->pricing->minimum_fare]);
                return;
            }

            $route = $this->getRouteData($points);

            // Log route data and API matrices
            Log::channel('route')->info('Route data retrieved', [
                'ride_id' => $ride->id,
                'distance' => $route['distance'],
                'duration' => $route['duration'],
                // 'points' => $points,
                // 'route_data' => $route,
                'pricing' => $ride->pricing,
                'actualDuration' => $actualDuration
            ]);

            $ride->update([
                'actual_distance' => $route['distance'],
                'actual_duration' => $actualDuration,
            ]);

            $pricing = $ride->pricing;

            // Calculate base price
            $distanceComponent = $route['distance'] * $pricing->km_price;
            $timeComponent = $actualDuration * $pricing->miu_price;
            $basePrice = $distanceComponent + $timeComponent;

            // Ensure we have a minimum price
            $finalPrice = max($basePrice, $pricing->minimum_fare);

            $ride->update(['final_price' => $finalPrice]);
        } catch (\Exception $e) {
            Log::error('Error calculating final price', [
                'ride_id' => $ride->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function applyCoupon(Ride $ride, string $code): array
    {
        try {
            $user = $ride->customer->user;
            if (!in_array($ride->status, [RideStatusEnum::PENDING, RideStatusEnum::ACCEPTED])) {
                throw new Exception(__('Cannot apply coupon to this ride status'), 422);
            }

            $coupon = Coupon::where('code', $code)
                ->validForRide($ride)
                ->firstOrFail();

            $this->validateCouponUsage($coupon,  $user);

            // Calculate discount amount
            $discountAmount = $coupon->calculateDiscount($ride->fare);

            if ($discountAmount >= $ride->fare) {
                throw new Exception(__('Coupon discount exceeds ride fare'), 422);
            }

            // DB::transaction(function () use ($ride, $coupon, $user) {
            $userCoupon = $user->coupons()
                ->where('coupons.id', $coupon->id)
                ->first();

            if (!$userCoupon) {
                // Auto-activate with initial usage
                $user->coupons()->attach($coupon->id, ['times_used' => 1]);
            } else {
                // Check existing usage
                if ($userCoupon->pivot->times_used >= $coupon->usage_limit_per_user) {
                    throw new Exception(__('Coupon usage limit exceeded'), 422);
                }

                // Increment usage
                $user->coupons()->updateExistingPivot($coupon->id, [
                    'times_used' => DB::raw('times_used + 1')
                ]);
            }

            // Apply coupon to ride
            $ride->update(['coupon_id' => $coupon->id]);

            // });

            return [
                'status' => 'success',
                'data' => $ride->fresh()->load('coupon')
            ];
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return [
                'status' => 'error',
                'message' => __('The coupon is invalid or has expired.'),
                'code' => 422
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'code' => 422
            ];
        }
    }

    private function validateCouponUsage(Coupon $coupon, User $user): void
    {
        $usage = $user->coupons()
            ->where('coupons.id', $coupon->id)
            ->first()
            ?->pivot
            ->times_used ?? 0;

        if ($usage >= $coupon->usage_limit_per_user) {
            throw new Exception(__('Coupon usage limit exceeded'), 422);
        }
    }

    /**
     * Get the in-progress ride for the given user (driver or customer)
     *
     * @param \App\Models\User $user
     * @return \App\Models\Ride|null
     */
    public function getInProgressRideForUser(User $user): ?Ride
    {
        if ($user->isDriver()) {
            return Ride::with(['points', 'customer', 'driver', 'pricing'])->where('driver_id', $user->userable->id)
                ->where('status', RideStatusEnum::IN_PROGRESS->value)
                ->latest('started_at')
                ->first();
        } elseif ($user->isCustomer()) {
            return Ride::with(['points', 'customer', 'driver', 'pricing'])->where('customer_id', $user->userable->id)
                ->where('status', RideStatusEnum::IN_PROGRESS->value)
                ->latest('started_at')
                ->first();
        }
        return null;
    }
}
