<?php

namespace App\Services;

use App\Models\CategoryUser;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class CategoryUserService extends BaseService
{
    function __construct(CategoryUser $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(CategoryUser::query())
                        ->allowedFilters([])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): CategoryUser
    {
        $model = CategoryUser::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
