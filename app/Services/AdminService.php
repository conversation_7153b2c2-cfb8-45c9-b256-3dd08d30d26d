<?php

namespace App\Services;

use App\Models\Admin;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class AdminService extends BaseService
{
    function __construct(Admin $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Admin::query())
                        ->allowedFilters([])
            ->defaultSorts('created_at')

            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data, ?Admin $admin = null): Admin
    {
        if(!isset($data['id']) && !$admin)
        {
          $data['is_active'] = isset($data['is_active']) ? (bool)$data['is_active'] : false;

          // Create new user and admin
          $user = User::create($data);
          $model = $user->admin()->create();

          $user->update([
            'userable_id' => $model->id,
            'userable_type' => Admin::class,
          ]);

          // Assign roles if provided
          if (isset($data['roles']) && is_array($data['roles'])) {
              // Get role names from IDs
              $roleNames = \App\Models\Role::whereIn('id', $data['roles'])->pluck('name')->toArray();
              $user->syncRoles($roleNames);
          }

          return $model;
        }

        // Update existing admin
        $model = $admin ?? Admin::find($data['id']);

        if ($model) {
            // Update the associated user
            $user = $model->user;

            // Extract user data
            $userData = array_intersect_key($data, array_flip([
                'first_name', 'last_name', 'email', 'phone',
                'birthdate', 'gender'
            ]));

            // Explicitly set is_active to false if not present in the request
            $userData['is_active'] = isset($data['is_active']) ? (bool)$data['is_active'] : false;

            // Update password only if provided
            if (isset($data['password']) && !empty($data['password'])) {
                $userData['password'] = $data['password'];
            }

            // Update user data
            $user->update($userData);

            // Handle profile picture if provided
            if (isset($data['profile']) && !is_null($data['profile'])) {
                $user->clearMediaCollection('profile');
                $user->addMedia($data['profile'])->preservingOriginal()->toMediaCollection('profile');
            }

            // Sync roles if provided
            if (isset($data['roles']) && is_array($data['roles'])) {
                // Get role names from IDs
                $roleNames = \App\Models\Role::whereIn('id', $data['roles'])->pluck('name')->toArray();
                $user->syncRoles($roleNames);
            }
        }

        return $model;
    }
}
