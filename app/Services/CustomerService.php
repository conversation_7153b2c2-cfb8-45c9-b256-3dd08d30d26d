<?php

namespace App\Services;

use App\Models\Customer;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class CustomerService extends BaseService
{
    function __construct(Customer $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Customer::query())
                        ->allowedFilters(["refer_code"])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Customer
    {
        $model = Customer::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
