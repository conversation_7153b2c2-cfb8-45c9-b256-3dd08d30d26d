<?php

namespace App\Services;

use App\Models\Shift;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class ShiftService extends BaseService
{
    function __construct(Shift $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Shift::query())
                        ->allowedFilters([])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Shift
    {
        $model = Shift::updateOrCreate(['id' => $data['id'] ?? null], $data);

        

        return $model;
    }
}
