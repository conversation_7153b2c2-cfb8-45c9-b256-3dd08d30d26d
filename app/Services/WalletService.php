<?php

namespace App\Services;

use App\Models\Driver;
use App\Models\Wallet;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class WalletService extends BaseService
{
    function __construct(Wallet $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Wallet::query())
            ->allowedFilters(["walletable_type"])
            ->defaultSorts('created_at')

            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    function storeOrUpdate(array $data): Wallet
    {
        $model = Wallet::updateOrCreate(['id' => $data['id'] ?? null], $data);



        return $model;
    }

    public function getDriverWallet(Driver $driver): Wallet
    {
        return $driver->wallet()
            ->with(['sentTransactions', 'receivedTransactions'])
            ->firstOrFail();
    }
}
