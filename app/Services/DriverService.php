<?php

namespace App\Services;

use App\Enums\DriverStatusEnum;
use App\Jobs\Driver\SendDriverApprovedNotificationJob;
use App\Jobs\Driver\SendDriverRejectedNotificationJob;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\QueryBuilder\QueryBuilder;

class DriverService extends BaseService
{
    function __construct(Driver $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Driver::query())
            ->allowedFilters(["license_number", "status", "refuse_reason"])
            ->defaultSorts('created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }



    public function store($data): Model
    {
        DB::beginTransaction();

        try {
            // If user_id is provided from dashboard, use the existing user
            if (isset($data['user_id']) && !empty($data['user_id'])) {
                $user = User::findOrFail($data['user_id']);
            } else {
                // Otherwise create a new user
                $user = User::firstOrCreate(
                    [
                        'phone' => $data['phone'],
                    ],
                    [
                        'phone' => $data['phone'],
                        'first_name' => $data['first_name'],
                        'last_name' => $data['last_name'],
                        'birthdate' => $data['birthdate'],
                        'gender' => $data['gender'],
                        'email' => $data['email'] ?? null,
                    ]
                );
            }

            $driver = $user->driver()->firstOrCreate([
                'license_number' => $data['license_number'],
                'user_id' => $user->id,
            ]);
            $user->update([
                'userable_type' => Driver::class,
                'userable_id' => $driver->id
            ]);
             Vehicle::create([
                'driver_id' => $driver->id,
                'model' => $data['model'],
                'plate_number' => $data['plate_number'],
                'color' => $data['color'],
                'seats' => $data['seats'],
                'manufacturing_year' => $data['manufacturing_year'] ?? null,
            ]);

            if (isset($data['car_type_ids']) && is_array($data['car_type_ids'])) {
                $driver->carTypes()->sync($data['car_type_ids']);
            }
            if (isset($data['region_id']) && !is_null($data['region_id'])) {
                $driver->regions()->sync($data['region_id']);
            }
            if (isset($data['front_license_image']) && !is_null($data['front_license_image'])) {
                $driver->clearMediaCollection('front_license_image');
                $driver->addMedia($data['front_license_image'])->preservingOriginal()->toMediaCollection('front_license_image');
            }
            if (isset($data['back_license_image']) && !is_null($data['back_license_image'])) {
                $driver->clearMediaCollection('back_license_image');
                $driver->addMedia($data['back_license_image'])->preservingOriginal()->toMediaCollection('back_license_image');
            }
            if (isset($data['car_image']) && !is_null($data['car_image'])) {
                $driver->clearMediaCollection('car_image');
                $driver->addMedia($data['car_image'])->preservingOriginal()->toMediaCollection('car_image');
            }
            if (isset($data['car_mechanics_image']) && !is_null($data['car_mechanics_image'])) {
                $driver->clearMediaCollection('car_mechanics_image');
                $driver->addMedia($data['car_mechanics_image'])->preservingOriginal()->toMediaCollection('car_mechanics_image');
            }
            DB::commit();
            return $driver->load('user');

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateDriverStatus(int $id, string $status, ?string $refuse_reason = null): void
    {
        $driver = $this->show($id);

        $driver->update([
            'status' => $status,
            'refuse_reason' => $refuse_reason,
        ]);

        if ($status == DriverStatusEnum::APPROVED->value) {
            // Create wallet if it doesn't exist
            if (!$driver->wallet) {
                $driver->wallet()->create([
                    'balance' => 2000,
                    'is_active' => true
                ]);
            }
            SendDriverApprovedNotificationJob::dispatch($driver);
        } elseif ($status == DriverStatusEnum::REFUSED->value) {
            SendDriverRejectedNotificationJob::dispatch($driver, $refuse_reason);
        }
    }

    public function updateDriver(Driver $driver, array $data): Driver
{
    DB::beginTransaction();

    try {
        // Update user information
        if (isset($data['first_name']) || isset($data['last_name']) || isset($data['phone']) || isset($data['email']) || isset($data['gender']) || isset($data['birthdate'])) {
            $driver->user->update([
                'phone' => $data['phone'] ?? $driver->user->phone,
                'first_name' => $data['first_name'] ?? $driver->user->first_name,
                'last_name' => $data['last_name'] ?? $driver->user->last_name,
                'email' => $data['email'] ?? $driver->user->email,
                'gender' => $data['gender'] ?? $driver->user->gender,
                'birthdate' => $data['birthdate'] ?? $driver->user->birthdate,
            ]);
        }

        // Update driver information
        $driver->update([
            'license_number' => $data['license_number'] ?? $driver->license_number,
            'status' => $data['status'] ?? $driver->status,
            'is_available' => isset($data['is_available']) ? $data['is_available'] : $driver->is_available,
            'refuse_reason' => $data['refuse_reason'] ?? $driver->refuse_reason,
        ]);

        // Update vehicle information
        if ($driver->vehicle) {
            $driver->vehicle->update([
                'plate_number' => $data['plate_number'] ?? $driver->vehicle->plate_number,
                'color' => $data['color'] ?? $driver->vehicle->color,
                'seats' => $data['seats'] ?? $driver->vehicle->seats,
                'manufacturing_year' => $data['manufacturing_year'] ?? $driver->vehicle->manufacturing_year,
            ]);
        }

        if (isset($data['car_type_ids'])) {
            $driver->carTypes()->sync($data['car_type_ids']);
        }

        if (isset($data['region_id'])) {
            $driver->regions()->sync($data['region_id']);
        }

        if (isset($data['front_license_image'])) {
            $driver->clearMediaCollection('front_license_image');
            $driver->addMedia($data['front_license_image'])
                ->preservingOriginal()
                ->toMediaCollection('front_license_image');
        }

        if (isset($data['back_license_image'])) {
            $driver->clearMediaCollection('back_license_image');
            $driver->addMedia($data['back_license_image'])
                ->preservingOriginal()
                ->toMediaCollection('back_license_image');
        }

        if (isset($data['car_image'])) {
            $driver->clearMediaCollection('car_image');
            $driver->addMedia($data['car_image'])
                ->preservingOriginal()
                ->toMediaCollection('car_image');
        }

        if (isset($data['car_mechanics_image'])) {
            $driver->clearMediaCollection('car_mechanics_image');
            $driver->addMedia($data['car_mechanics_image'])
                ->preservingOriginal()
                ->toMediaCollection('car_mechanics_image');
        }

        if (isset($data['profile_picture'])) {
            $driver->user->clearMediaCollection('profile_picture');
            $driver->user->addMedia($data['profile_picture'])
                ->preservingOriginal()
                ->toMediaCollection('profile_picture');
        }

        DB::commit();
        return $driver->fresh()->load(['user', 'vehicle', 'carTypes', 'regions']);

    } catch (\Exception $e) {
        DB::rollBack();
        throw $e;
    }
}
}