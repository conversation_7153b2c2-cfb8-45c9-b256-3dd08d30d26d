<?php

namespace App\Services;

use App\Http\Resources\AdminResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\DriverResource;
use App\Models\Admin;
use App\Models\Customer;
use App\Models\DeviceToken;
use App\Models\Driver;
use App\Models\User;
use App\Models\TempAuth;
use App\Services\Sms\MtnSmsService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Enums\TopicEnum;
use Modules\Notification\Services\NotificationService;

class AuthService
{

    public function __construct(protected MtnSmsService $smsService, protected NotificationService $notificationService)
    {
    }

    public function requestOtp(string $phone)
    {
        // Check if the user has requested an OTP within the last 1 minutes
        $cacheKey = 'otp_request_' . $phone;
        $lastRequestTime = cache()->get($cacheKey);

        if ($lastRequestTime) {
            $elapsedTimeInSeconds = $lastRequestTime->diffInSeconds(now());
            $waitTimeInSeconds = 60 - $elapsedTimeInSeconds; // 1 minutes = 60 seconds
            if ($waitTimeInSeconds > 0) {
                return [
                    'success' => false,
                    'message' => __('Please wait :seconds seconds before requesting another OTP.', ['seconds' => $waitTimeInSeconds]),
                    'wait_time' => $waitTimeInSeconds
                ];
            }
        }

        // Find user by phone or create a TempAuth record
        $userOrTemp = User::where('phone', $phone)->firstOr(function () use ($phone) {
            return TempAuth::updateOrCreate(['phone' => $phone]);
        });

        $otpCode = random_int(10000, 99999);

        // Send SMS via SmsService
        $this->smsService->sendSms($phone, __("Hi there! Your OTP code for Alia App is : :otpCode.", ['otpCode' => $otpCode]));

        $userOrTemp->update(['code' => $otpCode]);

        // Store the current time in cache to track rate limitin
         Cache::put($cacheKey, now(), now()->addMinutes(2));

        DB::commit();

        return [
            'success' => true,
        ];
    }

    public function verifyOTP(array $data)
    {
        $user = User::wherePhone($data['phone'])->first() ?: TempAuth::wherePhone($data['phone'])->first();

        Log::info('user:',$user->toArray());
        if (!$user || ($user->code != $data['code'] && $data['code'] != config('app.test-otp'))) {
            return ['status' => 'error', 'message' => __('Incorrect phone number or verification code.')];
        }


        if ($user instanceof TempAuth) {
            $user->update(['is_verified' => true]);
            return ['status' => 'success', 'message' => __('Profile must be completed.'), 'data' => ['must_complete_profile' => true]];
        }


        if ($user) {
            if (!$user->is_active) {
                return ['status' => 'error', 'message' => __('Sorry, your account is blocked.')];
            }
            if(isset($data['fcm_token'])){

                $this->registerDeviceToken($user,$data['fcm_token']);
            }
            Log::info('line 95:',$user->toArray());

            $user->update(['code' => null]);
            $token = $user->createToken('API_TOKEN')->plainTextToken;
            $userable = $user->userable;
            Log::info('Customer:',[ $user->userable]);

            if ($userable instanceof Customer) {
            Log::info('Customer:',$user->toArray());

                $userable->token = $token;
                return ['status' => 'success', 'message' => __('Login successful.'), 'data' => new CustomerResource($userable->load('user'))];
            }
            if ($userable instanceof Driver) {
                $userable->token = $token;
                return ['status' => 'success', 'message' => __('Login successful.'),  'data' => new DriverResource($userable->load('user'))];
            }

        }

        return ['status' => 'error', 'message' => __('Incorrect credentials.')];
    }

    public function loginAdmin(array $data): bool
    {
        $user = User::query()->where('email', $data['email'])->first();

        if (! $user || ! Hash::check($data['password'], $user->password)) {
            return false;
        }
        $userable = $user->userable;
        if ($userable instanceof Admin && $user->is_active == true) {
            Auth::login($user);
            return true;
        }

        return false;
    }
    public function logout($is_web_reuqest = false): bool
    {
        if ($is_web_reuqest) {
            Auth::guard('web')->logout();
        } else {
            /**
             * @var User $user
             */
            $user = auth()->user();
            $user->currentAccessToken()->delete();
            DeviceToken::where('device_id', request()->header('device-id'))->delete();
        }

        return true;
    }

    public function completeProfile($data): array
    {

        $user = User::updateOrCreate(['phone' => $data['phone']],$data);
        $data['is_active'] = true;

        if (isset($data['profile_picture']) && !is_null($data['profile_picture'])) {
            $user->clearMediaCollection('profile_picture');
            $user->addMedia($data['profile_picture'])->preservingOriginal()->toMediaCollection('profile_picture');
        }
        $customer = Customer::updateOrCreate(['user_id' => $user->id ?? null], [
            'user_id' => $user->id
        ]);

        $data['userable_id'] = $customer->id;
        $data['userable_type'] = Customer::class;

        $user->update($data);
        $token = $user->createToken('API_TOKEN')->plainTextToken;
        if(isset($data['fcm_token'])){

            $this->registerDeviceToken($user,$data['fcm_token']);
        }
        $userable = $user->userable;

        if ($userable instanceof Customer) {
            $userable->token = $token;
            return ['status' => 'success', 'message' => __('Profile updated successfully'), 'data' => new CustomerResource($userable)];
        }

        // Fallback return in case the user is not a Customer
        return ['status' => 'error', 'message' => __('Failed to complete profile')];
    }
    public function updateProfile($data)
    {
        $user = auth()->user();
        $user->update($data);
        if (isset($data['profile_picture']) && !is_null($data['profile_picture'])) {
            $user->clearMediaCollection('profile_picture');
            $user->addMedia($data['profile_picture'])->preservingOriginal()->toMediaCollection('profile_picture');
        }
        $userable = $user->userable;

        // Check if userable exists
        if (!$userable) {
            return ['status' => 'error', 'message' => __('User profile not found')];
        }

        // Hide the tokenable relation to prevent recursion
        $token = $user->currentAccessToken();
        $token->makeHidden('tokenable');
        $userable->token = $token;

        if ($userable instanceof Customer) {
            return ['status' => 'success', 'message' => __('Profile updated successfully'), 'data' => new CustomerResource($userable)];
        } elseif ($userable instanceof Driver) {
            return ['status' => 'success', 'message' => __('Profile updated successfully'), 'data' => new DriverResource($userable)];
        } else {
            // Generic response for other user types
            return ['status' => 'success', 'message' => __('Profile updated successfully'), 'data' => $userable];
        }
    }

    public function deleteUserAccount(): array
    {
        try {
            /**
             * @var User $user
             */
            $user = auth()->user();

            if ($user->userable) {
                $user->userable->delete();
            }

            $user->deviceTokens()->delete();
            $user->delete();
            return [
                'success' => true,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to delete user: ' . $e->getMessage());
            return [
                'success' => false,
            ];
        }
    }


    public function registerDeviceToken(User $user, $deviceToken):void
    {
        DeviceToken::whereToken($deviceToken)->delete();
        $user->deviceTokens()
            ->create(['token' => $deviceToken]);

        $parts = explode('\\', $user->userable_type);
        $modelName = end($parts);
        $topic = strtolower($modelName);

        $this->notificationService->registerTokenToTopics($deviceToken, [TopicEnum::ALL->value, $topic]);
    }

    public function createAccessToken($user): array
    {
        $data = [
            'token' => $user->createToken("API TOKEN")->plainTextToken,
            'role' => getResourceNameFromUserableType($user->userable_type),
        ];
        return $data;
    }


}
