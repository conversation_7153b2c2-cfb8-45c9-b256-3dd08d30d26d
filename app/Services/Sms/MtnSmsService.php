<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MtnSmsService
{

    protected $url;
    protected $user;
    protected $pass;
    protected $from;

    /**
     * MtnService Constructor
     *
     * Initializes the service with configurations from `config/mtn.php`.
     */
    public function __construct()
    {
        $this->url = config('mtn.url');
        $this->user = config('mtn.user');
        $this->pass = config('mtn.pass');
        $this->from = config('mtn.from');
    }

    /**
     * Send SMS
     *
     * Sends an SMS to the given phone number with the provided verification code.
     *
     * @param string $number The recipient's phone number (including country code).
     * @param string $code The verification code to be sent.
     * @return array The API response as an associative array.
     */
    public function sendSms($number, $code)
    {
        try {
            // Normalize the phone number
            $number = $this->normalizeNumber($number);
            $message = $code;
            // Construct the message
            // $message = __("Your verification code is: :code", ['code' => $code]);
            $lang = app()->getLocale();

            // Convert message to UCS-2BE hexadecimal format
            $arr = unpack('H*hex', iconv('UTF-8', 'UCS-2BE', $message));
            $message = strtoupper($arr['hex']);

            // Define API request parameters
            $params = [
                'User' => $this->user,
                'Pass' => $this->pass,
                'From' => $this->from,
                'Gsm' => $number,
                'Msg' => $message,
                'Lang' => $lang == 'ar' ? 0 : 1, // Language: 0 for Arabic, 1 for English
            ];

            // Send GET request to the API
            $response = Http::get($this->url, $params);

            return $response->body();
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('MTN SMS error: ' . $e->getMessage(), ['exception' => $e]);
            return false;
        }
    }

    /**
     * Normalize the phone number
     *
     * Ensures the phone number starts with the country code (without +)
     * and removes leading 0 after the country code.
     *
     * @param string $number
     * @return string
     */
    protected function normalizeNumber($number)
    {
        // Remove the + if it exists
        $number = ltrim($number, '+');

        // Remove the leading 0 after the country code (e.g., 9630 -> 963)
        $number = preg_replace('/^9630/', '963', $number);

        return $number;
    }
}
