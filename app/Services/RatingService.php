<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Driver;
use App\Models\Rating;
use App\Models\Ride;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class RatingService extends BaseService
{
    function __construct(Rating $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(Rating::query())
            ->allowedFilters(['ride_id', 'rating'])
            ->defaultSorts('created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    /**
     * Rate a driver by a customer
     */
    public function rateDriver(Ride $ride, $customer, array $data): Rating
    {
        // Check if the customer has already rated this driver for this ride
        $existingRating = Rating::where('ride_id', $ride->id)
            ->where('rater_id', $customer->id)
            ->where('rater_type', get_class($customer))
            ->where('rateable_id', $ride->driver_id)
            ->where('rateable_type', Driver::class)
            ->first();

        if ($existingRating) {
            throw new \Exception(__('api.rating.errors.already_rated'));
        }

        // Create the rating
        $rating = new Rating([
            'ride_id' => $ride->id,
            'rating' => $data['rating'],
            'comment' => $data['comment'] ?? null,
        ]);

        // Set the polymorphic relationships
        $rating->rater()->associate($customer);
        $rating->rateable()->associate($ride->driver);
        $rating->save();

        return $rating;
    }

    /**
     * Rate a customer by a driver
     */
    public function rateCustomer(Ride $ride, $driver, array $data): Rating
    {
        // Check if the driver has already rated this customer for this ride
        $existingRating = Rating::where('ride_id', $ride->id)
            ->where('rater_id', $driver->id)
            ->where('rater_type', get_class($driver))
            ->where('rateable_id', $ride->customer_id)
            ->where('rateable_type', Customer::class)
            ->first();

        if ($existingRating) {
            throw new \Exception(__('api.rating.errors.already_rated'));
        }

        // Create the rating
        $rating = new Rating([
            'ride_id' => $ride->id,
            'rating' => $data['rating'],
            'comment' => $data['comment'] ?? null,
        ]);

        // Set the polymorphic relationships
        $rating->rater()->associate($driver);
        $rating->rateable()->associate($ride->customer);
        $rating->save();

        return $rating;
    }

    /**
     * Get ratings for a driver
     */
    public function getDriverRatings($driver): Collection
    {
        return Rating::where('rateable_id', $driver->id)
            ->where('rateable_type', get_class($driver))
            ->with(['rater', 'ride'])
            ->get();
    }

    /**
     * Get ratings for a customer
     */
    public function getCustomerRatings($customer): Collection
    {
        return Rating::where('rateable_id', $customer->id)
            ->where('rateable_type', get_class($customer))
            ->with(['rater', 'ride'])
            ->get();
    }

    /**
     * Get average rating for a driver
     */
    public function getDriverAverageRating($driver): float
    {
        return Rating::where('rateable_id', $driver->id)
            ->where('rateable_type', get_class($driver))
            ->avg('rating') ?? 0;
    }

    /**
     * Get average rating for a customer
     */
    public function getCustomerAverageRating($customer): float
    {
        return Rating::where('rateable_id', $customer->id)
            ->where('rateable_type', get_class($customer))
            ->avg('rating') ?? 0;
    }
}
