<?php

namespace App\Services;

use App\Models\Coupon;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\QueryBuilder;

class CouponService extends BaseService
{
    function __construct(Coupon $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active coupons for user with filtering
     */
    public function getActiveCoupons(User $user): LengthAwarePaginator
    {
        return QueryBuilder::for($user->activeCoupons()->with('translations'))
            ->allowedFilters(['code', 'type'])
            ->defaultSort('-created_at')
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    /**
     * Activate coupon for user
     * 
     * @throws \Exception If coupon is invalid or activation fails
     */
    public function activateCoupon(string $code, User $user): Coupon
    {
        return DB::transaction(function () use ($code, $user) {
            $coupon = Coupon::where('code', $code)
                ->validForActivation()
                ->firstOrFail();

            // Check existing usage
            $existing = $user->coupons()
                ->where('coupons.id', $coupon->id)
                ->first();

            if ($existing) {
                if ($existing->pivot->times_used >= $coupon->usage_limit_per_user) {
                    throw new \Exception(__('Coupon usage limit exceeded'), 422);
                }
                return $coupon; // Already attached but under limit
            }

            $user->coupons()->attach($coupon->id, ['times_used' => 0]);
            return $coupon->load('translations');
        });
    }

    function storeOrUpdate(array $data): Coupon
    {
        $model = Coupon::updateOrCreate(['id' => $data['id'] ?? null], $data);



        return $model;
    }
}
