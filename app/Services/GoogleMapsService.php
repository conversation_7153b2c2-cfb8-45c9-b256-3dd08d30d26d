<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class GoogleMapsService
{
    private Client $client;
    private const CACHE_VERSION = 'v1';

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://maps.googleapis.com/maps/api/',
            'timeout' => 10,
        ]);
    }

    public function calculateRoute(array $points): array
    {

        $response = $this->client->get('directions/json', [
            'query' => $this->buildQueryParameters($points)
        ]);

        $data = json_decode($response->getBody(), true);

        $this->logRequest('directions/json',$data);

        if ($data['status'] !== 'OK') {
            throw new Exception("Google Maps API Error: {$data['status']}");
        }

        return $this->parseRouteData($data);
    }

    private function buildQueryParameters(array $points): array
    {
        return [
            'origin' => $this->formatPoint($points[0]),
            'destination' => $this->formatPoint(end($points)),
            'waypoints' => $this->formatWaypoints(array_slice($points, 1, -1)),
            'key' => config('services.google_maps.api_key'),
            'units' => 'metric',
            'optimize' => 'true',
            'alternatives' => 'true',  // Request alternative routes
            'mode' => 'driving',       // Specify driving mode
            'traffic_model' => 'best_guess', // Use best guess for traffic conditions
            'departure_time' => 'now', // Use current time for traffic calculations
        ];
    }

    private function formatPoint(array $point): string
    {
        return "{$point['latitude']},{$point['longitude']}";
    }

    private function formatWaypoints(array $points): string
    {
        return implode('|', array_map(fn($p): string => $this->formatPoint($p), $points));
    }

    private function parseRouteData(array $data): array
    {
        // If we have multiple routes, find the shortest one
        if (count($data['routes']) > 1) {
            $shortestDistance = PHP_FLOAT_MAX;
            $shortestRouteIndex = 0;

            foreach ($data['routes'] as $index => $route) {
                $totalDistance = 0;
                foreach ($route['legs'] as $leg) {
                    $totalDistance += $leg['distance']['value'];
                }

                if ($totalDistance < $shortestDistance) {
                    $shortestDistance = $totalDistance;
                    $shortestRouteIndex = $index;
                }
            }

            $route = $data['routes'][$shortestRouteIndex];
        } else {
            // Just use the first route if there's only one
            $route = $data['routes'][0];
        }

        $legs = $route['legs'];

        $totalSeconds = array_sum(array_column(array_column($legs, 'duration'), 'value'));
        $durationMinutes = round($totalSeconds / 60, 2);

        return [
            'distance' => array_sum(array_column(array_column($legs, 'distance'), 'value')) / 1000,
            'duration' => $durationMinutes,
            'polyline' => $route['overview_polyline']['points'],
        ];
    }


    private function generateCacheKey(string $query): string
    {
        return implode(':', [
            self::CACHE_VERSION,
            'gmaps',
            'search',
            md5(Str::lower($query)),
            app()->getLocale()
        ]);
    }

    private function buildSearchParams(string $query): array
    {
        return [
            'input' => $query,
            // 'types' => '(cities)',
            'components' => 'country:sy',
            'language' => app()->getLocale(),
            'key' => config('services.google_maps.api_key')
        ];
    }

    private function parseResponse(array $data): array
    {
        if ($data['status'] !== 'OK') {
            return [];
        }

        return [
            'attribution' => 'Powered by Google',
            'suggestions' => array_map(function ($prediction) {
                $details = $this->getPlaceDetails($prediction['place_id']);

                return [
                    'description' => $prediction['description'],
                    'place_id' => $prediction['place_id'],
                    'location' => $details['geometry']['location']
                ];
            }, $data['predictions'])
        ];
    }

    public function searchAreas(string $query): array
    {
        $cacheKey = $this->generateCacheKey($query);
        $cacheHit = true;

        try {
            $result = Cache::remember($cacheKey, $this->getTTL($query), function () use ($query, &$cacheHit) {
                // This closure only executes on cache MISS
                $cacheHit = false;
                $response = $this->client->get('place/autocomplete/json', [
                    'query' => $this->buildSearchParams($query)
                ]);

                $data = json_decode($response->getBody(), true);
                $this->logRequest('place/autocomplete/json', $data);
                return $this->parseResponse($data);
            });

            return array_merge($result, ['cache_hit' => $cacheHit]);

        } catch (Exception $e) {
            $staleData = $this->getCachedFallback($cacheKey);
            if ($staleData) {
                return array_merge($staleData, ['cache_hit' => true]);
            }
            throw new Exception(__('Location service unavailable'), 503);
        }
    }

    private function getPlaceDetails(string $placeId): array
    {
        $cacheKey = self::CACHE_VERSION . ':gmaps:details:' . $placeId;
        $cacheHit = true; // Assume cache hit by default

        $result = Cache::remember($cacheKey, 604800, function () use ($placeId, &$cacheHit) {
            $cacheHit = false; // Only set to false when fetching fresh data
            $response = $this->client->get('place/details/json', [
                'query' => [
                    'place_id' => $placeId,
                    'fields' => 'geometry',
                    'key' => config('services.google_maps.api_key')
                ]
            ]);
            $data = json_decode($response->getBody(), true);

            $this->logRequest('place/details/json', $data);

            return $data['result'];
        });

        return array_merge($result, ['cache_hit' => $cacheHit]);
    }

    private function getTTL(string $query): int
    {
        $commonQueries = config('services.google_maps.cache.common_queries', []);
        $query = Str::lower(trim($query));
        return in_array($query, $commonQueries)
            ? config('services.google_maps.cache.ttl.common')
            : config('services.google_maps.cache.ttl.default');
    }

    private function getCachedFallback(string $cacheKey): ?array
    {
        return Cache::get($cacheKey);
    }

    private function logRequest(string $endpoint, array $data): void
    {

        Log::channel('google')->info('Google Maps API', [
            'endpoint' => $endpoint,
            'status' => $data['status'] ?? 'unknown',
        ]);

    }
}
