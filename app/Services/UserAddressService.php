<?php

namespace App\Services;

use App\Models\UserAddress;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Spatie\QueryBuilder\QueryBuilder;

class UserAddressService extends BaseService
{
    function __construct(UserAddress $model)
    {
        parent::__construct($model);
    }

    public function index(array $data = []): LengthAwarePaginator | Collection
    {
        return QueryBuilder::for(auth()->user()->addresses())
                        ->allowedFilters(["name"])
            ->defaultSorts('created_at')
 
            ->paginate(config('pagination.page-size'))
            ->withQueryString();
    }

    public function store($data): UserAddress
    {

        $data['user_id'] = auth()->user()->id;

        return parent::store($data);

    }

    public function update(Model|UserAddress $user_address , $data): UserAddress
    {

        $data['user_id'] = auth()->user()->id;
        $user_address = parent::update($user_address,$data);
        return $user_address;

    }

    
}
