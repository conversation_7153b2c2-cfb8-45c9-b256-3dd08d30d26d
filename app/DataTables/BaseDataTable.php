<?php

namespace App\DataTables;

use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Column;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Services\DataTable;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

abstract class BaseDataTable extends DataTable
{
    protected string $modelClass;
    protected array $relationships = [];
    protected string $tableName;
    protected bool $includeCreateButton = true;
    protected array $options = [];

    /**
     * Constructor method to initialize table name based on the model class.
     */
    public function __construct()
    {
        parent::__construct();
        $this->tableName = (new $this->modelClass)->getTable();
        $modelNameSnake = Str::snake(class_basename($this->modelClass));
        $singularModelName = Str::singular($modelNameSnake);
        $pluralModelName = Str::plural($modelNameSnake);

        // Check for both singular and plural permissions
        $canUpdate = Auth::check() && (Auth::user()->can("update-{$singularModelName}") || Auth::user()->can("update-{$pluralModelName}"));
        $canDelete = Auth::check() && (Auth::user()->can("delete-{$singularModelName}") || Auth::user()->can("delete-{$pluralModelName}"));
        $canList = Auth::check() && (Auth::user()->can("list-{$singularModelName}") || Auth::user()->can("list-{$pluralModelName}"));

        info("Checking permissions for model: {$modelNameSnake}");
        info("Can update: " . ($canUpdate ? 'true' : 'false'));
        info("Can delete: " . ($canDelete ? 'true' : 'false'));
        info("Can list: " . ($canList ? 'true' : 'false'));

        // Create default options
        $defaultOptions = [
            'edit' => ['status' => $canUpdate],
            'delete' => $canDelete,
            'show' => $canList,
            'with_trashed' => 0,
        ];

        // Merge with child class options, but preserve nested arrays like 'edit'
        foreach ($this->options as $key => $value) {
            if (is_array($value) && isset($defaultOptions[$key]) && is_array($defaultOptions[$key])) {
                $defaultOptions[$key] = array_merge($defaultOptions[$key], $value);
            } else {
                $defaultOptions[$key] = $value;
            }
        }

        $this->options = $defaultOptions;
    }

    /**
     * Method to return custom table buttons for child classes.
     * This method should be implemented by the child class to add additional buttons.
     *
     * @return array
     */
    abstract public function getCustomTableButtons(): array;

    /**
     * Apply custom filters to the query.
     * This method can be overridden by child classes to define specific filters before selecting columns.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        $query->where('id', '!=', 1);

        return $query;
    }

    /**
     * Build the DataTable instance by adding column actions, IDs, and other configurations.
     * The query is passed to the `applyFilters` method before rendering the table.
     *
     * @param  QueryBuilder $query
     * @return EloquentDataTable
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($this->applyFilters($query)))
            ->editColumn('action', function ($item) {
                $route = "dashboard." . $this->tableName;
                $id = $item->id;
                $model = class_basename($this->modelClass);
                $options = $this->options;
                return view()->make('_partials.actions', ['id' => $id, 'route' => $route, 'options' => $options, 'model' => $model])->render();
            })
            ->setRowId('id');
    }

    /**
     * Build the base query for the DataTable.
     * Applies relationships and custom filters before the select statement.
     *
     * @return QueryBuilder
     */
    public function query(): QueryBuilder
    {
        $model = new $this->modelClass;

        $query = $model->newQuery()
            ->with($this->relationships);
        return $this->applyFilters($query)
            ->select("{$this->tableName}.*")
            ->distinct();
    }

    /**
     * Build the HTML structure for the DataTable.
     * Includes table ID, buttons, column definitions, and other table configurations.
     *
     * @return HtmlBuilder
     */
    public function html(): HtmlBuilder
    {
        $addRoute = route('dashboard.' . $this->tableName . '.create');
        return $this->builder()
            ->setTableId("{$this->tableName}-table")
            ->addTableClass('dt-responsive')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(0)
            ->responsive(true)
            ->stateSave(true)
            ->dom($this->getDomStructure())
            ->selectStyleSingle()
            ->addAction(['printable' => false, 'exportable' => false, 'className' => 'dt-center', 'title' => trans('Actions')])
            ->buttons($this->getDefaultTableButtons($addRoute))
            ->language($this->getDataTableLanguageUrl())
            ->addTableClass('table table-bordered table-hover')
            ->initComplete($this->getInitCompleteScript());
    }

    /**
     * Define the DOM structure of the DataTable.
     * Specifies the placement of table components such as header, footer, and buttons.
     *
     * @return string
     */
    protected function getDomStructure(): string
    {
        return '
                <"card mb-3"
                    <"card-body py-0"
                        <"card-header p-0 d-flex justify-content-between align-items-center"

                            <"d-flex justify-content-between align-items-center mx-0 row"
                                <"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"fr>
                            >

                            <"head-label"><"dt-action-buttons"B>
                        >
                    >
                >
                <"card"
                    <"card-body"
                        <"table-responsive"t>
                        <"d-flex justify-content-between mx-0 row"
                            <"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>
                        >
                    >
                >
            ';
    }

    /**
     * Define the script for handling events when DataTable initialization is complete.
     * Used for adding additional behaviors such as custom filtering or cloning headers.
     *
     * @return string
     */
    protected function getInitCompleteScript(): string
    {
        return 'function () {
            var dt_filter = this.api();
            $(`#' . $this->tableName . '-table thead tr`).clone(true).appendTo(`#' . $this->tableName . '-table thead`);
            $(`#' . $this->tableName . '-table thead tr:eq(1) th`).each(function (i) {
                var column = dt_filter.column(i);
                var title = $(this).text();
                if (column.settings()[0].aoColumns[i].bSearchable) {
                    $(this).html(\'<input type="text" class="form-control form-control-sm" placeholder="\' + title + \'" />\');
                    $(\'input\', this).on(\'keyup change\', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
                } else {
                    $(this).html(\'\'); // Clear the header for non-searchable columns
                }
            });
            // Disable sorting on the second header row for searchable columns
            $(`#' . $this->tableName . '-table thead tr:eq(1) th`).unbind("click");
        }';
    }

    /**
     * Define the default buttons for the DataTable.
     * Child classes can override this to add or modify buttons. A "Create" button can also be optionally included.
     *
     * @param  string $addRoute  The URL route for the "Create" button.
     * @return array
     */
    public function getDefaultTableButtons(string $addRoute): array
    {
        $buttonClass = 'btn mx-2 px-3 py-2 mt-0 rounded-pill';
        $defaultButtons = [
            Button::make('colvis')->addClass($buttonClass . ' btn-outline-primary')->text(__('Column Visibility')),
            [
                'extend' => 'collection',
                'text' => __('Export'),
                'className' =>  $buttonClass . ' btn-outline-warning',
                'buttons' => [
                    'excel',
                    'csv',
                    'pdf',
                    'print',
                    'copy',
                ],
            ],
        ];

        // Conditionally add the "Create" button based on the class attribute and user permissions
        $modelNameSnake = Str::snake(class_basename($this->modelClass));
        $singularModelName = Str::singular($modelNameSnake);
        $pluralModelName = Str::plural($modelNameSnake);

        // Check for both singular and plural permissions
        $canCreate = Auth::check() && (Auth::user()->can("create-{$singularModelName}") || Auth::user()->can("create-{$pluralModelName}"));

        if ($this->includeCreateButton && $canCreate) {
            $defaultButtons[] = [
                'text' => __('Create'),
                'className' => $buttonClass . ' btn-outline-info',
                'action' => 'function (e, dt, node, config) { window.location.href = "' . $addRoute . '"; }',
            ];
        }

        return array_merge($defaultButtons, $this->getCustomTableButtons());
    }

    /**
     * Define the columns for the DataTable.
     * Child classes should override this method to define their own columns.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [];
    }

    /**
     * Get the URL for the DataTable language file based on the application's locale.
     * Provides support for multilingual DataTables.
     *
     * @return string
     */
    public function getDataTableLanguageUrl()
    {
        return app()->getLocale() == "ar" ?
            "https://cdn.datatables.net/plug-ins/2.0.7/i18n/ar.json" :
            "https://cdn.datatables.net/plug-ins/2.0.7/i18n/en-GB.json";
    }

    /**
     * Generate a filename for exported DataTable files.
     * The filename is based on the table name and current timestamp.
     *
     * @return string
     */
    protected function filename(): string
    {
        return "{$this->tableName}_" . date('YmdHis');
    }
}
