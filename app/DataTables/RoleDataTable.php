<?php

namespace App\DataTables;

use App\Models\Role;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class RoleDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param  QueryBuilder  $query  Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->editColumn('action', function ($role) {
                $route = 'dashboard.roles';
                $id = $role->id;
                $model = 'Role';
                $user = auth()->user();
                $options = [
                    'edit' => ['status' => $user->can('update-role')],
                    'with_trashed' => 0,
                    'delete' => $user->can('delete-role'),
                ];
                $html = view()->make('_partials.actions', ['id' => $id, 'route' => $route, 'options' => $options, 'model' => $model])->render();

                return $html;
            })->editColumn('created_at', function ($role) {
                return $role->created_at->format('Y-m-d');
            })->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Role $model): QueryBuilder
    {
        return $model
            ->latest()
            ->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        $buttonClass = 'btn mx-2 px-4 py-3 mt-0';
        $addRoute = route('dashboard.roles.create');
        $dataTableLanguageUrl = app()->getLocale() == 'ar' ?
            'https://cdn.datatables.net/plug-ins/2.0.7/i18n/ar.json' :
            'https://cdn.datatables.net/plug-ins/2.0.7/i18n/en-GB.json';
        $buttons = [
            Button::make('colvis')->addClass($buttonClass . ' btn-success')->text(__('Column Visibility')),
            [
                'extend' => 'collection',
                'text' => __('Export'),
                'className' => $buttonClass . ' btn-warning',
                'buttons' => [
                    'excel',
                    'csv',
                    'pdf',
                    'print',
                    'copy',
                ],
            ],

        ];
        if (auth()->user()->can('create-role')) {
            $buttons[] = [
                'text' => __('Create'),
                'className' => $buttonClass . ' btn-primary',
                'action' => 'function (e, dt, node, config) { window.location.href = "' . $addRoute . '"; }',
            ];
        }

        return $this->builder()
            ->setTableId('role-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('
            <"card-header border-bottom p-3 d-flex justify-content-between align-items-center"
                <"head-label"><"dt-action-buttons"B>
            >
            <"d-flex justify-content-between align-items-center mx-0 row"
                <"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"fr>
            >
            <"table-responsive"t>
            <"d-flex justify-content-between mx-0 row"
                <"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>
            >
        ')
            ->addAction(['printable' => false, 'exportable' => false, 'className' => 'dt-center', 'title' => trans('Actions')])
            ->orderBy(1)
            ->selectStyleSingle()
            ->buttons($buttons)
            ->language($dataTableLanguageUrl)
            ->initComplete('function () {
                var dt_filter = this.api();
                $(`#regions-table thead tr`).clone(true).appendTo(`#regions-table thead`);
                $(`#regions-table thead tr:eq(1) th`).each(function (i) {
                    var column = dt_filter.column(i);
                    var title = $(this).text();
                    if (column.settings()[0].aoColumns[i].bSearchable) {
                        $(this).html(\'<input type="text" class="form-control form-control-sm" placeholder="\' + title + \'" />\');
                        $(\'input\', this).on(\'keyup change\', function () {
                            if (column.search() !== this.value) {
                                column.search(this.value).draw();
                            }
                        });
                    } else {
                        $(this).html(\'\'); // Clear the header for non-searchable columns
                    }
                });
                // Disable sorting on the second header row for searchable columns
                $(`#regions-table thead tr:eq(1) th`).unbind("click");
            }')
            ->parameters([
                'stateSave' => true,
                'rowReorder' => [
                    'selector' => 'tr>td:not(:last-child)', // I allow all columns for dragdrop except the last
                    'dataSrc' => 'sortsequence',
                    'update' => false, // this is key to prevent DT auto update
                ],
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->addClass('text-center'),
            Column::make('name')->title(trans('Name'))->addClass('text-center'),
            Column::make('created_at')->title(trans('CreatedAt'))->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Role_' . date('YmdHis');
    }
}
