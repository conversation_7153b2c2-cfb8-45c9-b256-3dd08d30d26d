<?php

namespace App\DataTables;

use App\Models\Region;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class RegionDataTable extends BaseDataTable
{
    protected string $modelClass = Region::class;
    protected string $tableName = 'regions';
    protected array $relationships = [];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Name')),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [
            'text' => __('amjad'),
        ];
    }
}
