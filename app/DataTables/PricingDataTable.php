<?php

namespace App\DataTables;

use App\Models\Pricing;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class PricingDataTable extends BaseDataTable
{
    protected string $modelClass = Pricing::class;
    protected string $tableName = 'pricings';
    protected array $relationships = ['shift','carType'];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('shift_name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Shift Name')),
            Column::make('car_type_name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Car Type')),
            Column::make('minimum_fare')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Minimum Fare')),
            Column::make('km_price')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Km Price')),
            Column::make('miu_price')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('MiuPrice')),
            // Column::make('flag_down_fee')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('flag_down_fee')),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        if (request('carType_id')) {
            $query->where('carType_id', request('carType_id'));
        }

        if (request('shift_id')) {
            $query->where('shift_id', request('shift_id'));
        }

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }
}
