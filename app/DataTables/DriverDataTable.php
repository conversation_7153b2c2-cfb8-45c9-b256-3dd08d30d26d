<?php

namespace App\DataTables;

use App\Models\Driver;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\Html\Column;

class DriverDataTable extends BaseDataTable
{
    protected string $modelClass = Driver::class;
    protected string $tableName = 'drivers';
    protected array $relationships = ['user'];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->title(__('Id')),
            Column::make('user.full_name')->searchable(false)->title(__('Name')),
            Column::make('user.phone')->title(__('Phone')),
            Column::make('is_available')->searchable(false)->title(__('Is Available')),
            Column::make('license_number')->title(__('License Number')),
            Column::make('status')->title(__('Status')),
            Column::make('refuse_reason')->title(__('Refuse Reason')),
            Column::make('created_at')->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        if (request('user_id')) {
            $query->where('user_id', request('user_id'));
        }

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)

            ->editColumn('is_available', function ($driver) {
                // Check the availability and return a Bootstrap badge
                $statusClass = $driver->is_available ? 'bg-success' : 'bg-secondary';
                $statusText = $driver->is_available ? 'Online' : 'Offline';


                return '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
            })->editColumn('status', function ($item) {

                return '<span class="badge bg-' . $item->status->color() . '">' . $item->status->label() . '</span>';
            })->editColumn('action', function ($item) {
                $route = "dashboard." . $this->tableName;
                $id = $item->id;
                $model = class_basename($this->modelClass);
                $user = auth()->user();
                $options = [
                    'edit' => ['status' => $user->can('update-driver')],
                    'show' => $user->can('list-drivers'),
                    'delete' => $user->can('delete-driver'),
                    'approve_driver' => $user->can('update-driver'),
                    'reject_driver' => $user->can('update-driver'),
                    'with_trashed' => 0,
                ];
                $html = view()->make('_partials.driver_actions', ['id' => $id, 'route' => $route, 'options' => $options, 'model' => $model, 'item' => $item])->render();

                return $html;
            })

            ->rawColumns(['is_available', 'status', 'action'], true);

        return $dataTable;
    }
}
