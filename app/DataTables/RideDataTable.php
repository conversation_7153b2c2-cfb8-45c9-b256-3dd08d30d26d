<?php

namespace App\DataTables;

use App\Models\Ride;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class RideDataTable extends BaseDataTable
{
    protected string $modelClass = Ride::class;
    protected string $tableName = 'rides';
    protected array $relationships = ['customer.user', 'driver.user', 'pricing.carType'];


    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)
            ->filterColumn('customer_info', function($query, $keyword) {
                $query->whereHas('customer.user', function($q) use ($keyword) {
                    $q->where('first_name', 'like', "%{$keyword}%")
                      ->orWhere('last_name', 'like', "%{$keyword}%")
                      ->orWhere('email', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            })
            ->filterColumn('driver_info', function($query, $keyword) {
                $query->whereHas('driver.user', function($q) use ($keyword) {
                    $q->where('first_name', 'like', "%{$keyword}%")
                      ->orWhere('last_name', 'like', "%{$keyword}%")
                      ->orWhere('email', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            })
           
            ->editColumn('status', function ($ride) {
                return '<span class="badge bg-' . $ride->status->color() . '">' . $ride->status->label() . '</span>';
            })
            ->addColumn('customer_info', function ($ride) {
                if (!$ride->customer || !$ride->customer->user) {
                    return '<span class="text-muted">'. __('Not available') .'</span>';
                }

                $user = $ride->customer->user;
                $html = '<div class="d-flex flex-column">';
                $html .= '<span class="fw-bold">' . $user->full_name . '</span>';
                $html .= '<span class="text-muted small">' . $user->phone . '</span>';
                $html .= '<span class="text-muted small">' . $user->email . '</span>';
                $html .= '</div>';

                return $html;
            })
            ->addColumn('driver_info', function ($ride) {
                if (!$ride->driver || !$ride->driver->user) {
                    return '<span class="text-muted">'. __('Not assigned') .'</span>';
                }

                $user = $ride->driver->user;
                $html = '<div class="d-flex flex-column">';
                $html .= '<span class="fw-bold">' . $user->full_name . '</span>';
                $html .= '<span class="text-muted small">' . $user->phone . '</span>';

                // Add driver status
                $statusClass = $ride->driver->is_available ? 'success' : 'danger';
                $statusText = $ride->driver->is_available ? __('Available') : __('Unavailable');
                $html .= '<span class="badge bg-' . $statusClass . ' small">' . $statusText . '</span>';

                $html .= '</div>';

                return $html;
            })
            ->addColumn('car_info', function ($ride) {
                if (!$ride->pricing || !$ride->pricing->carType) {
                    return '<span class="text-muted">'. __('Not available') .'</span>';
                }

                $carType = $ride->pricing->carType;
                $html = '<div class="d-flex flex-column">';
                $html .= '<span class="fw-bold">' . $carType->name . '</span>';
                $html .= '<span class="text-muted small">'. __('Range') .': ' . $carType->formatted_km_range . ' km</span>';
                $html .= '</div>';

                return $html;
            })
            ->addColumn('price_info', function ($ride) {
                $html = '<div class="d-flex flex-column">';

                if ($ride->final_price) {
                    $html .= '<span class="fw-bold text-success">' . number_format($ride->final_price, 0, '.', ',') . '</span>';
                } elseif ($ride->fare) {
                    $html .= '<span class="fw-bold">' . number_format($ride->fare, 0, '.', ',') . '</span>';
                } else {
                    $html .= '<span class="text-muted">'. __('Not calculated') .'</span>';
                }

                if ($ride->coupon) {
                    $html .= '<span class="badge bg-info small">' . __('Coupon') . ': ' . $ride->coupon->code . '</span>';
                }

                $html .= '</div>';

                return $html;
            })
            ->addColumn('distance_duration', function ($ride) {
                $html = '<div class="d-flex flex-column">';

                // Distance
                $html .= '<div class="mb-1">';
                $html .= '<i class="ti ti-ruler me-1"></i>';
                if ($ride->actual_distance) {
                    $html .= '<span class="fw-bold">' . number_format($ride->actual_distance, 1, '.', ',') . ' km</span>';
                } elseif ($ride->estimated_distance) {
                    $html .= '<span>' . number_format($ride->estimated_distance, 1, '.', ',') . ' km ('. __('Est.') .')</span>';
                } else {
                    $html .= '<span class="text-muted">'. __('Not available') .'</span>';
                }
                $html .= '</div>';

                // Duration
                $html .= '<div>';
                $html .= '<i class="ti ti-clock me-1"></i>';
                if ($ride->actual_duration) {
                    $minutes = round($ride->actual_duration / 60);
                    $html .= '<span class="fw-bold">' . $minutes . ' '. __('min') .'</span>';
                } elseif ($ride->estimated_duration) {
                    $minutes = round($ride->estimated_duration / 60);
                    $html .= '<span>' . $minutes . ' '. __('min') .' ('. __('Est.') .')</span>';
                } else {
                    $html .= '<span class="text-muted">'. __('Not available') .'</span>';
                }
                $html .= '</div>';

                $html .= '</div>';

                return $html;
            })
            ->rawColumns(['status', 'customer_info', 'driver_info', 'car_info', 'price_info', 'distance_duration', 'action']);

        return $dataTable;
    }

    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->title(__('ID'))->width(60)->searchable(false),
            Column::make('status')->title(__('Status'))->width(100),
            Column::make('customer_info')->title(__('Customer'))->width(200)->orderable(false)->searchable(true),
            Column::make('driver_info')->title(__('Driver'))->width(200)->orderable(false)->searchable(true),
            Column::make('car_info')->title(__('Car Type'))->width(150)->orderable(false)->searchable(false),
            Column::make('price_info')->title(__('Price'))->width(120)->orderable(false)->searchable(false),
            Column::make('distance_duration')->title(__('Distance & Duration'))->width(180)->orderable(false)->searchable(false),
            Column::make('type')->title(__('Type'))->width(100)->searchable(false),
            Column::make('driver_gender')->title(__('Driver Gender'))->width(120)->searchable(false),

            // Time-related columns
            Column::make('requested_at')->title(__('Requested At'))->width(150)->searchable(false),
            Column::make('scheduled_at')->title(__('Scheduled At'))->width(150)->searchable(false),
            Column::make('accepted_at')->title(__('Accepted At'))->width(150)->searchable(false),
            Column::make('started_at')->title(__('Started At'))->width(150)->searchable(false),
            Column::make('completed_at')->title(__('Completed At'))->width(150)->searchable(false),
            Column::make('canceled_at')->title(__('Canceled At'))->width(150)->searchable(false),

            // Additional details (hidden by default)
            Column::make('note')->title(__('Note'))->width(200)->searchable(false),
            Column::make('cancel_reason')->title(__('Cancel Reason'))->width(200)->searchable(false),
            Column::make('created_at')->title(__('Created At'))->width(150)->visible(false),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary
        if (request('coupon_id')){
            $query->where('coupon_id', request('coupon_id'));
        }

        if (request('pricing_id')){
            $query->where('pricing_id', request('pricing_id'));
        }

        if (request('driver_id')){
            $query->where('driver_id', request('driver_id'));
        }

        if (request('status')) {
            $query->where('status', request('status'));
        }

        // Apply global search if provided
        if ($search = request('search.value')) {
            $query->where(function($q) use ($search) {
                // Search in ride fields
                $q->where('id', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('driver_gender', 'like', "%{$search}%")
                  ->orWhere('note', 'like', "%{$search}%")
                  ->orWhere('cancel_reason', 'like', "%{$search}%")

                  // Search in customer fields
                  ->orWhereHas('customer.user', function($cq) use ($search) {
                      $cq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  })

                  // Search in driver fields
                  ->orWhereHas('driver.user', function($dq) use ($search) {
                      $dq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  })

                  // Search in car type fields
                  ->orWhereHas('pricing.carType', function($ctq) use ($search) {
                      $ctq->where('name', 'like', "%{$search}%")
                        ->orWhereHas('translations', function($tq) use ($search) {
                            $tq->where('name', 'like', "%{$search}%");
                        });
                  });
            });
        }

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    /**
     * Build the base query for the DataTable.
     * Override the parent method to add our specific relationships.
     *
     * @return QueryBuilder
     */
    public function query(): QueryBuilder
    {
        $model = new $this->modelClass;

        $query = $model->newQuery()
            ->with($this->relationships);

        return $this->applyFilters($query)
            ->select("{$this->tableName}.*")
            ->distinct();
    }
}
