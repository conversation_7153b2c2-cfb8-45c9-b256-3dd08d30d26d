<?php

namespace App\DataTables;

use App\Models\CarType;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class CarTypeDataTable extends BaseDataTable
{
    protected string $modelClass = CarType::class;
    protected string $tableName = 'car_types';
    protected array $relationships = ['translation'];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('km_range')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Km Range')),
            Column::make('translation.name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Name')),
            Column::make('is_available')->searchable(false)->title(__('Available')),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    /**
     * Build the DataTable with custom column rendering.
     *
     * @param  QueryBuilder $query
     * @return \Yajra\DataTables\EloquentDataTable
     */
    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)
            ->editColumn('km_range', function ($carType) {
                // Format km_range with one decimal place and comma as decimal separator
                return number_format($carType->km_range, 1, ',', '');
            })
            ->editColumn('is_available', function ($carType) {
                $checked = $carType->is_available ? 'checked' : '';
                return '
                <label class="switch switch-square">
                    <input type="checkbox"
                           id="is_available"
                           data-object-type="CarType"
                           data-id="' . $carType->id . '"
                           class="switch-input toggle-availability" ' . $checked . '>
                    <span class="switch-toggle-slider">
                        <span class="switch-on"></span>
                        <span class="switch-off"></span>
                    </span>
                </label>';
            })
            ->rawColumns(['is_available', 'action']);

        return $dataTable;
    }
}
