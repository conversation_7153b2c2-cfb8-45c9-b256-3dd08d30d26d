<?php

namespace App\DataTables;

use App\Models\Customer;
use App\Models\Driver;
use App\Models\Wallet;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Html\Column;

class WalletDataTable extends BaseDataTable
{
    protected string $modelClass = Wallet::class;
    protected string $tableName = 'wallets';
    protected array $relationships = ['walletable'];
    protected bool $includeCreateButton = false;
    protected array $options = ['delete' => false];



    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('balance')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Balance')),
            Column::make('is_active')->searchable(false)->title(__('Is Active')),
            Column::make('owner_name')->title(__('Owner Name'))->orderable(false),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Note: Global search for owner_name is handled in the dataTable method
        // We only need to handle basic wallet field searches here
        if ($search = request('search.value')) {
            $query->where(function ($q) use ($search) {
                // Search in wallet fields only
                $q->where('id', 'like', "%{$search}%")
                    ->orWhere('balance', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query);

        // Register a custom filter for owner_name
        $dataTable->filterColumn('owner_name', function ($query, $keyword) {
            $query->where(function ($q) use ($keyword) {
                // For Driver and Customer walletable types
                $q->whereHasMorph('walletable', [
                    \App\Models\Driver::class,
                    \App\Models\Customer::class
                ], function ($query) use ($keyword) {
                    $query->whereHas('user', function ($userQuery) use ($keyword) {
                        $userQuery->where('first_name', 'like', "%{$keyword}%")
                            ->orWhere('last_name', 'like', "%{$keyword}%")
                            ->orWhere(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', "%{$keyword}%");
                    });
                })
                    // For Company walletable type
                    ->orWhereHasMorph('walletable', [
                        \App\Models\Company::class
                    ], function ($query) use ($keyword) {
                        $query->whereHas('translations', function ($translationQuery) use ($keyword) {
                            $translationQuery->where('name', 'like', "%{$keyword}%");
                        });
                    });
            });
        });

        $dataTable
            ->editColumn('is_active', function ($wallet) {
                $checked = $wallet->is_active == true ? 'checked' : ' ';
                $url = url('/dashboard/change-object-visibility') . '?objectId=' . $wallet->id . '&objectType=Wallet&columnName=is_active';
                return '
        <label class="switch switch-square">
            <input onclick="$.ajax({
                    url: \'' . $url . '\',
                    type: \'PUT\',
                    headers: {
                        \'X-CSRF-TOKEN\': $(\'meta[name=&quot;csrf-token&quot;]\').attr(\'content\'),
                        Authorization: \'Bearer \' + $(\'meta[name=&quot;api-token&quot;]\').attr(\'content\')
                    },
                    success: function(response) {
                        console.log(\'Status updated successfully\');
                    },
                    error: function(xhr) {
                        console.error(\'Error updating status\');
                    }
                })"
                   id="active"
                   data-object-type="Wallet"
                   data-id="' . $wallet->id . '"
                   type="checkbox"
                   class="switch-input" ' . $checked . '>
            <span class="switch-toggle-slider">
                <span class="switch-on"></span>
                <span class="switch-off"></span>
            </span>
        </label>';
            })
            ->addColumn('owner_name', function ($wallet) {
                if (!$wallet->walletable) {
                    return __('Not available');
                }

                if ($wallet->walletable instanceof Driver || $wallet->walletable instanceof Customer) {
                    if ($wallet->walletable->user) {
                        return $wallet->walletable->user->fullName;
                    }
                    return __('Unknown');
                }

                // For Company type
                return $wallet->walletable->name ?? 'Alia Company';
            })
            ->rawColumns(['is_active', 'action']);

        return $dataTable;
    }
}
