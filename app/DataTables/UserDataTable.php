<?php

namespace App\DataTables;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Column;
use Illuminate\Support\Str;

class UserDataTable extends BaseDataTable
{
    protected string $modelClass = User::class;
    protected string $tableName = 'users';
    protected array $relationships = [];
    protected array $options = [
        'show' => true,
        'edit' => ['status' => true],
        'delete' => true,
    ];



    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('avatar')->title(__('Avatar')) // Add avatar column
                ->orderable(false)
                ->searchable(false)
                ->printable(false),
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('first_name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(true)->title(__('First Name')),
            Column::make('last_name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(true)->title(__('Last Name')),
            Column::make('email')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(true)->title(__('Email')),
            Column::make('phone')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(true)->title(__('Phone')),
            Column::make('is_active')->searchable(false)->title(__('Is Active')),
            Column::make('birthdate')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Birthdate')),
            Column::make('gender')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('gender')),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary
        return $query;
    }

    public function query(): QueryBuilder
    {
        info('update-user'); // Log the exact permission name we're checking for
        return parent::query()
            ->where('userable_type', Customer::class);
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)
            ->editColumn('avatar', function ($user) {
                $avatarUrl = $user->getFirstMediaUrl('profile') ? asset($user->getFirstMediaUrl('profile')) : asset('assets/img/default-avatar.png');
                return '<img src="' . $avatarUrl . '" class="rounded-circle" width="40" height="40">';
            })
            ->editColumn('gender', function ($user) {
                return __($user->gender->value);
            })
            ->editColumn('is_active', function ($user) {
                $checked = $user->is_active == true ? 'checked' : ' ';
                $url = url('/dashboard/change-object-visibility') . '?objectId=' . $user->id . '&objectType=user&columnName=is_active';
                return '
            <label class="switch switch-square">
                <input onclick="$.ajax({
                        url: \'' . $url . '\',
                        type: \'PUT\',
                        headers: {
                            \'X-CSRF-TOKEN\': $(\'meta[name=&quot;csrf-token&quot;]\').attr(\'content\'),
                            Authorization: \'Bearer \' + $(\'meta[name=&quot;api-token&quot;]\').attr(\'content\')
                        },
                        success: function(response) {
                            console.log(\'Status updated successfully\');
                        },
                        error: function(xhr) {
                            console.error(\'Error updating status\');
                        }
                    })"
                       id="active"
                       data-object-type="user"
                       data-id="' . $user->id . '"
                       type="checkbox"
                       class="switch-input" ' . $checked . '>
                <span class="switch-toggle-slider">
                    <span class="switch-on"></span>
                    <span class="switch-off"></span>
                </span>
            </label>';
            })

            ->rawColumns(['is_active', 'action', 'avatar']);

        return $dataTable;
    }
}
