<?php

namespace App\DataTables;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class ShiftDataTable extends BaseDataTable
{
    protected string $modelClass = Shift::class;
    protected string $tableName = 'shifts';
    protected array $relationships = ['translation'];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::make('translation.name')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Name')),
            Column::make('translation.notes')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Note')),
            Column::make('start_time')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Start Time')),
            Column::make('end_time')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('End Time')),
            // Column::make('minute_price')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Minute Price')),
            // Column::make('km_price')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Km Price')),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Created At')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }
}
