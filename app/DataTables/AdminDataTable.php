<?php

namespace App\DataTables;

use App\Models\Admin;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class AdminDataTable extends BaseDataTable
{
    protected string $modelClass = Admin::class;
    protected string $tableName = 'admins';
    protected array $relationships = ['user'];
    protected array $options = [
        'edit' => ['status' => false],
        'delete' => true,
    ];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('Id')),
            Column::computed('user.first_name')
            ->searchable(true)
            ->title(__('Name')),

            Column::make('user.phone')
                ->title(__('Phone')),

            Column::make('user.email')
                ->title(__('Email')),

            Column::make('user.gender')
                ->title(__('Gender'))
                ->searchable(false),
            Column::make('created_at')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('CreatedAt')),
        ];
    }

    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {
        // Add any specific filters for this model if necessary

        if (request('user_id'))
            $query->where('user_id', request('user_id'));

        // Exclude super admin users from the results
        return $query->whereHas('user', function($query) {
            $query->whereNot('email', '<EMAIL>');
        });
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }
}
