<?php

namespace App\DataTables;

use App\Models\Transaction;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\Html\Column;

class TransactionDataTable extends BaseDataTable
{
    protected string $modelClass = Transaction::class;
    protected string $tableName = 'transactions';
    protected array $relationships = ['senderWallet', 'receiverWallet', 'initiator'];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')
                ->title(__('Id'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false),

            Column::make('reference')

                ->title(__('Reference'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(true),

            Column::make('sender_name')
                ->title(__('Sender'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('receiver_name')
                ->title(__('Receiver'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('initiator_name')
                ->title(__('Initiator'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('amount')
                ->title(__('Amount'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false),

            Column::make('type')
                ->title(__('Type'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('status')
                ->title(__('Status'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('payment_method')
                ->title(__('Payment Method'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('coupon_type')
                ->title(__('Coupon Type'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('coupon_amount')
                ->title(__('Coupon Amount'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false)
                ->orderable(false),

            Column::make('created_at')
                ->title(__('created_at'))
                ->render('$.fn.dataTable.render.ellipsis(50)')
                ->searchable(false),

        ];
    }


    /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)
            ->editColumn('sender_name', function (Transaction $transaction) {
                if (!$transaction->senderWallet || !$transaction->senderWallet->walletable) {
                    return __('Not available');
                }

                $walletable = $transaction->senderWallet->walletable;

                // Check if walletable is a model with a user relationship (Customer or Driver)
                if (method_exists($walletable, 'user') && $walletable->user) {
                    return $walletable->user->fullName;
                }

                // For Company type which has name property directly
                if (property_exists($walletable, 'name') || isset($walletable->name)) {
                    return $walletable->name;
                }

                return __('Unknown');
            })
            ->editColumn('receiver_name', function (Transaction $transaction) {
                if (!$transaction->receiverWallet || !$transaction->receiverWallet->walletable) {
                    return __('Not available');
                }

                $walletable = $transaction->receiverWallet->walletable;

                // Check if walletable is a model with a user relationship (Customer or Driver)
                if (method_exists($walletable, 'user') && $walletable->user) {
                    return $walletable->user->fullName;
                }

                // For Company type which has name property directly
                if (property_exists($walletable, 'name') || isset($walletable->name)) {
                    return $walletable->name;
                }

                return __('Unknown');
            })
            ->editColumn('initiator_name', function (Transaction $transaction) {
                $initiator = $transaction->initiator;
                if (!$initiator) {
                    return __('Not available');
                }

                return match ($transaction->initiator_type) {
                    'App\Models\User' => $initiator->fullName ?? __('Unknown'),
                    'App\Models\Driver', 'App\Models\Admin', 'App\Models\Customer' =>
                        $initiator->user && $initiator->user->fullName ? $initiator->user->fullName : __('Unknown'),
                    'App\Models\Company' => $initiator->name ?? __('Unknown'),
                    default => __('Unknown')
                };
            })
            ->editColumn('status', function ($item) {
                return '<span class="badge bg-' . $item->status->color() . '">' . $item->status->label() . '</span>';
            })
            ->editColumn('type', function ($item) {
                return __($item->type->value);
            })
            ->editColumn('payment_method', function ($item) {
                return __($item->payment_method->value);
            })
            ->filterColumn('type', function ($query, $keyword) {
                $query->where('type', 'like', "%{$keyword}%");
            })
            ->filterColumn('status', function ($query, $keyword) {
                $query->where('status', 'like', "%{$keyword}%");
            });

        return $dataTable->rawColumns(['action','status']);
    }

}
