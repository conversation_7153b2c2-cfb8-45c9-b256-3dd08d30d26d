<?php
namespace App\DataTables;

use App\Models\Coupon;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\Html\Column;

class CouponDataTable extends BaseDataTable
{
    protected string $modelClass = Coupon::class;
    protected string $tableName = 'coupons';
    protected array $relationships = [];


    /**
     * Define the columns of the DataTable.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->width(60)->searchable(false)->title(__('ID')),
            Column::make('code')->searchable(true)->title(__('Code')),
            Column::make('usage_limit_per_user')->searchable(false)->title(__('Usage Limit Per User')),
            Column::make('start_at')->searchable(false)->title(__('Start Date')),
            Column::make('end_at')->searchable(false)->title(__('End Date')),
            Column::make('type')->searchable(true)->title(__('Type')),  
                
            Column::make('value')->searchable(false)->title(__('Value'))
                ,
            Column::make('created_at')->searchable(false)->title(__('Created At')),
            
        ];
    }

      /**
     * Apply any additional filters to the query.
     *
     * @param  QueryBuilder $query
     * @return QueryBuilder
     */
    protected function applyFilters(QueryBuilder $query): QueryBuilder
    {

        return $query;
    }

    /**
     * Define custom table buttons specific to this DataTable.
     * These will be merged with the default buttons in the BaseDataTable.
     *
     * @return array
     */
    public function getCustomTableButtons(): array
    {
        return [];
    }

    /**
     * Build the DataTable with custom column rendering.
     *
     * @param  QueryBuilder $query
     * @return \Yajra\DataTables\EloquentDataTable
     */
    public function dataTable(QueryBuilder $query): \Yajra\DataTables\EloquentDataTable
    {
        $dataTable = parent::dataTable($query)
            ->editColumn('type', function ($coupon) {
                return __($coupon->type->value);
            })
            ->rawColumns(['type', 'action']);

        return $dataTable;
    }

}
