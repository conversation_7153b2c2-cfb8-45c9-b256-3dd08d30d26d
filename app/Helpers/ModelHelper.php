<?php

namespace App\Helpers;

use App\Enums\DeleteableModelEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ModelHelper
{
    /**
     * Get a model object by its type and ID.
     *
     * @param string $objectType The type of the model (from DeleteableModelEnum)
     * @param int $objectId The ID of the model
     * @param bool $withTrashed Whether to include trashed models
     * @return Model The model object
     */
    public static function getModelObject(string $objectType, int $objectId, bool $withTrashed = false): Model
    {
        $modelClass = "App\\Models\\{$objectType}";
        
        if (!class_exists($modelClass)) {
            throw new \InvalidArgumentException("Model class {$modelClass} does not exist");
        }
        
        $query = $modelClass::query();
        
        if ($withTrashed && method_exists($modelClass, 'withTrashed')) {
            $query->withTrashed();
        }
        
        return $query->findOrFail($objectId);
    }
}
