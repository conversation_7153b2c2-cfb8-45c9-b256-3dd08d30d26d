<?php

namespace App\Helpers;

use App\Enums\DeleteableModelEnum;
use Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Helpers
{
  /**
   * @return mixed[]
   */
  public static function appClasses(): array
  {

    $data = config('custom.custom');


    // default data array
    $DefaultData = [
      'myLayout' => 'vertical',
      'myTheme' => 'theme-default',
      'myStyle' => 'light',
      'myRTLSupport' => true,
      'myRTLMode' => true,
      'hasCustomizer' => true,
      'showDropdownOnHover' => true,
      'displayCustomizer' => true,
      'contentLayout' => 'compact',
      'headerType' => 'fixed',
      'navbarType' => 'fixed',
      'menuFixed' => true,
      'menuCollapsed' => false,
      'footerFixed' => false,
      'customizerControls' => [
        'rtl',
      'style',
      'headerType',
      'contentLayout',
      'layoutCollapsed',
      'showDropdownOnHover',
      'layoutNavbarOptions',
      'themes',
      ],
      //   'defaultLanguage'=>'en',
    ];

    // if any key missing of array from custom.php file it will be merge and set a default value from dataDefault array and store in data variable
    $data = array_merge($DefaultData, $data);

    // All options available in the template
    $allOptions = [
      'myLayout' => ['vertical', 'horizontal', 'blank', 'front'],
      'menuCollapsed' => [true, false],
      'hasCustomizer' => [true, false],
      'showDropdownOnHover' => [true, false],
      'displayCustomizer' => [true, false],
      'contentLayout' => ['compact', 'wide'],
      'headerType' => ['fixed', 'static'],
      'navbarType' => ['fixed', 'static', 'hidden'],
      'myStyle' => ['light', 'dark', 'system'],
      'myTheme' => ['theme-default', 'theme-bordered', 'theme-semi-dark'],
      'myRTLSupport' => [true, false],
      'myRTLMode' => [true, false],
      'menuFixed' => [true, false],
      'footerFixed' => [true, false],
      'customizerControls' => [],
      // 'defaultLanguage'=>array('en'=>'en','fr'=>'fr','de'=>'de','ar'=>'ar'),
    ];

    //if myLayout value empty or not match with default options in custom.php config file then set a default value
    foreach ($allOptions as $key => $value) {
      if (array_key_exists($key, $DefaultData)) {
        if (gettype($DefaultData[$key]) === gettype($data[$key])) {
          // data key should be string
          if (is_string($data[$key])) {
            // data key should not be empty
            if (isset($data[$key]) && $data[$key] !== null) {
              // data key should not be exist inside allOptions array's sub array
              if (!array_key_exists($data[$key], $value)) {
                // ensure that passed value should be match with any of allOptions array value
                $result = array_search($data[$key], $value, 'strict');
                if (empty($result) && $result !== 0) {
                  $data[$key] = $DefaultData[$key];
                }
              }
            } else {
              // if data key not set or
              $data[$key] = $DefaultData[$key];
            }
          }
        } else {
          $data[$key] = $DefaultData[$key];
        }
      }
    }
    $styleVal = $data['myStyle'] == "dark" ? "dark" : "light";
    if(isset($_COOKIE['mode'])){
      if($_COOKIE['mode'] === "system"){
        if(isset($_COOKIE['colorPref'])) {
          $styleVal = Str::lower($_COOKIE['colorPref']);
        }
      }
      else {
        $styleVal = $_COOKIE['mode'];
      }
    }
    isset($_COOKIE['theme']) ? $themeVal = $_COOKIE['theme'] : $themeVal = $data['myTheme'];
    //layout classes
    $layoutClasses = [
      'layout' => $data['myLayout'],
      'theme' => $themeVal,
      'themeOpt' => $data['myTheme'],
      'style' => $styleVal,
      'styleOpt' => $data['myStyle'],
      'rtlSupport' => $data['myRTLSupport'],
      'rtlMode' => $data['myRTLMode'],
      'textDirection' => $data['myRTLMode'],
      'menuCollapsed' => $data['menuCollapsed'],
      'hasCustomizer' => $data['hasCustomizer'],
      'showDropdownOnHover' => $data['showDropdownOnHover'],
      'displayCustomizer' => $data['displayCustomizer'],
      'contentLayout' => $data['contentLayout'],
      'headerType' => $data['headerType'],
      'navbarType' => $data['navbarType'],
      'menuFixed' => $data['menuFixed'],
      'footerFixed' => $data['footerFixed'],
      'customizerControls' => $data['customizerControls'],
    ];

    // sidebar Collapsed
    if ($layoutClasses['menuCollapsed'] == true) {
      $layoutClasses['menuCollapsed'] = 'layout-menu-collapsed';
    }

    // Header Type
    if ($layoutClasses['headerType'] == 'fixed') {
      $layoutClasses['headerType'] = 'layout-menu-fixed';
    }
    // Navbar Type
    if ($layoutClasses['navbarType'] == 'fixed') {
      $layoutClasses['navbarType'] = 'layout-navbar-fixed';
    } elseif ($layoutClasses['navbarType'] == 'static') {
      $layoutClasses['navbarType'] = '';
    } else {
      $layoutClasses['navbarType'] = 'layout-navbar-hidden';
    }

    // Menu Fixed
    if ($layoutClasses['menuFixed'] == true) {
      $layoutClasses['menuFixed'] = 'layout-menu-fixed';
    }


    // Footer Fixed
    if ($layoutClasses['footerFixed'] == true) {
      $layoutClasses['footerFixed'] = 'layout-footer-fixed';
    }

    // RTL Supported template
    if ($layoutClasses['rtlSupport'] == true) {
      $layoutClasses['rtlSupport'] = '/rtl';
    }

    // RTL Layout/Mode
    if ($layoutClasses['rtlMode'] == true) {
      $layoutClasses['rtlMode'] = 'rtl';
      $layoutClasses['textDirection'] = 'rtl';
    } else {
      $layoutClasses['rtlMode'] = 'ltr';
      $layoutClasses['textDirection'] = 'ltr';
    }

    // Show DropdownOnHover for Horizontal Menu
    $layoutClasses['showDropdownOnHover'] = $layoutClasses['showDropdownOnHover'] == true;

    // To hide/show display customizer UI, not js
    $layoutClasses['displayCustomizer'] = $layoutClasses['displayCustomizer'] == true;

    return $layoutClasses;
  }

  public static function updatePageConfig($pageConfigs)
  {
    $demo = 'custom';
    if (isset($pageConfigs) && count($pageConfigs) > 0) {
      foreach ($pageConfigs as $config => $val) {
        Config::set('custom.' . $demo . '.' . $config, $val);
      }
    }
  }


  public static function getColumnInputType($type): string
  {
    switch($type)
    {
      case 'int': return 'number'; break;
      case 'smallint': return 'number'; break;
      case 'mediumint': return 'number'; break;
      case 'bigint': return 'number'; break;
      case 'decimal': return 'number'; break;
      case 'float': return 'number'; break;
      case 'double': return 'number'; break;
      case 'real': return 'number'; break;
      case 'bit': return 'number'; break;
      case 'char': return 'text'; break;
      case 'varchar': return 'text'; break;
      case 'tinytext': return 'text'; break;
      case 'text': return 'textarea'; break;
      case 'mediumtext': return 'textarea'; break;
      case 'longtext': return 'textarea'; break;
      case 'date': return 'date'; break;
      case 'year': return 'date'; break;
      case 'time': return 'date'; break;
      case 'datetime': return 'datetime'; break;
      case 'timestamp': return 'datetime'; break;
      case 'boolean': return 'checkbox'; break;
      case 'tinyint': return 'checkbox'; break;
      case 'enum': return 'select'; break;
      default: return 'textarea'; break;
    }
  }

  /**
   * @return array{name: non-falsy-string, value: non-falsy-string}[]
   */
  public static function getEnumValues($table, $column): array
  {
    $type = DB::select( "SHOW COLUMNS FROM $table WHERE Field = '$column'" )[0]->Type;
    preg_match('/^enum\((.*)\)$/', $type, $matches);
    $options = array();
    foreach( explode(',', $matches[1]) as $value )
    {
      $v = trim( $value, "'" );
      if ($v !== '' && $v !== '0') {
          $options[] = $v;
      }
    }
    $res = [];
    foreach($options as $option)
    {
      $res[] = [
        'name' => $option,
        'value' => $option
      ];
    }
    return $res;
  }

  public static function getResourceNameFromUserableType($userableType)
  {
    $parts = explode('\\', $userableType);
    $modelName = end($parts);
    return strtolower($modelName);
  }

  /**
   * Get a model object by its type and ID.
   *
   * @param string $objectType The type of the model (from DeleteableModelEnum)
   * @param int $objectId The ID of the model
   * @param bool $withTrashed Whether to include trashed models
   * @return Model The model object
   */
  public static function getModelObject(string $objectType, int $objectId, bool $withTrashed = false): Model
  {
      $modelClass = "App\\Models\\{$objectType}";

      if (!class_exists($modelClass)) {
          throw new \InvalidArgumentException("Model class {$modelClass} does not exist");
      }

      $query = $modelClass::query();

      if ($withTrashed && method_exists($modelClass, 'withTrashed')) {
          $query->withTrashed();
      }

      return $query->findOrFail($objectId);
  }
}