<?php


function upload_media_file($object, $file, $collection, $replace = false)
{
    if ($replace)
        $object->clearMediaCollection($collection);
    $object->addMedia($file)->toMediaCollection($collection);
}

function update_madia_file($object, $file, $collection, $oldCollection)
{
    $object->clearMediaCollection($oldCollection);
    upload_media_file($object, $file, $collection);
}

function upload_multi($object, $files, $collection, $replace = false)
{
    if ($replace)
        $object->clearMediaCollection($collection);
    foreach ($files as $file) {
        upload_media_file($object, $file, $collection);
    }
}



function getResourceNameFromUserableType($userableType)
{
    $parts = explode('\\', $userableType);
    $modelName = end($parts);
    return strtolower($modelName);
}