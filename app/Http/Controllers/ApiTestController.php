<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApiTestController extends Controller
{
    /**
     * Show the API test page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('api-test.index');
    }

    /**
     * Show the profile page.
     *
     * @return \Illuminate\View\View
     */
    public function profile()
    {
        $user = Auth::user();
        return view('api-test.profile', compact('user'));
    }
}
