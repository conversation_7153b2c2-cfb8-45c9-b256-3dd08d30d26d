<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StatisticController extends Controller
{
    public function index()
    {
        $modelFiles = File::allFiles(app_path('Models'));
        $statisticsCount = [];
        foreach ($modelFiles as $file) {
            $model = 'App\\Models\\' . pathinfo($file->getFilename(), PATHINFO_FILENAME);
            if (strpos($model, 'Translation') !== false) {
                continue;
            }
            $usesSoftDeletes = in_array(SoftDeletes::class, class_uses($model));
            $statisticsCount[] = [
                'title' => class_basename($model),
                'count' => $usesSoftDeletes ? $model::withTrashed()->count() : $model::count()
            ];
        }
        return view('dashboard.statistics.dashboard-analytics', ['statisticsCount' => $statisticsCount]);
    }
}
