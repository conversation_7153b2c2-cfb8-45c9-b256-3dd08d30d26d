<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePricingRequest;
use App\Models\Pricing;
use App\Http\Resources\PricingResource;
use App\Http\Resources\PricingCollection;
use App\Services\PricingService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\PricingDataTable;
use App\Models\CarType;
use App\Models\CarTypeTranslation;
use App\Models\Shift;
use App\Models\ShiftTranslation;

class PricingController extends Controller
{

    public function __construct(private PricingService $pricingService)
    {

        view()->share([]);
    }

    public function index(PricingDataTable $dataTable)
    {

        $carTypes = CarType::with('translations')->get();
        $carTypes_values = $carTypes->pluck('id');
        $carTypes_names = $carTypes->map(fn($ct) => $ct->translations->pluck('name')->join(', '));

        $shifts = Shift::with('translations')->get();
        $shifts_values = $shifts->pluck('id');
        $shifts_names = $shifts->map(fn($s) => $s->translations->pluck('name')->join(', '));

        return $dataTable->render(
            "dashboard.pricings.index",
            [
                'carTypes_values' => $carTypes_values,
                'carTypes_names' => $carTypes_names,
                'shifts_values' => $shifts_values,
                'shifts_names' => $shifts_names
            ]
        );
    }

    public function show(Pricing $pricing)
    {
        $pricing->load(['carType', 'shift']);
        
        return view("dashboard.pricings.show",  [
            'pricing' => $pricing,
           
        ]);
    }

    public function create()
    {
        return view("dashboard.pricings.create", 
            [
                'pricing' => new Pricing(),
                'carTypes_values' => $this->getFormData()['carTypes']['values'],
                'carTypes_names' =>$this->getFormData()['carTypes']['names'],
                'shifts_values' => $this->getFormData()['shifts']['values'],
                'shifts_names' => $this->getFormData()['shifts']['names']
            ]
        );
    }

    public function edit(Pricing $pricing)
    {
        // Load relationships for editing
        $pricing->load(['carType', 'shift']);
    
        return view("dashboard.pricings.edit", [
            'pricing' => $pricing,
            'carTypes_values' => $this->getFormData()['carTypes']['values'],
            'carTypes_names' =>$this->getFormData()['carTypes']['names'],
            'shifts_values' => $this->getFormData()['shifts']['values'],
            'shifts_names' => $this->getFormData()['shifts']['names']
        ]);
    }

    public function store(StorePricingRequest $request)
    {
        $pricing = $this->pricingService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.pricings.index');
    }

    public function destroy($id)
    {
        $this->pricingService->destroy($id);
        return redirect()->route('dashboard.pricings.index');
    }

    protected function getFormData(): array
    {
        return [
            'carTypes' => $this->pricingService->getCarTypesForDropdown(),
            'shifts' => $this->pricingService->getShiftsForDropdown(),
            'polymorphicTypes' => (new Pricing())->getPolymorphicTypes()
        ];
    }
}
