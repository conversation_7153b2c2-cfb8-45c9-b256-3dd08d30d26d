<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Region;
use App\Http\Resources\RegionResource;
use App\Http\Resources\RegionCollection;
use App\Services\RegionService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\RegionDataTable;
use App\Http\Requests\StoreRegionRequest;

class RegionController extends Controller
{

    public function __construct(private RegionService $regionService)
    {
        $this->middleware('permission:list-regions')->only(['index', 'show']);
        $this->middleware('permission:create-region')->only(['create', 'store']);
        $this->middleware('permission:update-region')->only(['edit', 'update']);
        $this->middleware('permission:delete-region')->only(['destroy']);

        view()->share([]);
    }

    public function index(RegionDataTable $dataTable)
    {

        return $dataTable->render("dashboard.regions.index");
    }

    public function show(Region $region)
    {
        return view("dashboard.regions.show", ['region' => $region]);
    }

    public function create()
    {
        $region = new Region();
        return view("dashboard.regions.create", ['region' => $region])
        ->with("polymorphicTypes", $region->getPolymorphicTypes());
    }

    public function edit(Region $region)
    {
        $region = $this->regionService->show($region);
        return view("dashboard.regions.edit", ['region' => $region])
        ->with("polymorphicTypes", $region->getPolymorphicTypes());;
    }

    public function store(StoreRegionRequest $request)
    {
        $region = $this->regionService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.regions.index');
    }
    public function update(StoreRegionRequest $request)
    {
        $region = $this->regionService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.regions.index');
    }

    public function destroy($id)
    {

        $this->regionService->destroy($id);
        return redirect()->route('dashboard.regions.index');
    }

}
