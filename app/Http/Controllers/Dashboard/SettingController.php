<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\SettingService;
use App\Http\Requests\UpdateSettingRequest;

class SettingController extends Controller
{
    public function __construct(private SettingService $settingService){}
    public function edit()
    {
        $excptedKeys = [
        
        ];
        $settings = Setting::query()->get();
        return view('dashboard.settings.edit', compact('settings', 'excptedKeys'));
    }

    public function update(UpdateSettingRequest $request)
    {
        $this->settingService->update($request->validated());
        return redirect()->route('dashboard.settings.edit')->with(['success' => trans('Done Successfully')]);
    }


}
