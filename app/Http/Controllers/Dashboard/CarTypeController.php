<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCarTypeRequest;
use App\Models\CarType;
use App\Http\Resources\CarTypeResource;
use App\Http\Resources\CarTypeCollection;
use App\Services\CarTypeService;

use App\DataTables\CarTypeDataTable;

class CarTypeController extends Controller
{

    public function __construct(private CarTypeService $carTypeService)
    {
        $this->middleware('permission:list-car_types')->only(['index', 'show']);
        $this->middleware('permission:create-car_type')->only(['create', 'store']);
        $this->middleware('permission:update-car_type')->only(['edit', 'update']);
        $this->middleware('permission:delete-car_type')->only(['destroy']);

        view()->share([]);
    }

    public function index(CarTypeDataTable $dataTable)
    {

        return $dataTable->render("dashboard.car-types.index");
    }

    public function show(CarType $carType)
    {
        // The show method now handles both IDs and model instances
        return view("dashboard.car-types.show", ['cartype' => $carType]);
    }

    public function create()
    {
        $carType = new CarType();
        return view("dashboard.car-types.create", ['carType' => $carType])
        ->with("polymorphicTypes", $carType->getPolymorphicTypes());
    }

    public function edit(CarType $carType)
    {
        return view("dashboard.car-types.edit", ['carType' => $carType])
        ->with("polymorphicTypes", $carType->getPolymorphicTypes());
    }

    public function store(StoreCarTypeRequest $request)
    {
        $carType = $this->carTypeService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.car_types.index')
            ->with('success', __('Car type created successfully'));
    }

    public function update(StoreCarTypeRequest $request, CarType $carType)
    {
        $data = $request->validated();
        $data['id'] = $carType->id;
        $this->carTypeService->storeOrUpdate($data);
        return redirect()->route('dashboard.car_types.index')
            ->with('success', __('Car type updated successfully'));
    }

    public function destroy($id)
    {
        $this->carTypeService->destroy($id);
        return redirect()->route('dashboard.car_types.index')
            ->with('success', __('Car type deleted successfully'));
    }
}
