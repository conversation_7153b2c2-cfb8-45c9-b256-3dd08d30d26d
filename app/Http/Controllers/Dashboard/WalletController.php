<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Wallet;
use App\Services\WalletService;
use App\DataTables\WalletDataTable;
use App\Http\Requests\StoreWalletRequest;

class WalletController extends Controller
{

    public function __construct(private WalletService $walletService)
    {

        view()->share([]);
    }

    public function index(WalletDataTable $dataTable)
    {
        return $dataTable->render("dashboard.wallets.index");
    }

    public function show(Wallet $wallet)
    {
        // Load the walletable relationship
        if ($wallet->walletable_type) {
            // Always load the basic walletable relationship
            $wallet->load('walletable');

            // Additionally load the user relationship for Driver or Customer
            $walletableType = class_basename($wallet->walletable_type);
            if ($walletableType == 'Driver' || $walletableType == 'Customer') {
                // Check if the relationship method exists before trying to load it
                if (method_exists($wallet->walletable, 'user')) {
                    $wallet->load('walletable.user');
                }
            }
        }

        return view("dashboard.wallets.show", compact("wallet"));
    }


    public function create()
    {
        $wallet = new Wallet();
        return view("dashboard.wallets.create", compact("wallet"))
            ->with("polymorphicTypes", $wallet->getPolymorphicTypes());
    }

    public function edit(Wallet $wallet)
    {
        // Load the walletable relationship
        if ($wallet->walletable_type) {
            // Always load the basic walletable relationship
            $wallet->load('walletable');

            // Additionally load the user relationship for Driver or Customer
            $walletableType = class_basename($wallet->walletable_type);
            if ($walletableType == 'Driver' || $walletableType == 'Customer') {
                // Check if the relationship method exists before trying to load it
                if (method_exists($wallet->walletable, 'user')) {
                    $wallet->load('walletable.user');
                }
            }
        }

        return view("dashboard.wallets.edit", compact("wallet"))
            ->with("polymorphicTypes", $wallet->getPolymorphicTypes());
    }

    public function store(StoreWalletRequest $request)
    {
        $wallet = $this->walletService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.wallets.index')->with('success', __('Wallet created successfully'));
    }

    public function update(StoreWalletRequest $request, Wallet $wallet)
    {
        $data = $request->validated();
        $data['id'] = $wallet->id;
        $this->walletService->storeOrUpdate($data);
        return redirect()->route('dashboard.wallets.index')->with('success', __('Wallet updated successfully'));
    }

    public function destroy($id)
    {
        $this->walletService->destroy($id);
        return redirect()->route('dashboard.wallets.index');
    }
}
