<?php

namespace App\Http\Controllers\Dashboard;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\VisibleObjectRequest;
use Illuminate\Support\Facades\Log;

class VisibleController extends Controller
{
    public function __invoke(VisibleObjectRequest $request)
    {
        // Get the validated data
        $validated = $request->validated();
        $objectType = $validated['objectType'];
        $objectId = $validated['objectId'];
        $column = $validated['columnName'];

        // Log the request for debugging
        \Illuminate\Support\Facades\Log::info('Change visibility request', [
            'objectType' => $objectType,
            'objectId' => $objectId,
            'column' => $column,
            'all_validated' => $validated
        ]);

        try {
            $object = Helpers::getModelObject($objectType, $objectId);

            // Log the object before update
            \Illuminate\Support\Facades\Log::info('Object before update', [
                'object' => $object->toArray(),
                'column_value' => $object[$column]
            ]);

            $object->update([
                $column => !$object[$column],
            ]);

            // Refresh the object to get the updated value
            $object->refresh();

            // Log the object after update
            \Illuminate\Support\Facades\Log::info('Object after update', [
                'object' => $object->toArray(),
                'column_value' => $object[$column]
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Done Successfully'),
                'data' => [
                    'new_value' => $object[$column]
                ]
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error updating visibility', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }

    }
}
