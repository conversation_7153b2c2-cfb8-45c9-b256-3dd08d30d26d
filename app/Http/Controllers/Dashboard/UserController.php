<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Models\User;
use App\Services\UserService;
use App\DataTables\UserDataTable;

class UserController extends Controller
{

    public function __construct(private UserService $userService)
    {

        view()->share([]);
    }

    public function index(UserDataTable $dataTable)
    {
        return $dataTable->render("dashboard.users.index");
    }

    public function show(User $user)
    {
        return view("dashboard.users.show", ['user' => $user]);
    }

    public function create()
    {
        $user = new User();
        return view("dashboard.users.create", ['user' => $user])
        ->with("polymorphicTypes", $user->getPolymorphicTypes());
    }

    public function edit(User $user)
    {
        return view("dashboard.users.edit", ['user' => $user])
            ->with("polymorphicTypes", $user->getPolymorphicTypes());
    }

    public function store(StoreUserRequest $request)
    {
        $user = $this->userService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.users.index')
            ->with('success', __('User created successfully'));
    }

    public function update(StoreUserRequest $request, User $user)
    {
        $data = $request->validated();
        $data['id'] = $user->id;
        $this->userService->storeOrUpdate($data);
        return redirect()->route('dashboard.users.index')
            ->with('success', __('User updated successfully'));
    }

    public function destroy($id)
    {
        $this->userService->destroy($id);
        return redirect()->route('dashboard.users.index')
            ->with('success', __('User deleted successfully'));
    }
}
