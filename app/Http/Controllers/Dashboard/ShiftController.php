<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreShiftRequest;
use App\Models\Shift;
use App\Http\Resources\ShiftResource;
use App\Http\Resources\ShiftCollection;
use App\Services\ShiftService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\ShiftDataTable;

class ShiftController extends Controller
{

    public function __construct(private ShiftService $shiftService)
    {
        
        view()->share([]);
    }

    public function index(ShiftDataTable $dataTable)
    {
       
        return $dataTable->render("dashboard.shifts.index");
    }

    public function show(Shift $shift)
    {
        return view("dashboard.shifts.show", ['shift' => $shift]);
    }

    public function create()
    {
        $shift = new Shift();
        return view("dashboard.shifts.create", ['shift' => $shift])
        ->with("polymorphicTypes", $shift->getPolymorphicTypes());
    }

    public function edit(Shift $shift)
    {
        return view("dashboard.shifts.edit", ['shift' => $shift])
        ->with("polymorphicTypes", $shift->getPolymorphicTypes());;
    }

    public function store(StoreShiftRequest $request)
    {
         $this->shiftService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.shifts.index');
    }

    public function destroy($id)
    {
        $this->shiftService->destroy($id);
        return redirect()->route('dashboard.shifts.index');
    }
}
