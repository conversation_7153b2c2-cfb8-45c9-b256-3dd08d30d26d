<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Services\TransactionService;
use App\DataTables\TransactionDataTable;
use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Http\Requests\StoreTransactionRequest;
use App\Http\Requests\UpdateTransactionRequest;
use App\Models\Coupon;
use App\Models\Ride;
use App\Models\Wallet;

class TransactionController extends Controller
{

    public function __construct(private TransactionService $transactionService)
    {

        view()->share([]);
    }

    public function index(TransactionDataTable $dataTable)
    {
        return $dataTable->render("dashboard.transactions.index");
    }

    public function show(Transaction $transaction)
    {
        // Load relationships
        $transaction->load([
            'senderWallet.walletable',
            'receiverWallet.walletable',
            'ride',
            'coupon'
        ]);

        // Load user relationships for Driver and Customer walletables
        if ($transaction->senderWallet && $transaction->senderWallet->walletable) {
            $senderType = class_basename($transaction->senderWallet->walletable_type);
            if ($senderType == 'Driver' || $senderType == 'Customer') {
                if (method_exists($transaction->senderWallet->walletable, 'user')) {
                    $transaction->load('senderWallet.walletable.user');
                }
            }
        }

        if ($transaction->receiverWallet && $transaction->receiverWallet->walletable) {
            $receiverType = class_basename($transaction->receiverWallet->walletable_type);
            if ($receiverType == 'Driver' || $receiverType == 'Customer') {
                if (method_exists($transaction->receiverWallet->walletable, 'user')) {
                    $transaction->load('receiverWallet.walletable.user');
                }
            }
        }

        return view("dashboard.transactions.show", compact("transaction"));
    }

    public function create()
    {
        $transaction = new Transaction();
        return view("dashboard.transactions.create", compact("transaction"))
        ->with([
            "polymorphicTypes" => $transaction->getPolymorphicTypes(),
            "wallets" => Wallet::all(),
            "rides" => Ride::all()->pluck('id', 'id'),
            'statusOptions' => TransactionStatusEnum::cases(),
            'typeOptions' => TransactionTypeEnum::cases(),
            'paymentMethodOptions' => TransactionPaymentMethodEnum::cases(),
            "coupons" => Coupon::all()->pluck('code', 'id')
        ]);
    }

    public function edit(Transaction $transaction)
    {
        // Load relationships
        $transaction->load([
            'senderWallet.walletable',
            'receiverWallet.walletable',
            'ride',
            'coupon'
        ]);

        // Load user relationships for Driver and Customer walletables
        if ($transaction->senderWallet && $transaction->senderWallet->walletable) {
            $senderType = class_basename($transaction->senderWallet->walletable_type);
            if ($senderType == 'Driver' || $senderType == 'Customer') {
                if (method_exists($transaction->senderWallet->walletable, 'user')) {
                    $transaction->load('senderWallet.walletable.user');
                }
            }
        }

        if ($transaction->receiverWallet && $transaction->receiverWallet->walletable) {
            $receiverType = class_basename($transaction->receiverWallet->walletable_type);
            if ($receiverType == 'Driver' || $receiverType == 'Customer') {
                if (method_exists($transaction->receiverWallet->walletable, 'user')) {
                    $transaction->load('receiverWallet.walletable.user');
                }
            }
        }

        return view("dashboard.transactions.edit", compact("transaction"))
            ->with("polymorphicTypes", $transaction->getPolymorphicTypes());
    }

    public function store(StoreTransactionRequest $request)
    {
        $this->transactionService->store($request->validated(), true);
        return redirect()->route('dashboard.transactions.index')->with('success', __('Transaction created successfully'));
    }

    public function update(UpdateTransactionRequest $request, Transaction $transaction)
    {
        $data = $request->validated();
        $data['id'] = $transaction->id;

        // Convert status to enum if provided
        if (isset($data['status'])) {
            $data['status'] = \App\Enums\TransactionStatusEnum::from($data['status']);
        }

        // We don't want to process the transaction again, just update the details
        $updatedTransaction = $this->transactionService->storeOrUpdate($data);

        // Log the transaction update
        \Illuminate\Support\Facades\Log::info('Transaction updated', [
            'transaction_id' => $transaction->id,
            'old_amount' => $transaction->amount,
            'new_amount' => $updatedTransaction->amount,
            'old_status' => $transaction->status->value,
            'new_status' => $updatedTransaction->status->value,
        ]);

        return redirect()->route('dashboard.transactions.index')->with('success', __('Transaction updated successfully'));
    }

    public function destroy($id)
    {
        $this->transactionService->destroy($id);
        return redirect()->route('dashboard.transactions.index')->with('success', __('Transaction deleted successfully'));
    }
}
