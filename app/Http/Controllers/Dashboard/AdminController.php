<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAdminRequest;
use App\Models\Admin;
use App\Http\Resources\AdminResource;
use App\Http\Resources\AdminCollection;
use App\Services\AdminService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\AdminDataTable;
use App\Models\Role;

class AdminController extends Controller
{

    public function __construct(private AdminService $adminService)
    {
        $this->middleware('permission:list-admins')->only(['index', 'show']);
        $this->middleware('permission:create-admin')->only(['create', 'store']);
        $this->middleware('permission:update-admin')->only(['edit', 'update']);
        $this->middleware('permission:delete-admin')->only(['destroy']);

        view()->share([]);
    }

    public function index(AdminDataTable $dataTable)
    {
        // $admins = $this->adminService->all();
        // return view("dashboard.admins.index", compact("admins"));
        return $dataTable->render("dashboard.admins.index");
    }

    public function show(Admin $admin)
    {
        // Check if this is a super admin
        if ($this->isSuperAdmin($admin)) {
            return redirect()->route('dashboard.admins.index')
                ->with('error', __('You cannot view super admin details'));
        }

        // Load the admin with its user and roles
        $admin->load('user.roles');
        return view("dashboard.admins.show", compact("admin"));
    }

    public function create()
    {
        $route = route('dashboard.admins.store');
        $title =  __('Create Admin');
        $admin = new Admin();
        $roles = Role::get();
        return view("dashboard.admins.create", compact("admin", 'route', 'title','roles'))
            ->with("polymorphicTypes", $admin->getPolymorphicTypes());
    }

    public function edit(Admin $admin)
    {
        // Check if this is a super admin
        if ($this->isSuperAdmin($admin)) {
            return redirect()->route('dashboard.admins.index')
                ->with('error', __('You cannot edit super admin details'));
        }

        $route = route('dashboard.admins.store');
        $title =  __('Edit Admin');
        $admin->load('user.roles');
        $roles = Role::get();
        return view("dashboard.admins.create", compact("admin", 'route', 'title','roles'))
            ->with("polymorphicTypes", $admin->getPolymorphicTypes());
    }

    public function store(StoreAdminRequest $request)
    {
        $this->adminService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.admins.index')->with('success', __('Admin created successfully'));
    }

    public function update(StoreAdminRequest $request, Admin $admin)
    {
        // Pass the validated data and admin object to the service
        $this->adminService->storeOrUpdate($request->validated(), $admin);
        return redirect()->route('dashboard.admins.index')->with('success', __('Admin updated successfully'));
    }

    public function destroy($id)
    {
        // Get the admin
        $admin = Admin::findOrFail($id);

        // Check if this is a super admin
        if ($this->isSuperAdmin($admin)) {
            return redirect()->route('dashboard.admins.index')
                ->with('error', __('You cannot delete super admin'));
        }

        $this->adminService->destroy($id);
        return redirect()->route('dashboard.admins.index')
            ->with('success', __('Admin deleted successfully'));
    }

    /**
     * Check if the admin is a super admin
     *
     * @param Admin $admin
     * @return bool
     */
    private function isSuperAdmin(Admin $admin): bool
    {
        // Check if the admin's email is the super admin email
        return $admin->user && (
            $admin->user->email === '<EMAIL>' ||
            in_array($admin->user->email, config('app.super_admin_emails', []))
        );
    }
}
