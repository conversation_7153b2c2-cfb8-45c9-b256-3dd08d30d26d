<?php

namespace App\Http\Controllers\dashboard;

use App\Http\Controllers\Controller;

class LanguageController extends Controller
{
    public function swap($locale)
    {
        $availLocale = config('app._availableLocale');
        if (in_array($locale, $availLocale)) {
            session()->put('locale', $locale);
            app()->setLocale($locale);
        }
        return redirect()->back();
    }
}
