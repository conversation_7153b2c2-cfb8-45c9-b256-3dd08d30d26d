<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\AdminLoginRequest;
use App\Services\AuthService;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function __construct(private AuthService $authService)
    {
        $this->middleware('auth:sanctum')->only(['logout']);
    }

    public function loginCover()
    {
        $pageConfigs = ['myLayout' => 'blank'];
        $route = route('dashboard.login-post');

        return view('dashboard.authentications.auth-login-cover', ['pageConfigs' => $pageConfigs, 'route' => $route]);
    }

    public function login(AdminLoginRequest $request)
    {

        $data = $request->validated();
        $result = $this->authService->loginAdmin($data);
        if ($result) {
            return redirect()->route('dashboard.statistics.index');
        }
        

        return redirect()->back()->withErrors(trans('Invalid email or password.'));
    }

    public function logout()
    {
        $this->authService->logout(true);

        return redirect()->route('dashboard.login');
    }

    public function showProfile()
    {
        $user = Auth::user();
        return view('dashboard.users.profile', ['user' => $user]);
    }
}
