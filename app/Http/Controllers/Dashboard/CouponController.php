<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Http\Resources\CouponResource;
use App\Http\Resources\CouponCollection;
use App\Services\CouponService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\CouponDataTable;
use App\Http\Requests\StoreCouponRequest;

class CouponController extends Controller
{

    public function __construct(private CouponService $couponService)
    {

        view()->share([]);
    }

    public function index(CouponDataTable $dataTable)
    {

        return $dataTable->render("dashboard.coupons.index");
    }

    public function show(Coupon $coupon)
    {
        return view("dashboard.coupons.show", compact("coupon"));
    }

    public function create()
    {
        $coupon = new Coupon();
        return view("dashboard.coupons.create", compact("coupon"))
        ->with("polymorphicTypes", $coupon->getPolymorphicTypes());
    }

    public function edit(Coupon $coupon)
    {
        return view("dashboard.coupons.edit", compact("coupon"))
        ->with("polymorphicTypes", $coupon->getPolymorphicTypes());;
    }

    public function store(StoreCouponRequest $request)
    {
        $coupon = $this->couponService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.coupons.index')->with('success', __('Coupon created successfully'));
    }

    public function update(StoreCouponRequest $request, Coupon $coupon)
    {
        $data = $request->validated();
        $data['id'] = $coupon->id;
        $this->couponService->storeOrUpdate($data);
        return redirect()->route('dashboard.coupons.index')->with('success', __('Coupon updated successfully'));
    }

    public function destroy($id)
    {
        $this->couponService->destroy($id);
        return redirect()->route('dashboard.coupons.index')->with('success', __('Coupon deleted successfully'));
    }
}
