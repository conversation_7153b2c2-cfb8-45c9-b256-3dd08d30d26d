<?php

namespace App\Http\Controllers\Dashboard;

use App\Enums\DeleteActionEnum;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\DeleteObjectRequest;

class DeleteController extends Controller
{
    public function __invoke(DeleteObjectRequest $request)
    {
        // Get the validated data from the request
        $validated = $request->validated();
        $objectType = $validated['objectType'];
        $objectId = $validated['objectId'];
        $actionType = $validated['actionType'];
        $withTrashed = $validated['withTrashed'] ?? true;

        // Log the request for debugging
        \Illuminate\Support\Facades\Log::info('Delete object request', [
            'objectType' => $objectType,
            'objectId' => $objectId,
            'actionType' => $actionType,
            'withTrashed' => $withTrashed
        ]);

        $object = Helpers::getModelObject($objectType, $objectId, withTrashed: $withTrashed);
        $flag = false;
        if ($actionType === DeleteActionEnum::SOFT_DELETE()->value) {
            $flag = $object->delete();
        }
        if ($actionType === DeleteActionEnum::RESTORE_DELETED()->value) {
            $flag =  $object->restore();
        }
        if ($actionType === DeleteActionEnum::FORCE_DELETE()->value) {
            $flag =  $object->forceDelete();
        }
        if ($flag === true) {
            // TODO
            return response()->json(['success' => true, 'message' => trans('Done Successfully')]);
        } else {
            return response()->json(['success' => false, 'message' => 'Resource not found'], 404);
        }
    }
}
