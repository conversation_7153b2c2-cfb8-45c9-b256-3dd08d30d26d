<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRideRequest;
use App\Models\Ride;
use App\Services\RideService;
use App\DataTables\RideDataTable;
use App\Models\Driver;
use App\Models\Pricing;

class RideController extends Controller
{

    public function __construct(private RideService $rideService)
    {
        $this->middleware('permission:list-rides')->only(['index', 'show']);
        $this->middleware('permission:create-ride')->only(['create', 'store']);
        $this->middleware('permission:update-ride')->only(['edit', 'update']);
        $this->middleware('permission:delete-ride')->only(['destroy']);

        view()->share([]);
    }

    public function index(RideDataTable $dataTable)
    {

        $pricings = Pricing::with('carType.translations')->get();
        $pricings_values = $pricings->pluck('id');
        $pricings_names = $pricings->map(fn($p) => $p->carType->translations->pluck('name')->join(', '));

        $drivers = Driver::with('user')->get();
        $drivers_values = $drivers->pluck('id');
        $drivers_names = $drivers->map(fn($d) => $d->user->fullName);
        return $dataTable->render("dashboard.rides.index", [

            'pricings_values' => $pricings_values,
            'pricings_names' => $pricings_names,
            'drivers_values' => $drivers_values,
            'drivers_names' => $drivers_names,
        ]);
    }

    public function show(Ride $ride)
    {
        $ride->load([
            'customer.user',
            'driver.vehicle',
            'driver.user',
            'customerRating',
            'coupon',
            'driverRating'
        ]);
        return view("dashboard.rides.show", compact("ride"));
    }

    public function create()
    {
        $ride = new Ride();
        return view("dashboard.rides.create", compact("ride"))
            ->with("polymorphicTypes", $ride->getPolymorphicTypes());
    }

    public function edit(Ride $ride)
    {
        // The show method now handles both IDs and model instances
        $ride = $this->rideService->show($ride);
        return view("dashboard.rides.edit", compact("ride"))
            ->with("polymorphicTypes", $ride->getPolymorphicTypes());
    }

    public function store(StoreRideRequest $request)
    {
        $ride = $this->rideService->storeOrUpdate($request->validated());
        return redirect()->route('dashboard.rides.index')
            ->with('success', __('Ride created successfully'));
    }

    public function update(StoreRideRequest $request, Ride $ride)
    {
        $data = $request->validated();
        $data['id'] = $ride->id;
        $this->rideService->storeOrUpdate($data);
        return redirect()->route('dashboard.rides.index')
            ->with('success', __('Ride updated successfully'));
    }

    public function destroy($id)
    {
        $this->rideService->destroy($id);
        return redirect()->route('dashboard.rides.index')
            ->with('success', __('Ride deleted successfully'));
    }
}
