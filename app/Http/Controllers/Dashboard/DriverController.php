<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDriverRequest;
use App\Models\Driver;
use App\Services\DriverService;
use App\DataTables\DriverDataTable;
use App\Enums\DriverStatusEnum;
use App\Http\Requests\rejectDriverRequest;

class DriverController extends Controller
{

    public function __construct(private DriverService $driverService)
    {
        $this->middleware('permission:list-drivers')->only(['index', 'show']);
        $this->middleware('permission:create-driver')->only(['create', 'store']);
        $this->middleware('permission:update-driver')->only(['edit', 'update']);
        $this->middleware('permission:delete-driver')->only(['destroy']);
        $this->middleware('permission:approve-driver')->only(['approveDriver', 'rejectDriver', 'showDriverRejectPage']);

        view()->share([]);
    }

    public function index(DriverDataTable $dataTable)
    {

        return $dataTable->render("dashboard.drivers.index");
    }

    public function show($id)
    {
        $driver = $this->driverService->show($id);
        $driver->with(['user', 'vehicle', 'carTypes', 'regions']);
        return view("dashboard.drivers.show", ['driver' => $driver]);
    }

    public function create()
    {
        $driver = new Driver();
        return view("dashboard.drivers.create", ['driver' => $driver])
        ->with("polymorphicTypes", $driver->getPolymorphicTypes());
    }

    public function edit(Driver $driver)
    {
        return view("dashboard.drivers.edit", ['driver' => $driver])
        ->with("polymorphicTypes", $driver->getPolymorphicTypes());;
    }

    public function store(StoreDriverRequest $request)
    {
        try {
            $data = $request->validated();

            $this->driverService->store($data);

            return redirect()->route('dashboard.drivers.index')
                ->with('success', __('Driver created successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('Failed to create driver: ') . $e->getMessage());
        }
    }

    public function update(StoreDriverRequest $request, Driver $driver)
    {
        try {
            $data = $request->validated();
            $data['id'] = $driver->id;

            // Update the driver using the driver service
            $this->driverService->updateDriver($driver, $data);

            return redirect()->route('dashboard.drivers.index')
                ->with('success', __('Driver updated successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('Failed to update driver: ') . $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $this->driverService->destroy($id);
        return redirect()->route('dashboard.drivers.index');
    }

    public function approveDriver($id)
    {
        $this->driverService->updateDriverStatus($id, DriverStatusEnum::APPROVED->value);
        return redirect()->route('dashboard.drivers.index')
            ->with('success', __('Driver approved successfully. Notification has been sent.'));
    }

    public function rejectDriver(rejectDriverRequest $request)
    {
        $this->driverService->updateDriverStatus(
           id: $request->validated()['id'],
           status: DriverStatusEnum::REFUSED->value,
           refuse_reason: $request->validated()['refuse_reason']
        );

        return redirect()->route('dashboard.drivers.index')
            ->with('success', __('Driver rejected successfully. Notification has been sent.'));
    }

    public function showDriverRejectPage($id)
    {
        $driver = $this->driverService->show($id);
        return view("dashboard.drivers.reject_page", ['driver' => $driver]);
    }

}
