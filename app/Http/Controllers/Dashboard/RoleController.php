<?php

namespace App\Http\Controllers\Dashboard;

use App\DataTables\RoleDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRoleRequest;
use App\Models\Permission;
use App\Models\Role;
use App\Services\RoleService;

class RoleController extends Controller
{
    public function __construct(private RoleService $roleService)
    {
        $this->middleware('permission:list-roles')->only(['index']);
        $this->middleware('permission:update-role')->only(['edit']);
        $this->middleware('permission:create-role')->only(['create']);
        $this->middleware('permission:create-role|update-role')->only(['store']);

    }

    public function index(RoleDataTable $roleDataTable)
    {
        return $roleDataTable->render('dashboard.roles.index');
    }

    public function create()
    {
        $role = null;
        $permissions = Permission::query()
            ->whereNotIn('group', ['manage-super-admins', 'manage-role', 'manage-admin'])
            ->get()
            ->groupBy('group');

        return view('dashboard.roles.create', ['role' => $role, 'permissions' => $permissions]);
    }

    public function edit(Role $role)
    {
        $permissions = Permission::query()
            ->whereNotIn('group', ['manage-super-admins'])
            ->get()
            ->groupBy('group');
        $rolePermissions = $role->permissionsIds();

        return view('dashboard.roles.create', ['role' => $role, 'permissions' => $permissions, 'rolePermissions' => $rolePermissions]);
    }

    public function store(StoreRoleRequest $request)
    {
        $permissionsAsIntegers = array_map('intval', $request->permissions ?? []);
        info($permissionsAsIntegers);
        $role = $this->roleService->storeOrUpdate($request->validated());
        $role->syncPermissions($permissionsAsIntegers);

        return redirect()->route('dashboard.roles.index')->with(['success' => trans('Done Successfully')]);
    }
}
