<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePricingRequest;
use App\Http\Resources\PricingResource;
use App\Services\PricingService;
use App\Models\Pricing;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PricingController extends Controller
{
    public function __construct(private PricingService $pricingService)
    {
        // Constructor code
    }

    public function index()
    {
        $pricings = $this->pricingService->index();
        return response()->success(PricingResource::collection($pricings)->response()->getData());
    }
    

    public function show($id)
    {
        $pricing = $this->pricingService->show($id);
        return response()->success(PricingResource::make($pricing));
    }

}
