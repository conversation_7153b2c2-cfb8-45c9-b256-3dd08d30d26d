<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Shift\StoreShiftRequest;
use App\Http\Requests\Api\Shift\UpdateShiftRequest;
use App\Http\Resources\ShiftResource;
use App\Services\ShiftService;
use App\Models\Shift;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ShiftController extends Controller
{
    public function __construct(private ShiftService $shiftService)
    {
        // Constructor code
    }

    public function index()
    {
        $shifts = $this->shiftService->index();
        return response()->success(ShiftResource::collection($shifts)->response()->getData());
    }


    public function show($id)
    {
        $shift = $this->shiftService->show($id);
        return response()->success(ShiftResource::make($shift));
    }

}
