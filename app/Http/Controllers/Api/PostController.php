<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Post\StorePostRequest;
use App\Http\Requests\Api\Post\UpdatePostRequest;
use App\Http\Resources\PostResource;
use App\Services\PostService;
use App\Models\Post;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PostController extends Controller
{
    public function __construct(private PostService $postService)
    {
        // Constructor code
    }

    public function index()
    {
        $posts = $this->postService->index();
        return response()->success(PostResource::collection($posts)->response()->getData());
    }

    public function store(StorePostRequest $request)
    {
        $post = $this->postService->store($request->validated());
        return response()->success(PostResource::make($post), 'Post created successfully');
    }

    public function update(UpdatePostRequest $request , Post $post )
    {
        $post = $this->postService->update($post,$request->validated());
        return response()->success(PostResource::make($post), 'Post created successfully');
    }

    public function show($id)
    {
        $post = $this->postService->show($id);
        return response()->success(PostResource::make($post));
    }

    public function destroy($id)
    {
        $this->postService->destroy($id);
        return response()->success(null, 'Post deleted successfully');
    }
}
