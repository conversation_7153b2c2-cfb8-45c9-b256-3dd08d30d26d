<?php

namespace App\Http\Controllers\Api;

use App\Helpers\APIResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Driver\GetLatestLocationRequest;
use App\Http\Requests\Api\Driver\GetLocationHistoryRequest;
use App\Http\Requests\Api\Driver\UpdateLocationRequest;
use App\Http\Resources\DriverLocationResource;
use App\Http\Resources\DriverResource;
use App\Services\DriverLocationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * @group  Driver Location 
 * APIs for  Driver Location Management
 *
 * @subgroup Driver Location Endpoints
 *
 * @subgroupDescription Endpoints for handling Driver Location, Update Driver Availability.
 */
class DriverLocationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param DriverLocationService $driverLocationService
     */
    public function __construct(protected DriverLocationService $driverLocationService) {}
    /**
     * Update the driver's current location
     *
     * @param UpdateLocationRequest $request
     * @return JsonResponse
     * @responseFile test-responses/DriverLocation/DriverLocation.json
     * 
     */
    public function updateLocation(UpdateLocationRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $driver = $user->userable;

            if (!$driver) {
                return response()->fail(__('Driver not found'), 400);
            }

            $driverLocation = $this->driverLocationService->updateLocation(
                $driver,
                $request->validated()
            );

            return response()->success(
                data: DriverLocationResource::make($driverLocation),
                message: __('Location updated successfully')
            );
        } catch (\Exception $e) {
            Log::error('Error updating driver location: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);

            return response()->fail(__('An error occurred while updating location', 400));
        }
    }

    /**
     * Get the driver's location history
     *
     * @param GetLocationHistoryRequest $request
     * @return JsonResponse
     */
    public function getLocationHistory(GetLocationHistoryRequest $request): JsonResponse
    {
        // Get the location history using the service
        $locations = $this->driverLocationService->getLocationHistory($request->validated());

        if ($locations) {
            return response()->success(
                data: DriverLocationResource::collection($locations),
                message: __('Location history retrieved successfully')
            );
        }

        return response()->fail(__('An error occurred while retrieving location history', 400));
    }

    /**
     * Get the latest location for a driver
     *
     * @param GetLatestLocationRequest $request
     * @return JsonResponse
     */
    public function getLatestLocation(GetLatestLocationRequest $request): JsonResponse
    {

        $location = $this->driverLocationService->getLatestLocation($request->validated()['driver_id']);

        if (!$location) {
            return response()->fail(__('No location found for this driver', 400));
        }

        return response()->success(
            data: DriverLocationResource::make($location),
            message: __('Latest location retrieved successfully')
        );
    }

    /**
     * Get the latest locations for all online drivers
     *
     * @return JsonResponse
     */
    // public function getAllOnlineDrivers(): JsonResponse
    // {
    //     try {
    //         // Get all drivers with their latest location using the service
    //         $drivers = $this->driverLocationService->getAllOnlineDrivers();

    //         return response()->success(
    //             data: DriverResource::collection($drivers),
    //             message: __('Online drivers retrieved successfully')
    //         );

    //     } catch (\Exception $e) {
    //         Log::error('Error retrieving online drivers: ' . $e->getMessage(), [
    //             'trace' => $e->getTraceAsString(),
    //             'user_id' => Auth::id()
    //         ]);

    //         return response()->fail(__('An error occurred while retrieving online drivers',400));

    //     }
    // }
}
