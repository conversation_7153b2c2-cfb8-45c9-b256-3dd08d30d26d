<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Category\StoreCategoryRequest;
use App\Http\Requests\Api\Category\UpdateCategoryRequest;
use App\Http\Resources\CategoryResource;
use App\Services\CategoryService;
use App\Models\Category;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CategoryController extends Controller
{
    public function __construct(private CategoryService $categoryService)
    {
        // Constructor code
    }

    public function index()
    {
        $categories = $this->categoryService->index();
        return response()->success(CategoryResource::collection($categories)->response()->getData());
    }

    public function store(StoreCategoryRequest $request)
    {
        $category = $this->categoryService->store($request->validated());
        return response()->success(CategoryResource::make($category), 'Category created successfully');
    }

    public function update(UpdateCategoryRequest $request , Category $category )
    {
        $category = $this->categoryService->update($category,$request->validated());
        return response()->success(CategoryResource::make($category), 'Category created successfully');
    }

    public function show($id)
    {
        $category = $this->categoryService->show($id);
        return response()->success(CategoryResource::make($category));
    }

    public function destroy($id)
    {
        $this->categoryService->destroy($id);
        return response()->success(null, 'Category deleted successfully');
    }
}
