<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UserAddress\StoreUserAddressRequest;
use App\Http\Requests\Api\UserAddress\UpdateUserAddressRequest;
use App\Http\Resources\UserAddressResource;
use App\Services\UserAddressService;
use App\Models\UserAddress;
use Illuminate\Support\Facades\Gate;

/**
 * @group User Address 
 * APIs for User Address
 *
 * @subgroup User Address Endpoints
 *
 * @subgroupDescription Endpoints for handling crud for User Address.
 */
class UserAddressController extends Controller
{
    public function __construct(private UserAddressService $userAddressService)
    {
        // Constructor code
    }

     /**
     * List User Addresses
     *
     * Get a paginated list of authenticated user's addresses
     *
     * @authenticated
     *
     * @responseFile test-responses/user-address/index.json
     */
    public function index()
    {
        $userAddresses = $this->userAddressService->index();
        return response()->success(UserAddressResource::collection($userAddresses)->response()->getData());
    }

    /**
     * Create User Address
     *
     * Store a new address for the authenticated user
     *
     * @authenticated
     * @queryParam filter[name] string Filter by addres name
     *
     * @responseFile status=200 test-responses/user-address/store.json
     */
    public function store(StoreUserAddressRequest $request)
    {
        $userAddress = $this->userAddressService->store($request->validated());
        return response()->success(UserAddressResource::make($userAddress), __('User Address created successfully'));
    }


    /**
     * Update User Address
     *
     * Update an existing address
     *
     * @authenticated
     * 
     * @responseFile status=200 test-responses/user-address/update.json
     *
     */
    public function update(UpdateUserAddressRequest $request , UserAddress $userAddress )
    {
        if (! Gate::allows('update', $userAddress)) {
            abort(403);
        }
        $userAddress = $this->userAddressService->update($userAddress,$request->validated());
        return response()->success(UserAddressResource::make($userAddress), __('User Address created successfully'));
    }


    /**
     * Show User Address
     *
     * Display details of a specific address
     *
     * @authenticated
     * @responseFile status=200 test-responses/user-address/show.json
     */
    public function show(UserAddress $user_address)
    {
        if (! Gate::allows('view', $user_address)) {
            abort(403);
        }
        return response()->success(UserAddressResource::make($user_address));
    }


    /**
     * Delete User Address
     *
     * Remove an existing address
     *
     * @authenticated
     *
     * @responseFile status=200 test-responses/user-address/destroy.json
   
     */
    public function destroy(UserAddress $user_address)
    {
        if (! Gate::allows('delete', $user_address)) {
            abort(403);
        }
        $user_address->delete();
        return response()->success(null, __('User Address deleted successfully'));
    }
}
