<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\WalletResource;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\Wallet;
use App\Services\WalletService;
use Illuminate\Http\JsonResponse;


/**
 * @group Wallet
 *
 * APIs for Wallet Management
 *
 *
 * @subgroupDescription Endpoints for managing driver's financial transactions and balance
 */
class WalletController extends Controller
{
    public function __construct(private WalletService $walletService)
    {
        // Constructor code
    }

    public function index()
    {
        $wallets = $this->walletService->index();
        return response()->success(WalletResource::collection($wallets)->response()->getData());
    }



    public function show($id)
    {
        $wallet = $this->walletService->show($id);
        return response()->success(WalletResource::make($wallet));
    }

    public function destroy($id)
    {
        $this->walletService->destroy($id);
        return response()->success(null, 'Wallet deleted successfully');
    }

    /**
     * Get Driver Wallet
     *
     * Retrieve the authenticated driver's wallet information
     *
     * @responseFile test-responses/Driver/wallet.json
     */
    public function getWallet(): JsonResponse
    {
        $driver = auth()->user()->userable;
        $wallet = $this->walletService->getDriverWallet($driver);

        return response()->success(
            WalletResource::make($wallet),
            __('Wallet retrieved successfully')
        );
    }

    public function getByType($type)
    {
        $modelClass = "App\\Models\\" . $type;

        $wallets = Wallet::with('walletable')
            ->where('walletable_type', $modelClass)
            ->get()
            ->map(function ($wallet) {
                $walletName = 'Unnamed Wallet';

                if ($wallet->walletable) {
                    if (($wallet->walletable_type == Driver::class || $wallet->walletable_type == Customer::class)
                        && method_exists($wallet->walletable, 'user')
                        && $wallet->walletable->user) {
                        $walletName = $wallet->walletable->user->fullName ?? 'ID: ' . $wallet->walletable->id;
                    } elseif ($wallet->walletable_type == "App\\Models\\Company" && isset($wallet->walletable->name)) {
                        $walletName = $wallet->walletable->name;
                    } else {
                        // Fallback for any other type
                        $walletName = $wallet->walletable->name ?? $wallet->walletable->title ?? 'ID: ' . $wallet->walletable->id;
                    }
                }

                return [
                    'id' => $wallet->id,
                    'balance' => $wallet->balance,
                    'name' => $walletName
                ];
            });

        return response()->json($wallets);
    }
}
