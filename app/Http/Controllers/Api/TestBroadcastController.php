<?php

namespace App\Http\Controllers\Api;

use App\Events\DriverLocationUpdated;
use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\DriverLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TestBroadcastController extends Controller
{
    /**
     * Test broadcasting to a public channel
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testPublicBroadcast(Request $request)
    {
        try {
            // Create a test driver location
            $driverId = $request->input('driver_id', 1);
            $driver = Driver::find($driverId);
            
            if (!$driver) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Driver not found'
                ], 404);
            }
            
            $location = new DriverLocation([
                'driver_id' => $driver->id,
                'latitude' => $request->input('latitude', 33.5123),
                'longitude' => $request->input('longitude', 36.3123),
                'heading' => $request->input('heading', 90),
                'is_online' => true,
                'address' => 'Test Location',
            ]);
            
            // Save the location to the database
            $location->save();
            
            // Log before broadcasting
            Log::info('About to broadcast test event', [
                'driver_id' => $driver->id,
                'location_id' => $location->id
            ]);
            
            // Broadcast the event
            event(new DriverLocationUpdated($location));
            
            // Log after broadcasting
            Log::info('Test event broadcasted successfully');
            
            return response()->json([
                'status' => 'success',
                'message' => 'Test event broadcasted successfully',
                'data' => [
                    'driver_id' => $driver->id,
                    'location' => $location,
                    'channels' => [
                        'private-driver.' . $driver->id,
                        'test-driver-location'
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error broadcasting test event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Error broadcasting test event: ' . $e->getMessage()
            ], 500);
        }
    }
}
