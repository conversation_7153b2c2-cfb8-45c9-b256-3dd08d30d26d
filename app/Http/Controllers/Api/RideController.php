<?php

namespace App\Http\Controllers\Api;

use App\Enums\RideStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ApplyCouponRequest;
use App\Http\Requests\Api\Area\AreaSearchRequest;
use App\Http\Requests\Api\Driver\CancelRideRequest;
use App\Http\Requests\Api\Ride\RequestRideRequest;
use App\Http\Requests\Api\Ride\StoreRideRequest;
use App\Http\Resources\AreaSuggestionResource;
use App\Http\Resources\RideResource;
use App\Models\Ride;
use App\Services\GoogleMapsService;
use App\Services\RideService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @group Ride
 * APIs for Ride Management
 *
 * @subgroup Ride Endpoints
 *
 * @subgroupDescription Endpoints for handling request a ride, confirm a ride request.
 */
class RideController extends Controller
{

    public function __construct(
        private RideService $rideService
    ) {}


    /**
     * List Rides
     *
     * This endpoint returns a paginated list of rides based on the authenticated user's role:
     * - For **customers**: Returns rides created by the customer.
     * - For **drivers**: Returns rides assigned to the driver.
     *
     * @authenticated
     *
     * @queryParam status string Filter rides by status. Example: completed
     * @queryParam type string Filter rides by type. Example: immediate
     *
     * @responseFile status=200 test-responses/Ride/index.json
     * @responseFile status=200 test-responses/Driver/driverRideList.json
     */
    public function index()
    {
        $rides = $this->rideService->index();
        return response()->success(data: RideResource::collection($rides)->response()->getData());
    }
    /**
     * Display Ride Details
     *
     * Retrieve detailed information about a specific ride.
     *
     * @authenticated
     *
     * @urlParam ride integer required The ID of the ride. Example: 1
     *
     * @responseFile status=200 test-responses/Ride/show.json
     */
    public function show(Ride $ride)
    {
        $this->authorize('view', $ride);
        $ride->load([
            'points',
            'driver.vehicle',
            'customer',
            'customerRating',
            'driverRating',
            'points',
            'coupon'
        ]);
        return response()->success(data: RideResource::make($ride),);
    }

    /**
     * Confirm a Ride
     *
     * This endpoint let's user to confirm a ride request.
     *
     */
    public function store(StoreRideRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            $user = auth()->user();
            if (!$user || !$user->userable) {
                throw new Exception(__('User profile is not properly set up'));
            }

            $ride = $this->rideService->createRide(
                $user->userable->id,
                $validated,
            );

            return response()->success(
                data: RideResource::make($ride),
                message: __('Ride created successfully')
            );
        } catch (Exception $e) {
            Log::error('Failed to create ride', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->message(
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Estimate Pricing for a ride
     *
     * This endpoint estimate Pricing for a ride.
     * @responseFile test-responses/Ride/estimateRidePricing.json
     *
     */
    public function estimatePricing(RequestRideRequest $request): JsonResponse
    {
        try {
            $pricing = $this->rideService->calculatePricing(
                $request->validated()['points']
            );

            // Check if there's an error in the pricing calculation
            if (isset($pricing['error'])) {
                return response()->fail(
                    message: $pricing['error'],
                    code: 422
                );
            }

            // Determine the appropriate message
            $message = isset($pricing['warning'])
                ? $pricing['warning']
                : __('Ride estimated successfully');

            return response()->success(
                data: $pricing,
                message: $message
            );
        } catch (Exception $e) {
            Log::error('Error in estimatePricing controller method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->fail(
                message: __('Unable to estimate ride pricing. Please try again.'),
                code: 500
            );
        }
    }

    /**
     * Cancel a Ride
     *
     * This endpoint allows users to cancel a pending or accepted ride and changes status to canceled.
     *
     * @urlParam ride integer required The ID of the ride. Example: 1
     * @bodyParam cancel_reason string required The reason for canceling the ride. Example: Driver Ask that
     *
     * @responseFile test-responses/Ride/cancelRide.json
     */
    public function cancelRide(Ride $ride, CancelRideRequest $request)
    {
        $data = $request->validated();
        if ($ride->driver) {
            $result = $this->rideService->cancelRide($ride, $ride->driver, $data['cancel_reason']);
        } else {
            $this->rideService->destroy($ride->id, isForce: true);
            return response()->success(
                [],
                __('Ride canceled successfully')
            );
        }

        if ($result['status'] === 'error') {
            return response()->fail($result['message'], 422);
        }

        return response()->success(
            RideResource::make($ride->fresh()),
            __('Ride canceled successfully')
        );
    }

    /**
     * Apply Coupon to Ride
     *
     * Apply a coupon to a confirmed ride before completion
     *
     * @urlParam ride integer required The ID of the ride. Example: 1
     * @bodyParam code string required Coupon code. Example: SUMMER20
     *
     * @responseFile test-responses/Ride/apply-coupon.json
     */
    public function applyCoupon(Ride $ride, ApplyCouponRequest $request)
    {
        $validated = $request->validated();
        $result = $this->rideService->applyCoupon($ride, $validated['code']);

        if ($result['status'] === 'error') {
            return response()->fail(
                $result['message'],
                $result['code']
            );
        }

        return response()->success(
            RideResource::make($result['data']),
            __('Coupon applied successfully')
        );
    }

    /**
     * Search Areas
     *
     * Search for Syrian cities using Google Places API with caching.
     * Results include Google's required attribution.
     *
     * @queryParam query string required Search term (2+ characters). Example: دم
     *
     * @responseFile test-responses/Area/search-areas.json
     */
    public function searchAreas(AreaSearchRequest $request, GoogleMapsService $mapsService)
    {
        try {
            $results = $mapsService->searchAreas($request->validated()['query']);

            return response()->success(
                data: $results,
                message: __('Areas retrieved successfully')
            );
        } catch (Exception $e) {
            return response()->fail(
                message: $e->getMessage(),
                code: $e->getCode()
            );
        }
    }

    /**
     * Get the in-progress ride for the authenticated user (driver or customer)
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function inProgressRide()
    {
        $user = auth()->user();
        $ride = $this->rideService->getInProgressRideForUser($user);
        return response()->success(
            $ride ? RideResource::make($ride) : null,
            $ride ? __('In-progress ride found') : __('No in-progress ride')
        );
    }
}
