<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Driver\CancelRideRequest;
use App\Http\Requests\Api\Driver\ComplatedRideRequest;
use App\Http\Requests\Api\Driver\StoreDriverRequest;
use App\Http\Requests\Api\Driver\UpdateAvailabilityRequest;
use App\Http\Requests\Api\Driver\UpdateDriverRequest;
use App\Http\Resources\DriverResource;
use App\Http\Resources\RideResource;
use App\Models\Ride;
use App\Services\DriverService;
use App\Services\RideService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

/**
 * @group Driver 
 * APIs for Driver Management
 *
 * @subgroup Driver Endpoints
 *
 * @subgroupDescription Endpoints for handling register Driver, Update Driver Availability.
 */
class DriverController extends Controller
{
    public function __construct(private DriverService $driverService, private RideService $rideService)
    {
        // Constructor code
    }

    /**
     * Store a new  Driver 
     *
     * This endpoint Register a new Driver.
     * @responseFile test-responses/Driver/Driverregister.json
     * 
     */
    public function storeDriver(StoreDriverRequest $request): JsonResponse
    {

        $driver = $this->driverService->store($request->validated());
        return response()->success(DriverResource::make($driver), _('Driver registration completed successfully. You are pending approval.'));
    }

    /**
     * Update Driver Availability Status
     *
     * This endpoint Update Driver Availability Status.
     * @responseFile test-responses/Driver/updateAvailability.json
     * 
     */

    public function updateAvailability(UpdateAvailabilityRequest $request): JsonResponse
    {
        $data = $request->validated();
        $driver = auth()->user()->userable;

        $updatedDriver = $this->driverService->update($driver, $data);
        return response()->success(DriverResource::make($updatedDriver), message: _('Availability updated successfully.'));
    }

    /**
     * Update Driver Profile
     *
     * This endpoint Update Driver Profile.
     */
    public function updateDriverProfile(UpdateDriverRequest $request): JsonResponse
    {
        $driver = auth()->user()->userable;
        $updatedDriver = $this->driverService->updateDriver($driver, $request->validated());
        return response()->success(
            DriverResource::make($updatedDriver),
            __('Driver information updated successfully')
        );
    }

    /**
     * Apply to a Ride
     * 
     * This endpoint allows drivers to apply for an available ride and changes status to in progress.
     * 
     * @urlParam ride integer required The ID of the ride. Example: 1
     * 
     * @responseFile test-responses/Driver/applyToRide.json
     */
    public function applyToRide(Ride $ride)
    {
        $driver = auth()->user()->userable;

        $result = $this->rideService->applyToRide(
            ride: $ride,
            driver: $driver
        );

        if ($result['status'] === 'success') {
            return response()->success(
                data: RideResource::make($ride->fresh()),
                message: __('Ride application successful')
            );
        }

        return response()->fail(error: $result['message'], code: 422);
    }

    /**
     * Mark Driver Arrived
     * 
     * @urlParam ride integer required The ID of the ride. Example: 1
     * 
     * @responseFile test-responses/Driver/arriveAtRide.json
     */
    public function arriveAtRide(Ride $ride)
    {
        $result = $this->rideService->arriveAtRide(
            ride: $ride,
            driver: auth()->user()->userable
        );

        if ($result['status'] === 'success') {
            return response()->success(
                data: RideResource::make($ride->fresh()),
                message: __('Driver arrived successfully')
            );
        }

        return response()->fail(error: $result['message'], code: 422);
    }


    /**
     * Start a Ride
     * 
     * This endpoint allows drivers to start an accepted ride and changes status to in progress.
     * 
     * @urlParam ride integer required The ID of the ride. Example: 1
     * 
     * @responseFile test-responses/Driver/startRide.json
     */
    public function startRide(Ride $ride)
    {
        $result =  $this->rideService->startRide($ride, auth()->user()->userable);

        if ($result['status'] === 'error') {
            return response()->fail($result['message'], 422);
        }
        return response()->success(
            RideResource::make($ride->fresh()),
            __('Ride started successfully')
        );
    }


    /**
     * Complete a Ride
     * 
     * This endpoint allows drivers to complete an in-progress ride and changes status to completed.
     * 
     * @urlParam ride integer required The ID of the ride. Example: 1
     * 
     * @responseFile test-responses/Driver/completeRide.json
     */
    public function completeRide(Ride $ride, ComplatedRideRequest $request)
    {
        $data = $request->validated();
        $result = $this->rideService->completeRide($ride, auth()->user()->userable, $data);

        if ($result['status'] === 'error') {
            return response()->fail($result['message'], 422);
        }

        return response()->success(
            RideResource::make($ride->load('coupon')),
            __('Ride completed successfully')
        );
    }


    /**
     * Cancel a Ride
     * 
     * This endpoint allows drivers to cancel a pending or accepted ride and changes status to canceled.
     * 
     * @urlParam ride integer required The ID of the ride. Example: 1
     * @bodyParam cancel_reason string required The reason for canceling the ride. Example: Driver unavailable
     * 
     * @responseFile test-responses/Driver/cancelRide.json
     */
    public function cancelRide(Ride $ride, CancelRideRequest $request)
    {
        if (!Gate::allows('cancel', [$ride])) {
            return response()->fail(__('Unauthorized'), 403);
        }
        $data = $request->validated();

        $result = $this->rideService->cancelRide($ride, auth()->user()->userable, $data['cancel_reason']);

        if ($result['status'] === 'error') {
            return response()->fail($result['message'], 422);
        }

        return response()->success(
            RideResource::make($ride->fresh()),
            __('Ride canceled successfully')
        );
    }
}
