<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\StoreUserRequest;
use App\Http\Requests\Api\User\UpdateUserRequest;
use App\Http\Resources\UserResource;
use App\Services\UserService;
use App\Models\User;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserController extends Controller
{
    public function __construct(private UserService $userService)
    {
        // Constructor code
    }

    public function index()
    {
        $users = $this->userService->index();
        return response()->success(UserResource::collection($users)->response()->getData());
    }

    public function store(StoreUserRequest $request)
    {
        $user = $this->userService->store($request->validated());
        return response()->success(UserResource::make($user), 'User created successfully');
    }

    public function update(UpdateUserRequest $request , User $user )
    {
        $user = $this->userService->update($user,$request->validated());
        return response()->success(UserResource::make($user), 'User created successfully');
    }

    public function show($id)
    {
        $user = $this->userService->show($id);
        return response()->success(UserResource::make($user));
    }

    public function destroy($id)
    {
        $this->userService->destroy($id);
        return response()->success(null, 'User deleted successfully');
    }
}
