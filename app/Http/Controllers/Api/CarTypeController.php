<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CarTypeResource;
use App\Services\CarTypeService;

class CarTypeController extends Controller
{
    public function __construct(private CarTypeService $carTypeService)
    {
    }

    /**
     * Get all car types or only available ones for booking.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // Only return available car types for the API
        $carTypes = $this->carTypeService->index(['onlyAvailable' => true]);
        $formattedData = CarTypeResource::collection($carTypes)->response()->getData();
        return response()->success($formattedData, __('Car types retrieved successfully'));
    }

}
