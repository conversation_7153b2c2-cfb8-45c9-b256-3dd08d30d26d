<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Customer\StoreCustomerRequest;
use App\Http\Requests\Api\Customer\UpdateCustomerRequest;
use App\Http\Resources\CustomerResource;
use App\Services\CustomerService;
use App\Models\Customer;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerController extends Controller
{
    public function __construct(private CustomerService $customerService)
    {
        // Constructor code
    }

    public function index()
    {
        $customers = $this->customerService->index();
        return response()->success(CustomerResource::collection($customers)->response()->getData());
    }

    public function store(StoreCustomerRequest $request)
    {
        $customer = $this->customerService->store($request->validated());
        return response()->success(CustomerResource::make($customer), 'Customer created successfully');
    }

    public function update(UpdateCustomerRequest $request , Customer $customer )
    {
        $customer = $this->customerService->update($customer,$request->validated());
        return response()->success(CustomerResource::make($customer), 'Customer created successfully');
    }

    public function show($id)
    {
        $customer = $this->customerService->show($id);
        return response()->success(CustomerResource::make($customer));
    }

    public function destroy($id)
    {
        $this->customerService->destroy($id);
        return response()->success(null, 'Customer deleted successfully');
    }
}
