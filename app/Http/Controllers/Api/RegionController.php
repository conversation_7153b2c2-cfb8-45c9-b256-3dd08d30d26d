<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Region\StoreRegionRequest;
use App\Http\Requests\Api\Region\UpdateRegionRequest;
use App\Http\Resources\RegionResource;
use App\Services\RegionService;
use App\Models\Region;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RegionController extends Controller
{
    public function __construct(private RegionService $regionService)
    {
        // Constructor code
    }

    public function index()
    {
        $regions = $this->regionService->index();
        return response()->success(RegionResource::collection($regions)->response()->getData());
    }
}
