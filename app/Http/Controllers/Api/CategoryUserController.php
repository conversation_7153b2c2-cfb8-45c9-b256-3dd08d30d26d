<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\CategoryUser\StoreCategoryUserRequest;
use App\Http\Requests\Api\CategoryUser\UpdateCategoryUserRequest;
use App\Http\Resources\CategoryUserResource;
use App\Services\CategoryUserService;
use App\Models\CategoryUser;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CategoryUserController extends Controller
{
    public function __construct(private CategoryUserService $categoryUserService)
    {
        // Constructor code
    }

    public function index()
    {
        $categoryUsers = $this->categoryUserService->index();
        return response()->success(CategoryUserResource::collection($categoryUsers)->response()->getData());
    }

    public function store(StoreCategoryUserRequest $request)
    {
        $categoryUser = $this->categoryUserService->store($request->validated());
        return response()->success(CategoryUserResource::make($categoryUser), 'CategoryUser created successfully');
    }

    public function update(UpdateCategoryUserRequest $request , CategoryUser $categoryUser )
    {
        $categoryUser = $this->categoryUserService->update($categoryUser,$request->validated());
        return response()->success(CategoryUserResource::make($categoryUser), 'CategoryUser created successfully');
    }

    public function show($id)
    {
        $categoryUser = $this->categoryUserService->show($id);
        return response()->success(CategoryUserResource::make($categoryUser));
    }

    public function destroy($id)
    {
        $this->categoryUserService->destroy($id);
        return response()->success(null, 'CategoryUser deleted successfully');
    }
}
