<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Coupon\ActivateCouponRequest;
use App\Http\Resources\CouponResource;
use App\Services\CouponService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Coupons
 * 
 * APIs for managing customer coupons
 */
class CouponController extends Controller
{
    public function __construct(private CouponService $couponService)
    {
    }

    /**
     * Get Active Coupons
     * 
     * Retrieve paginated list of active coupons available for the authenticated user
     * 
     * @queryParam filter[code] string Filter by coupon code
     * @queryParam filter[type] string Filter by coupon type (percentage/fixed)
     * 
     * @responseFile test-responses/coupons/index.json
     */
    public function index(Request $request): JsonResponse
    {
        $coupons = $this->couponService->getActiveCoupons($request->user());
        return response()->success(CouponResource::collection($coupons));
    }

    /**
     * Activate Coupon
     * 
     * Add a new coupon to user's available coupons using coupon code
     * 
     * @bodyParam code string required Coupon code to activate. Example: SUMMER20
     * 
     * @responseFile test-responses/coupons/activate.json
     */
    public function activate(ActivateCouponRequest $request): JsonResponse
    {
        $data = $request->validated();
        try {
            $coupon = $this->couponService->activateCoupon(
                $data['code'],
                auth()->user()
            );
            
            return response()->success(
                CouponResource::make($coupon),
                __('Coupon activated successfully')
            );
            
        } catch (\Exception $e) {
            return response()->fail(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }
}
