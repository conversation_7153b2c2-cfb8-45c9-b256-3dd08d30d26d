<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Rating\StoreRatingRequest;
use App\Http\Resources\RatingResource;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\Ride;
use App\Services\RatingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

/**
 * @group Rating
 * APIs for Rating Management
 *
 * @subgroup Rating Endpoints
 *
 * @subgroupDescription Endpoints for handling ratings between drivers and customers.
 */
class RatingController extends Controller
{
    public function __construct(private RatingService $ratingService)
    {
        // Constructor code
    }

    /**
     * Rate a driver after a ride
     *
     * This endpoint allows a customer to rate a driver after a completed ride.
     *
     * @responseFile test-responses/Rating/rate-driver.json
     */
    public function rateDriver(StoreRatingRequest $request, Ride $ride): JsonResponse
    {
        $user = Auth::user();

        // Check if user is a customer
        if (!$user->isCustomer()) {
            return response()->json([
                'code' => 403,
                'message' => __('api.rating.errors.not_customer'),
                'data' => null
            ], 403);
        }

        // Check if user is assigned to this ride
        if ($ride->customer_id !== $user->userable->id) {
            return response()->json([
                'code' => 403,
                'message' => __('api.rating.errors.not_assigned_customer'),
                'data' => null
            ], 403);
        }

        // Check if the ride is completed
        if ($ride->status->value !== 'completed') {
            return response()->json([
                'code' => 422,
                'message' => __('api.rating.errors.ride_not_completed'),
                'data' => null
            ], 422);
        }

        try {
            $rating = $this->ratingService->rateDriver($ride, $user->userable, $request->validated());
            return response()->json([
                'code' => 200,
                'message' => __('api.rating.success.driver_rated'),
                'data' => RatingResource::make($rating)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 422,
                'message' => __('api.rating.errors.already_rated'),
                'data' => null
            ], 422);
        }
    }

    /**
     * Rate a customer after a ride
     *
     * This endpoint allows a driver to rate a customer after a completed ride.
     *
     * @responseFile test-responses/Rating/rate-customer.json
     */
    public function rateCustomer(StoreRatingRequest $request, Ride $ride): JsonResponse
    {
        // Check if the authenticated user is the driver of this ride
        $user = Auth::user();
        // Check if user is the driver of this ride
        if (!$user->isDriver()) {
            return response()->json([
                'code' => 403,
                'message' => __('api.rating.errors.not_driver'),
                'data' => null
            ], 403);
        }

        // Check if user is assigned to this ride
        if ($ride->driver_id !== $user->userable->id) {
            return response()->json([
                'code' => 403,
                'message' => __('api.rating.errors.not_assigned_driver'),
                'data' => null
            ], 403);
        }

        // Check if the ride is completed
        if ($ride->status->value !== 'completed') {
            return response()->json([
                'code' => 422,
                'message' => __('api.rating.errors.ride_not_completed'),
                'data' => null
            ], 422);
        }

        try {
            $rating = $this->ratingService->rateCustomer($ride, $user->userable, $request->validated());
            return response()->json([
                'code' => 200,
                'message' => __('api.rating.success.customer_rated'),
                'data' => RatingResource::make($rating)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 422,
                'message' => __('api.rating.errors.already_rated'),
                'data' => null
            ], 422);
        }
    }

    /**
     * Get ratings for the authenticated user
     *
     * This endpoint returns all ratings received by the authenticated user.
     *
     * @responseFile test-responses/Rating/get-my-ratings.json
     */
    public function getMyRatings(): JsonResponse
    {
        $user = Auth::user();

        if ($user->isDriver()) {
            $ratings = $this->ratingService->getDriverRatings($user->userable);
            $averageRating = $this->ratingService->getDriverAverageRating($user->userable);
        } elseif ($user->isCustomer()) {
            $ratings = $this->ratingService->getCustomerRatings($user->userable);
            $averageRating = $this->ratingService->getCustomerAverageRating($user->userable);
        } else {
            return response()->json([
                'code' => 422,
                'message' => __('api.rating.errors.invalid_user_type'),
                'data' => null
            ], 422);
        }

        return response()->json([
            'code' => 200,
            'message' => __('api.rating.success.ratings_retrieved'),
            'data' => [
                'ratings' => RatingResource::collection($ratings),
                'average_rating' => $averageRating
            ]
        ], 200);
    }

    /**
     * Get ratings for a specific driver
     *
     * This endpoint returns all ratings received by a specific driver.
     *
     * @responseFile test-responses/Rating/get-driver-ratings.json
     */
    public function getDriverRatings(Driver $driver): JsonResponse
    {
        $ratings = $this->ratingService->getDriverRatings($driver);
        $averageRating = $this->ratingService->getDriverAverageRating($driver);

        return response()->json([
            'code' => 200,
            'message' => __('api.rating.success.driver_ratings_retrieved'),
            'data' => [
                'ratings' => RatingResource::collection($ratings),
                'average_rating' => $averageRating
            ]
        ], 200);
    }

    /**
     * Get ratings for a specific customer
     *
     * This endpoint returns all ratings received by a specific customer.
     *
     * @responseFile test-responses/Rating/get-customer-ratings.json
     */
    public function getCustomerRatings(Customer $customer): JsonResponse
    {
        $ratings = $this->ratingService->getCustomerRatings($customer);
        $averageRating = $this->ratingService->getCustomerAverageRating($customer);

        return response()->json([
            'code' => 200,
            'message' => __('api.rating.success.customer_ratings_retrieved'),
            'data' => [
                'ratings' => RatingResource::collection($ratings),
                'average_rating' => $averageRating
            ]
        ], 200);
    }
}
