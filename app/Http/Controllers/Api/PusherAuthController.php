<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use P<PERSON><PERSON>\Pusher;
use P<PERSON>er\PusherException;

/**
 * PusherAuthController handles authentication for Pusher private channels.
 *
 * This controller provides an endpoint for authenticating users who want to subscribe
 * to private Pusher channels. It verifies that the user is authenticated and authorized
 * to access the requested channel before generating an authentication signature.
 *
 * Private channels are used for secure real-time communication, particularly for
 * sensitive data like driver locations and ride information.
 *
 * @package App\Http\Controllers\Api
 */
class PusherAuthController extends Controller
{
    /**
     * Authenticate the user for Pusher private channels.
     *
     * This method:
     * 1. Verifies that the user is authenticated
     * 2. Extracts the socket ID and channel name from the request
     * 3. Creates a Pusher instance with the application's credentials
     * 4. Generates an authentication signature for the requested channel
     * 5. Returns the authentication signature as a JSON response
     *
     * The authentication signature is required by the Pusher client library
     * to subscribe to private channels.
     *
     * @param Request $request The HTTP request containing socket_id and channel_name
     * @return JsonResponse|Response Authentication signature or error response
     * @throws PusherException If there's an error generating the authentication signature
     */
    public function authenticate(Request $request): JsonResponse|Response
    {
        // Check for Bearer token in the Authorization header
        $bearerToken = $request->bearerToken();

        // Log the token for debugging (first 10 chars only)
        if ($bearerToken) {
            Log::info('Bearer token found', [
                'token_prefix' => substr($bearerToken, 0, 10) . '...',
                'token_length' => strlen($bearerToken)
            ]);
        } else {
            Log::info('No Bearer token found in request');
        }

        // Try to get user from API token or web session
        $sanctumUser = Auth::guard('sanctum')->user();
        $webUser = Auth::guard('web')->user();
        $user = $sanctumUser ?? $webUser;

        // Log authentication attempt details
        Log::info('Pusher auth attempt details', [
            'sanctum_user' => $sanctumUser ? $sanctumUser->id : null,
            'web_user' => $webUser ? $webUser->id : null,
            'user' => $user ? $user->id : null,
            'is_authenticated' => Auth::check(),
            'auth_guard' => Auth::getDefaultDriver(),
            'has_bearer_token' => !empty($bearerToken),
            'request_headers' => array_keys($request->headers->all()),
        ]);

        if (!$user) {
            Log::warning('Pusher auth failed - no authenticated user found');
            return response('Unauthorized', 401);
        }

        // Validate the request parameters
        if (!$request->has('socket_id') || !$request->has('channel_name')) {
            return response('Missing required parameters', 422);
        }

        // Get the socket ID and channel name from the request
        $socketId = $request->socket_id;
        $channelName = $request->channel_name;

        // Log the authentication attempt
        Log::info('Pusher authentication attempt', [
            'user_id' => $user->id,
            'channel_name' => $channelName,
            'user_type' => $user->userable_type ?? 'unknown'
        ]);

        try {
            // Create a new Pusher instance
            $pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options')
            );

            // Generate the auth signature
            $auth = $pusher->authorizeChannel($channelName, $socketId);

            // Log successful authentication with detailed information
            Log::info('Pusher authentication successful', [
                'user_id' => $user->id,
                'channel_name' => $channelName,
                'socket_id' => $socketId,
                'auth_response' => $auth,
                'app_key' => config('broadcasting.connections.pusher.key')
            ]);

            return response()->json($auth);
        } catch (PusherException $e) {
            // Log the error
            Log::error('Pusher authentication error', [
                'user_id' => $user->id,
                'channel_name' => $channelName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response('Error authenticating with Pusher', 500);
        }
    }
}
