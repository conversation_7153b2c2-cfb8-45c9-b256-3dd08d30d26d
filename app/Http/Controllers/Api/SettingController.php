<?php

namespace App\Http\Controllers\Api;

use App\Enums\SettingEnum;
use App\Http\Controllers\Controller;
use App\Http\Resources\SettingResource;
use App\Services\SettingService;


/**
 * @group Application Settings
 *
 * APIs for retrieving application configuration and static content
 */
class SettingController extends Controller
{

    public function __construct(private SettingService $settingService)
    {
    }

    /**
     * Get Application Settings
     *
     * Retrieve all publicly available application settings including:
     * - Social media links
     * - Contact information
     * - Legal documents
     * - Application coordinates
     *
     * @unauthenticated
     *
     * @responseFile status=200 test-responses/Settings/index.json
     *
     */
    public function index()
    {
           
        $keys = SettingEnum::cases();
        
        
        $settings = $this->settingService->index($keys);
        
        $settings = SettingResource::collection($settings);
        return response()->success($settings);
    }

}
