<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class GoogleMapsProxyController extends Controller
{
    /**
     * Proxy requests to Google Maps API to avoid referrer restrictions
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function proxy(Request $request)
    {
        // Get the API key from config
        $apiKey = config('services.google_maps.api_key');
        
        // Build the Google Maps API URL with the API key
        $url = 'https://maps.googleapis.com/maps/api/js';
        $queryParams = $request->all();
        $queryParams['key'] = $apiKey;
        
        // Add callback if not present
        if (!isset($queryParams['callback'])) {
            $queryParams['callback'] = 'Function.prototype';
        }
        
        // Convert query params to string
        $queryString = http_build_query($queryParams);
        $fullUrl = $url . '?' . $queryString;
        
        // Cache the response to reduce API calls
        $cacheKey = 'google_maps_js_' . md5($fullUrl);
        $cacheTtl = 60 * 24; // 1 day in minutes
        
        return Cache::remember($cacheKey, $cacheTtl, function () use ($fullUrl) {
            // Make the request to Google Maps API
            $response = Http::get($fullUrl);
            
            // Return the response with appropriate headers
            return response($response->body())
                ->header('Content-Type', 'application/javascript')
                ->header('Cache-Control', 'public, max-age=86400'); // 1 day
        });
    }
}
