<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Pusher\Pusher;

class PusherTestController extends Controller
{
    /**
     * Test sending a location update for a driver
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendDriverLocation(Request $request)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'socket_id' => 'nullable|string',
        ]);

        try {
            $pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options')
            );

            $data = [
                'driver_id' => $request->driver_id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'timestamp' => now()->toIso8601String(),
            ];

            // Get the driver's active rides if any
            $driver = Driver::findOrFail($request->driver_id);
            $activeRides = $driver->rides()->whereIn('status', ['accepted', 'in_progress'])->get();

            // Broadcast to driver's channel
            $pusher->trigger(
                'private-driver.' . $request->driver_id,
                'location-updated',
                $data,
                $request->socket_id
            );

            // Also broadcast to any active ride channels
            foreach ($activeRides as $ride) {
                $pusher->trigger(
                    'private-ride.' . $ride->id,
                    'driver-location-updated',
                    $data,
                    $request->socket_id
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Driver location broadcast successful',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Pusher broadcast error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to broadcast driver location: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Test sending a notification to a user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendUserNotification(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'message' => 'required|string',
            'socket_id' => 'nullable|string',
        ]);

        try {
            $pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options')
            );

            $data = [
                'message' => $request->message,
                'timestamp' => now()->toIso8601String(),
            ];

            $pusher->trigger(
                'private-user.' . $request->user_id,
                'notification',
                $data,
                $request->socket_id
            );

            return response()->json([
                'success' => true,
                'message' => 'User notification broadcast successful',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Pusher broadcast error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to broadcast user notification: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get all drivers for testing
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDrivers()
    {
        try {
            $drivers = Driver::with('user:id,first_name,last_name')->get();

            if ($drivers->isEmpty()) {
                Log::info('No drivers found in the database');
                return response()->json([
                    'success' => true,
                    'message' => 'No drivers found',
                    'data' => []
                ]);
            }

            $formattedDrivers = $drivers->map(function ($driver) {
                if (!$driver->user) {
                    Log::warning('Driver ID ' . $driver->id . ' has no associated user');
                    return null;
                }

                return [
                    'id' => $driver->id,
                    'name' => $driver->user->first_name . ' ' . $driver->user->last_name,
                    'user_id' => $driver->user_id,
                ];
            })->filter()->values();

            Log::info('Drivers retrieved successfully', ['count' => $formattedDrivers->count()]);
            return response()->json([
                'success' => true,
                'message' => 'Drivers retrieved successfully',
                'data' => $formattedDrivers
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving drivers: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving drivers: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get all users for testing
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsers()
    {
        try {
            $users = User::select('id', 'first_name', 'last_name')->get();

            if ($users->isEmpty()) {
                Log::info('No users found in the database');
                return response()->json([
                    'success' => true,
                    'message' => 'No users found',
                    'data' => []
                ]);
            }

            $formattedUsers = $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->first_name . ' ' . $user->last_name,
                ];
            });

            Log::info('Users retrieved successfully', ['count' => $formattedUsers->count()]);
            return response()->json([
                'success' => true,
                'message' => 'Users retrieved successfully',
                'data' => $formattedUsers
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving users: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving users: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
