<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\CompleteProfileRequest;
use App\Http\Requests\Api\Auth\SendAuthCodeRequest;
use App\Http\Requests\Api\Auth\UpdateProfileRequest;
use App\Http\Requests\Api\Auth\VerifyAuthCodeRequest;
use App\Http\Resources\AdminResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\DriverResource;
use App\Models\Admin;
use App\Models\Customer;
use App\Models\DeviceToken;
use App\Models\Driver;
use App\Services\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Js;

use Modules\Notification\Services\NotificationService;
use function Laravel\Prompts\error;

/**
 * @group Auth
 * APIs for Authentication Management
 *
 * @subgroup Auth Endpoints
 *
 * @subgroupDescription Endpoints for handling OTP, user profile, logout, and account deletion.
 */
class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Request OTP
     *
     * This endpoint sends an OTP code to the specified phone number.
     *
     * @responseFile test-responses/Auth/request-otp.json
     */
    public function requestOtp(SendAuthCodeRequest $request): JsonResponse
    {
        $phone = $request->input('phone');
        $result = $this->authService->requestOtp($phone);

        if ($result['success']) {
            return response()->success(null, __('Code sent successfully'));
        }

        // Handle rate limiting response
        if (isset($result['message'])) {
            return response()->fail(
                $result['message'],
                429 // Too Many Requests
            );
        }

        return response()->fail(__('Failed to send code'), 422);
    }

    /**
     * Verify OTP
     *
     * This endpoint verifies the OTP code sent to the user's phone.
     * @bodyParam phone string required The phone number to verify. Example: +963987654321.
     * @bodyParam code string required The OTP code sent to the phone. Test Code: 12345.
     * @responseFile test-responses/Auth/verify-otp.json
     *
     */
    public function verifyOTP(VerifyAuthCodeRequest $request)
    {
        $data = $request->validated();

        $result = $this->authService->verifyOTP($data);
        if ($result['status'] === 'success') {
            return response()->success(message: $result['message'], data: $result['data']);
        }

        return response()->fail(error: $result['message'], code: 422);
    }


    /**
     * Get Profile
     *
     * This endpoint retrieves the authenticated user's profile.
     *
     * @header Authorization Bearer token required The access token of the authenticated user.
     * @responseFile test-responses/Auth/profile.json
     *
     */
    public function getProfile()
    {
        $user = auth()->user();

        $userable = $user->userable;
        if ($userable instanceof Customer) {
            return response()->success(message: __('Profile fetched successfully'), data: new CustomerResource($userable));
        }

        if ($userable instanceof Driver) {
            $userable->load(['user', 'vehicle']);
            return response()->success(message: __('Profile fetched successfully'), data: new DriverResource($userable));
        }

        return response()->fail(error: __('Profile not found'), code: 404);

    }

    /**
     * Logout
     *
     * This endpoint logs out the authenticated user by revoking the current access token.
     *
     * @header Authorization Bearer token required The access token of the authenticated user.
     * @responseFile test-responses/Auth/logout.json
     *
     */
    public function logout()
    {
        /**
         * @var User $user
         */
        $user = auth()->user();
        $user->currentAccessToken()->delete();
        (new NotificationService())->unRegisterToken($user->deviceTokens()->pluck('token')->toArray());
        $user->deviceTokens()->delete();
        return response()->success(message: __('Logged out successfully'));
    }

    /**
     * Complete Profile
     *
     * This endpoint completes the user's profile by updating the necessary details.
     *
     * @responseFile test-responses/Auth/complete-profile.json
     *
     */

    public function completeProfile(CompleteProfileRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        $result = $this->authService->completeProfile($validatedData);

        // Return the updated profile
        if ($result['status'] === 'success') {
            return response()->success(message: $result['message'], data: $result['data']);
        }

        return response()->fail(error: __('Unable to update profile'), code: 422);
    }
    /**
     * Update user Profile
     *
     * This endpoint update the user's profile by updating the necessary details.
     *
     * @responseFile test-responses/Auth/update-profile.json
     *
     */

    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        $result = $this->authService->updateProfile($validatedData);


        // Return the updated profile
        if ($result['status'] === 'success') {
            return response()->success(message: $result['message'], data: $result['data']);
        }

        return response()->fail(error: __('Unable to update profile'), code: 422);
    }

    /**
     * Delete User Account
     *
     * This endpoint deletes the authenticated user's account along with related data.
     *
     * @header Authorization Bearer token required The access token of the authenticated user.
     *
     * @responseFile test-responses/Auth/delete-account.json
     *
     */
    public function deleteUserAccount(): JsonResponse
    {
        $result = $this->authService->deleteUserAccount();

        if ($result['success']) {
            return response()->success(message: __('User deleted successfully'));
        }

        return response()->fail(error: __('Unable to delete user'), code: 422);
    }

    // public function getUserPoints(){

    // }
}
