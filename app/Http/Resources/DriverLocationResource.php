<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DriverLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'driver_id' => $this->driver_id,
            'ride_id' => $this->ride_id,
            'latitude' => (float) $this->latitude,
            'longitude' => (float) $this->longitude,
            'heading' => (float) $this->heading,
            'is_online' => (bool) $this->is_online,
            'address' => $this->address,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'driver' => $this->when($this->relationLoaded('driver'), function () {
                return [
                    'id' => $this->driver->id,
                    'user_id' => $this->driver->user_id,
                    'is_available' => (bool) $this->driver->is_available,
                    'user' => $this->when($this->driver->relationLoaded('user'), function () {
                        return [
                            'id' => $this->driver->user->id,
                            'name' => $this->driver->user->name,
                            'phone' => $this->driver->user->phone,
                        ];
                    }),
                ];
            }),
            'ride' => $this->when($this->relationLoaded('ride'), function () {
                return [
                    'id' => $this->ride->id,
                    'status' => $this->ride->status,
                    'customer_id' => $this->ride->customer_id,
                ];
            }),
        ];
    }
}
