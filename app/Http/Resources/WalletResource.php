<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class WalletResource extends JsonResource
{
    public function toArray($request)
    {
        $transactions = collect([]);
        
        if ($this->relationLoaded('sentTransactions')) {
            $transactions = $transactions->merge($this->sentTransactions);
        }
    
        if ($this->relationLoaded('receivedTransactions')) {
            $transactions = $transactions->merge($this->receivedTransactions);
        }
    
        return [
            'id' => $this->id,
            'balance' => $this->balance,
            'is_active' => $this->is_active,
            'transactions' => TransactionResource::collection(
                $transactions->sortByDesc('created_at')
            ),
            'created_at' => $this->created_at,
        ];
    }
}
