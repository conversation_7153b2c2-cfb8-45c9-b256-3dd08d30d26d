<?php

namespace App\Http\Resources;

use App\Models\Customer;
use App\Models\Driver;
use App\Models\Rating;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'phone_verified_at' => $this->phone_verified_at,
            'user_type' => $this->getUserType(),
            'is_active' => $this->is_active,
            'birthdate' => $this->birthdate,
            'gender' => $this->gender->value,
            'profile_picture' => $this->getFirstMediaUrl('profile_picture'),
            'average_rating' => $this->getAverageRating(),
        ];
    }

    /**
     * Get the average rating for the user based on their userable type
     *
     * @return float
     */
    private function getAverageRating(): float
    {
        if (!$this->userable) {
            return 0;
        }

        $userableType = get_class($this->userable);
        $userableId = $this->userable->id;

        return Rating::where('rateable_type', $userableType)
            ->where('rateable_id', $userableId)
            ->avg('rating') ?? 0;
    }

    /**
     * Get the user type dynamically from the userable relationship.
     */
    private function getUserType(): ?string
    {
        if ($this->userable) {
            return class_basename($this->userable);
        }
        return null;
    }
}
