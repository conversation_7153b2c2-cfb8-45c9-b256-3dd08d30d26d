<?php

namespace App\Http\Resources;

use App\Models\Rating;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'points' => $this->points,
            'refer_code' => $this->refer_code,
            'average_rating' => $this->getAverageRating(),
            'user' => UserResource::make($this->user),
            'access_token' => $this->when($this->token, $this->token),
        ];
    }

    /**
     * Get the average rating for this customer
     *
     * @return float
     */
    private function getAverageRating(): float
    {
        return Rating::where('rateable_type', get_class($this->resource))
            ->where('rateable_id', $this->id)
            ->avg('rating') ?? 0;
    }
}
