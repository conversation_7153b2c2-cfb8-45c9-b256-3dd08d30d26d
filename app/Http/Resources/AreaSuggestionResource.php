<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaSuggestionResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'description' => $this['description'],
            'place_id' => $this['place_id'],
            'location' => [
                'lat' => $this['location']['lat'],
                'lng' => $this['location']['lng']
            ]
        ];
    }
}
