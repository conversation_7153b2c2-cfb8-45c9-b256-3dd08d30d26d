<?php

namespace App\Http\Resources;

use App\Models\Rating;
use Illuminate\Http\Resources\Json\JsonResource;

class DriverResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'is_available' => $this->is_available,
            'license_number' => $this->license_number,
            'front_license_image' => $this->getFirstMediaUrl('front_license_image'),
            'back_license_image' => $this->getFirstMediaUrl('back_license_image'),
            'car_mechanics_image' => $this->getFirstMediaUrl('car_mechanics_image'),
            'car_image' => $this->getFirstMediaUrl('car_image'),
            'status' => $this->status,
            'refuse_reason' => $this->refuse_reason,
            'created_at' => $this->created_at,
            'average_rating' => $this->getAverageRating(),
            'user' => UserResource::make($this->whenLoaded('user')),
            'vehicle' => VehicleResource::make($this->whenLoaded('vehicle')),
            'access_token' => $this->when($this->token, $this->token),
            'current_latitude' => $this->current_latitude,
            'current_longitude' => $this->current_longitude,
        ];
    }

    /**
     * Get the average rating for this driver
     *
     * @return float
     */
    private function getAverageRating(): float
    {
        return Rating::where('rateable_type', get_class($this->resource))
            ->where('rateable_id', $this->id)
            ->avg('rating') ?? 0;
    }
}
