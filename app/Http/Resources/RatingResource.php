<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RatingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'ride_id' => $this->ride_id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'created_at' => $this->created_at,
            'rater' => $this->whenLoaded('rater', function() {
                if ($this->rater_type === 'App\\Models\\Driver') {
                    return DriverResource::make($this->rater);
                } else if ($this->rater_type === 'App\\Models\\Customer') {
                    return CustomerResource::make($this->rater);
                }
                return null;
            }),
            'rateable' => $this->whenLoaded('rateable', function() {
                if ($this->rateable_type === 'App\\Models\\Driver') {
                    return DriverResource::make($this->rateable);
                } else if ($this->rateable_type === 'App\\Models\\Customer') {
                    return CustomerResource::make($this->rateable);
                }
                return null;
            }),
            'ride' => $this->whenLoaded('ride', function() {
                return RideResource::make($this->ride);
            }),
        ];
    }
}
