<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PricingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'shift_id' => $this->shift_id,
            'car_type_id' => $this->car_type_id,
            'minimum_fare' => $this->minimum_fare,
            'km_price' => $this->km_price,
            'miu_price' => $this->miu_price,
            'carType' => CarTypeResource::make($this->whenLoaded('carType')),
            'shift' => ShiftResource::make($this->whenLoaded('shift'))
        ];
    }
}
