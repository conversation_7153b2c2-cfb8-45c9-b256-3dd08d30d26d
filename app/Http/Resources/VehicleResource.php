<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'driver_id' => $this->driver_id,
            'model' => $this->model,
            'plate_number' => $this->plate_number,
            'color' => $this->color,
            'seats' => $this->seats,
            'manufacturing_year' => $this->manufacturing_year,
        ];
    }
}
