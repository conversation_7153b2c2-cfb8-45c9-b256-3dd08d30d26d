<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CouponResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'descrption' => $this->descrption,
            'code' => $this->code,
            'usage_limit_per_user' => $this->usage_limit_per_user,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'type' => $this->type,
            'value' => $this->value,
            'created_at' => $this->created_at,
            'discount_amount' => $this->when($this->ride, function () {
            return $this->calculateDiscount($this->ride->final_price);
        }),
            'price_after_discount' => $this->when($this->ride, function () {
                return $this->calculatePriceAfterDiscount($this->ride->final_price);
            }),
           
        ];
    }
}
