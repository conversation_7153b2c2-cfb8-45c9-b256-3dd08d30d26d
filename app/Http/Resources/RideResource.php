<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RideResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'pricing_id' => $this->pricing_id,
            'customer' => CustomerResource::make($this->whenLoaded('customer')),
            'estimated_distance' => $this->estimated_distance,
            'estimated_duration' => $this->estimated_duration,

            'actual_distance' => $this->actual_distance,
            'actual_duration' => $this->actual_duration,
            'fare' => $this->fare,
            'final_price' => $this->final_price,
            'status' => $this->status,
            'type' => $this->type,
            'scheduled_at' => $this->scheduled_at,
            'requested_at' => $this->requested_at,
            'completed_at' => $this->completed_at,
            'coupon' => CouponResource::make($this->whenLoaded('coupon')),
            'points' => RidePointResource::collection($this->whenLoaded('points')),
            'driver' => new DriverResource($this->whenLoaded('driver')),
            'created_at' => $this->created_at,
            'note' => $this->note,

            'final_price_after_discount' => $this->whenLoaded('coupon', function () {
                return $this->coupon 
                    ? $this->coupon->calculatePriceAfterDiscount($this->final_price)
                    : $this->final_price;
            }, $this->final_price),
            'customer_rating' => RatingResource::make($this->whenLoaded('customerRating')),
            'driver_rating' => RatingResource::make($this->whenLoaded('driverRating')),
        ];
    }
}
