<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'deleted_at' => $this->deleted_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'name' => $this->name,
            'description' => $this->description,
            'categoryUser' => CategoryUserResource::make($this->whenLoaded('categoryUser')),
            'post' => PostResource::make($this->whenLoaded('post')),
            'posts' => PostCollection::make($this->whenLoaded('posts')),
            'categories' => CategoryCollection::make($this->whenLoaded('categories')),
            'media' => MediumCollection::make($this->whenLoaded('media'))
        ];
    }
}
