<?php

namespace App\Http\Middleware;

use App\Models\DeviceToken;
use Closure;
use Illuminate\Http\Request;
use Modules\Notification\Enums\TopicEnum;
use Modules\Notification\Services\NotificationService;
use Symfony\Component\HttpFoundation\Response;

class UpdateFcmTokenMiddleware
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if ($user) {
            $fcmToken = $request->header('fcm_token');

            if ($fcmToken) {
                DeviceToken::whereToken($fcmToken)->delete();
                $user->deviceTokens()->updateOrCreate(['token' => $fcmToken]);

                $parts = explode('\\', $user->userable_type);
                $modelName = end($parts);
                $topic = strtolower($modelName);

                $this->notificationService->registerTokenToTopics($fcmToken, [TopicEnum::ALL->value, $topic]);
            }
        }

        return $next($request);
    }
}
