<?php

namespace App\Http\Middleware;

use App\Models\Driver;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsDriverMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        info($user->toArray());
        info($user->userable);
        if (!$user || !$user->userable instanceof Driver) {
            return response()->json([
                'success' => false,
                'message' => __('Unauthorized')
            ], 403);
        }

        return $next($request);
    }
}
