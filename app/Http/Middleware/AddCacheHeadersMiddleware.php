<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AddCacheHeadersMiddleware
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        if ($response->isSuccessful()) {
            $data = json_decode($response->getContent(), true);
            $cacheStatus = isset($data['data']['cache_hit']) && $data['data']['cache_hit'] 
            ? 'HIT' 
            : 'MISS';
            
            $response->header('X-Cache', $cacheStatus);
        }
        
        return $response;
    }

private function isCachedResponse(): bool
{
    $cacheVersion = config('services.google_maps.cache_version');
    return request()->header('X-Cache') === $cacheVersion;
}
}
