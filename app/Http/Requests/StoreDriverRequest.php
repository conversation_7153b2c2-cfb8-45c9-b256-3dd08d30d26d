<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDriverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'id' => ["nullable", "exists:drivers,id"],
            'user_id' => ["nullable", "integer", "exists:users,id"],
            'is_available' => ["nullable", "boolean"],
            'license_number' => ["required_without:id", "string"],
            'status' => ["nullable", "string"],
            'refuse_reason' => ["nullable", "string"],

            // Vehicle information
            'model' => ["nullable", "string"],
            'plate_number' => ["required_without:id", "string", function ($attribute, $value, $fail) {
                // Get the current request instance
                $request = request();

                // Get the driver ID (either from route parameter or request input)
                $driverId = $request->route('driver') ? $request->route('driver')->id : $request->input('id');

                // If no driver ID, this is a new driver - just check if plate number exists
                if (!$driverId) {
                    $existingVehicle = \App\Models\Vehicle::where('plate_number', $value)->first();
                    if ($existingVehicle) {
                        $fail(__('The plate number has already been taken.'));
                    }
                    return;
                }

                // For existing drivers, get the current vehicle
                $driver = \App\Models\Driver::find($driverId);

                // If driver has a vehicle and the plate number is the same, skip validation
                if ($driver && $driver->vehicle && $driver->vehicle->plate_number === $value) {
                    return;
                }

                // Otherwise check if any other driver has this plate number
                $existingVehicle = \App\Models\Vehicle::where('plate_number', $value)
                    ->when($driver, function($query) use ($driver) {
                        return $query->where('driver_id', '!=', $driver->id);
                    })
                    ->first();

                if ($existingVehicle) {
                    $fail(__('The plate number has already been taken.'));
                }
            }],
            'color' => ["required_without:id", "string"],
            'seats' => ["required_without:id", "integer", "min:1", "max:10"],
            'manufacturing_year' => ["required_without:id", "integer", "max:2030"],

            // Car types and regions
            'car_type_ids' => ["nullable", "array"],
            'car_type_ids.*' => ["exists:car_types,id"],
            'region_id' => ['required','integer','exists:regions,id'],

            // Document uploads
            'front_license_image' => ["nullable", "file", "image", "max:5120"],
            'back_license_image' => ["nullable", "file", "image", "max:5120"],
            'car_image' => ["nullable", "file", "image", "max:5120"],
            'car_mechanics_image' => ["nullable", "file", "image", "max:5120"],

            // User information (always include, but make conditional in the controller)
            'first_name' => ["nullable", "string", "max:255"],
            'last_name' => ["nullable", "string", "max:255"],
            'phone' => ["nullable", "string", "regex:/^963\d{9}$/"],
            'gender' => ["nullable", "string", "in:male,female"],
            'birthdate' => ["nullable", "date"],
        ];

        return $rules;
    }
}
