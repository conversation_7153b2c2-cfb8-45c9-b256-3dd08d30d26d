<?php

namespace App\Http\Requests;

use App\Enums\DeleteableModelEnum;
use App\Enums\DeleteActionEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class DeleteObjectRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }
    public function prepareForValidation()
    {
        $this->merge([
            'objectId' => $this->objectId,
            'objectType' => $this->objectType,
            'actionType' => $this->actionType,
            'withTrashed' => $this->withTrashed,
        ]);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'objectType' => ['required', Rule::enum(DeleteableModelEnum::class)],
            'objectId' => ['required', Rule::exists(Str::lower(Str::plural($this->objectType)), 'id')],
            'actionType' => ['required', 'string', Rule::enum(DeleteActionEnum::class)],
            'withTrashed' => ['nullable', 'boolean']
        ];
    }
}
