<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreShiftRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach(config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required_without:id","string"];
            $langRules["$locale.notes"] = ["required_without:id","string"];
        }
        

        return [
            'id' => ["nullable","exists:shifts,id"],
            'start_time' => ["required_without:id","date_format:H:i"],
            'end_time' => ["required_without:id","date_format:H:i"]
        ] + $langRules;
    }
}
