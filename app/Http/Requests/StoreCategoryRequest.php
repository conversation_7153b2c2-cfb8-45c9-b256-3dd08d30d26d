<?php

namespace App\Http\Requests\Category;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required_without:id","string"];
            $langRules["$locale.description"] = ["required_without:id","string"];
            }

        return [
            'id' => ["nullable","exists:categories,id"]
        ] + $langRules;
    }
}
