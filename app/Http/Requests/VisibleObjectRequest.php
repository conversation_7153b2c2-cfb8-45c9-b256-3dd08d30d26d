<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class VisibleObjectRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        // Log the request data for debugging
        Log::info('VisibleObjectRequest data', [
            'objectId' => $this->objectId,
            'objectType' => $this->objectType,
            'columnName' => $this->columnName,
            'all' => $this->all()
        ]);

        // Get the table name based on the object type
        $tableName = $this->getTableName($this->objectType);

        return [
            'objectId' => ['required', Rule::exists($tableName, 'id')],
            'objectType' => ['required'],
            'columnName' => ['required', 'string'],
        ];
    }

    /**
     * Get the table name for the given object type.
     *
     * @param string $objectType
     * @return string
     */
    protected function getTableName($objectType)
    {
        // Convert CamelCase to snake_case and pluralize
        // For example: 'CarType' becomes 'car_types'
        return Str::snake(Str::plural($objectType));
    }
}
