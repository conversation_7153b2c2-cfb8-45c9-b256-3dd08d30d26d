<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTransactionRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.description"] = ["nullable","string"];
            }

        return [
            'id' => ["nullable","exists:transactions,id"],
            'sender_wallet_id' => ["required_without:id","integer","exists:wallets,id"],
            'receiver_wallet_id' => ["required_without:id","integer","exists:wallets,id"],
            'ride_id' => ["nullable","integer","exists:rides,id"],
            'coupon_id' => ["nullable","integer","exists:coupons,id"],
            'payment_method' => ["required_without:id","string"],
            'type' => ["required_without:id","string"],
            'amount' => ["required_without:id","numeric"]
        ] + $langRules;
    }
}
