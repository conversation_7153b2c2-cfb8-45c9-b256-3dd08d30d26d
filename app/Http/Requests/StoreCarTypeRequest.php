<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCarTypeRequest extends FormRequest
{

    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required_without:id","string","max:255"];
            $langRules["$locale.description"] = ["required_without:id","string","max:255"];
            }

        return [
            'id' => ["nullable","exists:car_types,id"],
            'km_range' => ['required', 'numeric', 'regex:/^\d+[\.,]\d$/'],
        ] + $langRules;
    }


    public function messages()
    {
        return [
            'km_range.required' => __('The kilometer range field is required.'),
            'km_range.numeric' => __('The kilometer range must be a number.'),
            'km_range.regex' => __('The kilometer range must be a number with exactly one decimal place (e.g., 1,8).'),
            '*.name.required_without' => __('The name field is required.'),
            '*.description.required_without' => __('The description field is required.'),
            '*.name.string' => __('The name must be a string.'),
            '*.name.max' => __('The name may not be greater than :max characters.'),
            '*.description.string' => __('The description must be a string.'),
            '*.description.max' => __('The description may not be greater than :max characters.'),
            'id.exists' => __('The selected car type is invalid.'),
        ];
    }
}
