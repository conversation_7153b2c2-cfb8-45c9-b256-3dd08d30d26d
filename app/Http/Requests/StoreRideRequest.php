<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRideRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'driver_id' => 'required|exists:drivers,id',
            'customer_id' => 'required|exists:customers,id',
            'pickup_location' => 'required|string',
            'dropoff_location' => 'required|string',
            'status' => 'required|string',
            'price' => 'required|numeric',
            'distance' => 'required|numeric',
            'duration' => 'required|numeric',
            'scheduled_at' => 'nullable|date',
        ];

        // If we're updating an existing ride, make the ID field required
        if (request()->isMethod('PUT') || request()->has('id')) {
            $rules['id'] = 'required|exists:rides,id';
        }

        return $rules;
    }
}
