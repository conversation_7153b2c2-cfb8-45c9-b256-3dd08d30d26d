<?php

namespace App\Http\Requests;

use App\Enums\SettingEnum;
use App\Enums\SettingKeyEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'settings' => ['required', 'array'],
            'settings.*.key' => ['required', Rule::enum(SettingEnum::class)],
            'settings.*.value' => ['required'],
            'settings.*.image' => ['file'],
        ];
    }
}
