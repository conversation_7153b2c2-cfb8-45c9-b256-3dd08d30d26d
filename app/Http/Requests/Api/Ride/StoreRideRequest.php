<?php

namespace App\Http\Requests\Api\Ride;

use App\Enums\DriverGenderEnum;
use App\Enums\RideTypeEnum;
use App\Enums\UserGenderEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRideRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'points' => ['required', 'array', 'min:2'],
            'points.*.latitude' => ['required', 'numeric'],
            'points.*.longitude' => ['required', 'numeric'],
            'points.*.address' => ['required', 'string'],
            'type' => ['required', 'string', Rule::enum(RideTypeEnum::class)],
            'scheduled_at' => [
                'nullable',
                Rule::requiredIf(function () {
                    return $this->input('type') === RideTypeEnum::SCHEDULED->value;
                }),
                'date'
            ],
            'coupon_code' => ['sometimes', 'nullable', 'string'],
            'pricing_id' => ['required', 'numeric',Rule::exists('pricings','id')],
            'driver_gender' => ['required' ,Rule::enum(DriverGenderEnum::class)],
            'note' => ['sometimes', 'nullable', 'string'],
        ];
    }
}
