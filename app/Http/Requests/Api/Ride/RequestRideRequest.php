<?php

namespace App\Http\Requests\Api\Ride;

use App\Enums\RideTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RequestRideRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'points' => ['required', 'array', 'min:2'],
            'points.*.latitude' => ['required', 'numeric', 'between:-90,90'],
            'points.*.longitude' => ['required', 'numeric', 'between:-180,180'],
            'points.*.address' => ['required', 'string', 'max:255'],
            'type' => ['required', Rule::enum(RideTypeEnum::class)],
            'scheduled_at' => [
                'nullable',
                Rule::requiredIf(function () {
                    return $this->input('type') === RideTypeEnum::SCHEDULED->value;
                }),
                'date'
            ],
            'coupon_code' => ['sometimes', 'nullable', 'string', 'exists:coupons,code'],
            'note' => ['sometimes', 'nullable', 'string', 'max:500'],
        ];
    }
}
