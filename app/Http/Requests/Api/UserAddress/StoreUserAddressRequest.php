<?php

namespace App\Http\Requests\Api\UserAddress;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserAddressRequest extends FormRequest
{

    public function rules()
    {
        return [
            'name' => ["required", "string"],
            'description' => ["required", "string"],
            'latitude' => ["required", "numeric", "between:-90,90"],
            'longitude' => ["required", "numeric", "between:-180,180"],
            'address' => ["nullable", "string"]
        ];
    }
}
