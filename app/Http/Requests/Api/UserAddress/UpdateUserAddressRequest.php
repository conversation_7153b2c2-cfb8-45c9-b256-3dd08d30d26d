<?php

namespace App\Http\Requests\Api\UserAddress;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserAddressRequest extends FormRequest
{
   
    public function rules()
    {

        return [
            'name' => ["nullable","string","max:255"],
            'description' => ["nullable","string","max:255"],
            'latitude' => ["nullable","numeric","between:-90,90"],
            'longitude' => ["nullable","numeric","between:-180,180"]
        ] ;
    }
}
