<?php

namespace App\Http\Requests\Api\Post;

use Illuminate\Foundation\Http\FormRequest;

class StorePostRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'title' => ["required","string"],
            'content' => ["required","string"],
            'user_id' => ["required","integer","exists:users,id"],
            'category_id' => ["required","integer","exists:categories,id"],
            'postable_type' => ["required","string"],
            'postable_id' => ["required","integer"]
        ] + $langRules;
    }
}
