<?php

namespace App\Http\Requests\Api\Post;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePostRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'title' => ["nullable","string"],
            'content' => ["nullable","string"],
            'user_id' => ["nullable","integer","exists:users,id"],
            'category_id' => ["nullable","integer","exists:categories,id"],
            'postable_type' => ["nullable","string"],
            'postable_id' => ["nullable","integer"]
        ] + $langRules;
    }
}
