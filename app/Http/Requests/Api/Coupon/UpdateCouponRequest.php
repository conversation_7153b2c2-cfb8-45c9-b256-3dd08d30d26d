<?php

namespace App\Http\Requests\Api\Coupon;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCouponRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required","string"];
            $langRules["$locale.descrption"] = ["required","string"];
            }

        return [
            'code' => ["nullable","string"],
            'usage_limit_per_user' => ["nullable","integer"],
            'start_at' => ["nullable","date"],
            'end_at' => ["nullable","date"],
            'type' => ["nullable","string"],
            'value' => ["nullable","numeric"]
        ] + $langRules;
    }
}
