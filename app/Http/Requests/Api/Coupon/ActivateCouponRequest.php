<?php

namespace App\Http\Requests\Api\Coupon;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ActivateCouponRequest extends FormRequest
{
   
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::exists('coupons', 'code')->where(function ($query) {
                    $query->where('start_at', '<=', now())
                          ->where('end_at', '>=', now());
                })
            ]
        ];
    }
}
