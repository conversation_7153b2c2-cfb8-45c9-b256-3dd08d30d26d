<?php

namespace App\Http\Requests\Api\CategoryUser;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'user_id' => ["required","integer","exists:users,id"],
            'category_id' => ["required","integer","exists:categories,id"]
        ] + $langRules;
    }
}
