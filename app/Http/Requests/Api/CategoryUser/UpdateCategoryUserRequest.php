<?php

namespace App\Http\Requests\Api\CategoryUser;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'user_id' => ["nullable","integer","exists:users,id"],
            'category_id' => ["nullable","integer","exists:categories,id"]
        ] + $langRules;
    }
}
