<?php

namespace App\Http\Requests\Api\CarType;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCarTypeRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required","string"];
            }

        return [
            'km_range' => ["nullable","numeric"]
        ] + $langRules;
    }
}
