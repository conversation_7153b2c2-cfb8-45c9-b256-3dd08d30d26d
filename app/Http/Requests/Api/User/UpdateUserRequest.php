<?php

namespace App\Http\Requests\Api\User;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'first_name' => ["nullable","string"],
            'last_name' => ["nullable","string"],
            'email' => ["nullable","string"],
            'phone' => ["nullable","string"],
            'phone_verified_at' => ["nullable","date"],
            'password' => ["nullable","string"],
            'userable_type' => ["nullable","string"],
            'userable_id' => ["nullable","integer"],
            'is_active' => ["nullable","integer"],
            'remember_token' => ["nullable","string"]
        ] + $langRules;
    }
}
