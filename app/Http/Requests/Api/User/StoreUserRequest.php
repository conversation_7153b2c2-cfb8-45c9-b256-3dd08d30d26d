<?php

namespace App\Http\Requests\Api\User;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'first_name' => ["required","string"],
            'last_name' => ["required","string"],
            'email' => ["required","string"],
            'phone' => ["required","string"],
            'phone_verified_at' => ["required","date"],
            'password' => ["required","string"],
            'userable_type' => ["required","string"],
            'userable_id' => ["required","integer"],
            'is_active' => ["required","integer"],
            'remember_token' => ["required","string"]
        ] + $langRules;
    }
}
