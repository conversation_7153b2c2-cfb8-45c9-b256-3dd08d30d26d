<?php

namespace App\Http\Requests\Api\Category;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required","string"];
            $langRules["$locale.description"] = ["required","string"];
            }

        return [
            
        ] + $langRules;
    }
}
