<?php

namespace App\Http\Requests\Api\Pricing;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePricingRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'shift_id' => ["nullable","integer","exists:shifts,id"],
            'car_type_id' => ["nullable","integer","exists:car_types,id"],
            'minimum_fare' => ["nullable","numeric"],
            'flag_down_fee' => ["nullable","numeric"],
            'km_price' => ["nullable","numeric"],
            'miu_price' => ["nullable","numeric"]
        ] + $langRules;
    }
}
