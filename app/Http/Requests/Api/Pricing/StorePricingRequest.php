<?php

namespace App\Http\Requests\Api\Pricing;

use Illuminate\Foundation\Http\FormRequest;

class StorePricingRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'shift_id' => ["required","integer","exists:shifts,id"],
            'car_type_id' => ["required","integer","exists:car_types,id"],
            'minimum_fare' => ["required","numeric"],
            'flag_down_fee' => ["nullable","numeric"],
            'km_price' => ["required","numeric"],
            'miu_price' => ["required","numeric"]
        ] + $langRules;
    }
}
