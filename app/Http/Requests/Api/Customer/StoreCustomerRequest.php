<?php

namespace App\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'user_id' => ["required","integer","exists:users,id"],
            'points' => ["required","integer"],
            'refer_code' => ["required","string"]
        ] + $langRules;
    }
}
