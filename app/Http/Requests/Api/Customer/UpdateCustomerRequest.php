<?php

namespace App\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'user_id' => ["nullable","integer","exists:users,id"],
            'points' => ["nullable","integer"],
            'refer_code' => ["nullable","string"]
        ] + $langRules;
    }
}
