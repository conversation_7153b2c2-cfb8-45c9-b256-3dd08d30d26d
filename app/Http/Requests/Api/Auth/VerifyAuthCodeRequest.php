<?php

namespace App\Http\Requests\Api\Auth;

use App\Rules\PhoneExistsInUsersOrTempAuth;
use App\Rules\PhoneExistsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VerifyAuthCodeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone' => ['required', 'string',new PhoneExistsInUsersOrTempAuth],
            'code' => ['required', 'digits:5'],
            'fcm_token' => ['string']
        ];
    }
}