<?php

namespace App\Http\Requests\Api\Auth;

use App\Enums\UserGenderEnum;
use App\Rules\PhoneExistsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['nullable','string', 'max:25'],
            'last_name'  => ['nullable','string', 'max:25'],
            'email'      => ['nullable', 'string', 'email'],
            'phone'      => ['nullable','string', 'max:12','regex:/^963\d{9}$/', Rule::unique('users','phone')->ignore($this->user()->id)],
            'birthdate'      => ['nullable','date'],
            'gender'      => ['nullable',Rule::enum(UserGenderEnum::class)],
            'profile_picture' => ['nullable', 'image', 'mimes:jpg,jpeg,png', 'max:2048'], // Max size 2MB
        ];
    }
}