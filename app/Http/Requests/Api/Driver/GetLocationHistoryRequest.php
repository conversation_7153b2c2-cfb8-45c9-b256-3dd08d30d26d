<?php

namespace App\Http\Requests\Api\Driver;

use App\Helpers\APIResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class GetLocationHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Any authenticated user can access this endpoint
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'driver_id' => 'nullable|exists:drivers,id',
            'ride_id' => 'nullable|exists:rides,id',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
            'limit' => 'nullable|integer|min:1|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'driver_id.exists' => __('Driver not found'),
            'ride_id.exists' => __('Ride not found'),
            'start_time.date' => __('Start time must be a valid date'),
            'end_time.date' => __('End time must be a valid date'),
            'end_time.after_or_equal' => __('End time must be after or equal to start time'),
            'limit.integer' => __('Limit must be an integer'),
            'limit.min' => __('Limit must be at least 1'),
            'limit.max' => __('Limit cannot exceed 1000'),
        ];
    }
}
