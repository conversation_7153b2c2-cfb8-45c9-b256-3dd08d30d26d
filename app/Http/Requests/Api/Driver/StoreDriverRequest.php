<?php

namespace App\Http\Requests\Api\Driver;

use App\Enums\UserGenderEnum;
use App\Rules\PhoneExistsInUsersOrTempAuth;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreDriverRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        
           return[
            'phone' => ['required','string','regex:/^963\d{9}$/',Rule::unique('users','phone')], 
            'first_name' => ['required', 'string', 'max:25'],
            'last_name'  => ['required', 'string', 'max:25'],
            'email'      => ['nullable', 'string', 'email'],
            'birthdate'      => ['nullable', 'date'],
            'gender'      => ['required', Rule::enum(UserGenderEnum::class)],
            'profile_picture' => ['required', 'image', 'mimes:jpg,jpeg,png', 'max:5048'], 
            'license_number' => ['required', 'string', 'unique:drivers'],
            'car_type_ids' => ['nullable', 'array'],
            'car_type_ids.*' => ['integer', 'exists:car_types,id'],
            'region_id' => ['required','integer','exists:regions,id'],
            'model' => ['required', 'string'],
            'plate_number' => ['required', 'string', 'unique:vehicles,plate_number'],
            'color' => ['required', 'string'],
            'seats' => ['required', 'integer', 'min:1'],
            'manufacturing_year' => ['nullable', 'integer', 'digits:4'],
            'front_license_image' => ['required', 'image', 'mimes:jpg,jpeg,png','max:5048'],
            'back_license_image' => ['required', 'image', 'mimes:jpg,jpeg,png','max:5048'],
            'car_image' => ['required', 'image', 'mimes:jpg,jpeg,png','max:5048'],
            'car_mechanics_image' => ['required', 'image', 'mimes:jpg,jpeg,png','max:5048'],
             ] + $langRules;
        
    }

   
}
