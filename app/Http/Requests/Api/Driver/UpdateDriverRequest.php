<?php

namespace App\Http\Requests\Api\Driver;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDriverRequest extends FormRequest
{
    public function rules(): array
    {
        $driver = $this->user()->userable;

        return [
            'phone' => ['required','string','regex:/^963\d{9}$/',Rule::unique('users','phone')->ignore($this->user()->id)], 
            'first_name' => ['sometimes', 'string', 'max:255'],
            'last_name' => ['sometimes', 'string', 'max:255'],
            'license_number' => [
                'sometimes',
                'string',
                Rule::unique('drivers')->ignore($driver->id)
            ],
            'car_type_ids' => ['sometimes', 'array'],
            'car_type_ids.*' => ['integer', 'exists:car_types,id'],
            'region_id' => ['sometimes', 'integer', 'exists:regions,id'],
            'model' => ['sometimes', 'string'],
            'plate_number' => [
                'sometimes',
                'string',
                Rule::unique('vehicles')->ignore($driver->vehicle->id ?? null)
            ],
            'color' => ['sometimes', 'string'],
            'seats' => ['sometimes', 'integer', 'min:1'],
            'manufacturing_year' => ['sometimes', 'integer', 'digits:4'],
            'front_license_image' => ['sometimes', 'image', 'mimes:jpg,jpeg,png'],
            'back_license_image' => ['sometimes', 'image', 'mimes:jpg,jpeg,png'],
        ];
    }
}