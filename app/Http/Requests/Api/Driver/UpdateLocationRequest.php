<?php

namespace App\Http\Requests\Api\Driver;

use App\Helpers\APIResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateLocationRequest extends FormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'heading' => 'nullable|numeric|between:0,360',
            'is_online' => 'nullable|boolean',
            'address' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'latitude.required' => __('Latitude is required'),
            'latitude.numeric' => __('Latitude must be a number'),
            'latitude.between' => __('Latitude must be between -90 and 90'),
            'longitude.required' => __('Longitude is required'),
            'longitude.numeric' => __('Longitude must be a number'),
            'longitude.between' => __('Longitude must be between -180 and 180'),
            'heading.numeric' => __('Heading must be a number'),
            'heading.between' => __('Heading must be between 0 and 360'),
            'is_online.boolean' => __('Is online must be a boolean'),
            'address.string' => __('Address must be a string'),
            'address.max' => __('Address must not exceed 255 characters'),
        ];
    }

    
}
