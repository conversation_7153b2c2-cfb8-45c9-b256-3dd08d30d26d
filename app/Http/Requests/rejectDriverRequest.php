<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class rejectDriverRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['nullable', 'exists:drivers,id'],
            'refuse_reason' => ['required', 'string'],
        ];
    }
}
