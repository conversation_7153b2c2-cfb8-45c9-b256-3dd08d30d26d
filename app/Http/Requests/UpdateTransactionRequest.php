<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.description"] = ["nullable", "string"];
        }

        return [
            'id' => ["required", "exists:transactions,id"],
            'amount' => ["nullable", "numeric"],
            'purpose' => ["nullable", "string"],
            'status' => ["nullable", "string", "in:pending,success,failed"],
        ] + $langRules;
    }
}
