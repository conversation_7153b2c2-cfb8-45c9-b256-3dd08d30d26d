<?php

namespace App\Http\Requests;

use App\Enums\CouponTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCouponRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required", "string", "max:255"];
            $langRules["$locale.descrption"] = ["required", "string"];
        }

        $rules = [
            'id' => ["nullable", "exists:coupons,id"],
            'code' => [
                "required", 
                "string", 
                "max:50",
                Rule::unique('coupons', 'code')->ignore($this->id)
            ],
            'usage_limit_per_user' => ["required", "integer", "min:1"],
            'start_at' => ["required", "date"],
            'end_at' => ["required", "date", "after:start_at"],
            'type' => ["required", Rule::enum(CouponTypeEnum::class)],
            'value' => ["required", "numeric", "min:0"]
        ];

        // Add additional validation for percentage type
        if ($this->input('type') === CouponTypeEnum::PERCENTAGE->value) {
            $rules['value'][] = "max:100";
        }

        return $rules + $langRules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'code.required' => __('The coupon code is required.'),
            'code.unique' => __('This coupon code is already in use.'),
            'usage_limit_per_user.required' => __('The usage limit per user is required.'),
            'usage_limit_per_user.min' => __('The usage limit must be at least 1.'),
            'start_at.required' => __('The start date is required.'),
            'end_at.required' => __('The end date is required.'),
            'end_at.after' => __('The end date must be after the start date.'),
            'type.required' => __('The coupon type is required.'),
            'value.required' => __('The coupon value is required.'),
            'value.min' => __('The coupon value must be at least 0.'),
            'value.max' => __('The percentage value cannot exceed 100.'),
        ];
    }
}
