<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePricingRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'id' => ["nullable","exists:pricings,id"],
            'shift_id' => ["required_without:id","integer","exists:shifts,id"],
            'car_type_id' => ["required_without:id","integer","exists:car_types,id"],
            'minimum_fare' => ["required_without:id","numeric"],
            'flag_down_fee' => ['nullable',"numeric"],
            'km_price' => ["required_without:id","numeric"],
            'miu_price' => ["required_without:id","numeric"]
        ] + $langRules;
    }
}
