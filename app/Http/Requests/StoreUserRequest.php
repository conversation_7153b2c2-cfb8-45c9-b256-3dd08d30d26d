<?php

namespace App\Http\Requests;

use App\Enums\UserGenderEnum;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        $id = request()->input('id');
        return [
            'id' => ["nullable","exists:users,id"],
            'first_name' => ["required_without:id","string"],
            'last_name' => ["required_without:id","string"],
            'email' => ["nullable","string",Rule::unique('users','email')->ignore($id)],
            'phone' => ["required_without:id","string"],
            'phone_verified_at' => ["nullable","date"],
            'password' => ["nullable","string"],
            'userable_type' => ["nullable","string"],
            'userable_id' => ["nullable","integer"],
            'is_active' => ["nullable","boolean"],
            'birthdate'      => ['required_without:id', 'date','before:today'],
            'profile' => ['nullable', 'image', 'mimes:jpg,jpeg,png'],

            'gender'      => ['required_without:id', Rule::enum(UserGenderEnum::class)],
        ] + $langRules;
    }
}
