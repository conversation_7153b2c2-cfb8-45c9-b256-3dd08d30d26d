<?php

namespace App\Http\Requests\Post;

use Illuminate\Foundation\Http\FormRequest;

class StorePostRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'id' => ["nullable","exists:posts,id"],
            'title' => ["required_without:id","string"],
            'content' => ["required_without:id","string"],
            'user_id' => ["required_without:id","integer","exists:users,id"],
            'category_id' => ["required_without:id","integer","exists:categories,id"],
            'postable_type' => ["nullable","string"],
            'postable_id' => ["required_without:id","integer"]
        ] + $langRules;
    }
}
