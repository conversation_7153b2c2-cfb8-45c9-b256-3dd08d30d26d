<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRegionRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        foreach (config("app._availableLocale") as $locale) {
            $langRules["$locale.name"] = ["required_without:id","string"];
            }

        return [
            'id' => ["nullable","exists:regions,id"]
        ] + $langRules;
    }
}
