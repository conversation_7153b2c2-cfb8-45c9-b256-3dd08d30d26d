<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use App\Models\Admin;

class StoreAdminRequest extends StoreUserRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $langRules = [];
        $parentRules = parent::rules();

        // Get the ID from the request
        $id = request()->input('id');
        $adminId = request()->route('admin');

        // If we're updating an existing admin
        if ($id || $adminId) {
            // Find the admin and get the associated user ID
            $admin = null;
            if ($adminId && is_object($adminId)) {
                $admin = $adminId;
            } elseif ($id) {
                $admin = Admin::find($id);
            }

            // If we found the admin and it has a user
            if ($admin && $admin->user_id) {
                $userId = $admin->user_id;

                // Modify the unique rules to ignore the current user
                $parentRules['email'] = ['nullable', 'string', Rule::unique('users', 'email')->ignore($userId)];
                $parentRules['phone'] = ['required', 'string', Rule::unique('users', 'phone')->ignore($userId)];
            }
        }

        return [
            'roles' => ['nullable', 'array', 'exists:roles,id']
        ] + $langRules + $parentRules;
    }
}
