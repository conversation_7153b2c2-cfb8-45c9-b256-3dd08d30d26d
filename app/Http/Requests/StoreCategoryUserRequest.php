<?php

namespace App\Http\Requests\CategoryUser;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryUserRequest extends FormRequest
{
   
    public function rules()
    {
        $langRules = [];

        

        return [
            'id' => ["nullable","exists:category_user,id"],
            'user_id' => ["required_without:id","integer","exists:users,id"],
            'category_id' => ["required_without:id","integer","exists:categories,id"]
        ] + $langRules;
    }
}
