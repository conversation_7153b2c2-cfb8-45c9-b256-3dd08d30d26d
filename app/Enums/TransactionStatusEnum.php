<?php

namespace App\Enums;

enum TransactionStatusEnum: string
{
    case PENDING = 'pending';  
    case SUCCESS = 'success';
    case FAILED = 'failed';
    
    public function label(): string
    {
        return match ($this) {
            self::PENDING => __('pending'),
            self::SUCCESS => __('success'),
            self::FAILED => __('failed')
        };
    }
    
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::SUCCESS => 'success',
            self::FAILED => 'danger'
        };
    }
}