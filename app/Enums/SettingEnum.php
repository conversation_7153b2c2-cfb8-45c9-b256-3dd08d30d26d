<?php

namespace App\Enums;

enum SettingEnum: string
{

    case FACEBOOK = 'facebook';
    case LINKEDIN = 'linkedin';
    case YOUTUBE = 'youtube';
    case PHONE = 'phone';
    case TWITTER = 'twitter';
    case EMAIL = 'email';
    case ADDRESS = 'address';
    case GOOGLE_PLAY = 'google_play';
    case APP_STORE = 'app_store';
    case ABOUT_US = 'about_us';
    case PRIVACY_POLICY = 'privacy_policy';
    case TERMS_CONDITION = 'Terms_Conditions';
        // case DRIVER_COMMISSION = 'driver_commission';
    case COMPANY_COMMISSION = 'company_commission';

    case DRIVER_ANDROID_CURRENT_VERSION = 'driver_android_current_version';
    case DRIVER_ANDROID_MINIMAL_VERSION = 'driver_android_minimal_version';
    case DRIVER_ANDROID_FORCE_UPDATE = 'driver_android_force_update';

    case DRIVER_IOS_CURRENT_VERSION = 'driver_ios_current_version';
    case DRIVER_IOS_MINIMAL_VERSION = 'driver_ios_minimal_version';
    case DRIVER_IOS_FORCE_UPDATE = 'driver_ios_force_update';

    case USER_ANDROID_CURRENT_VERSION = 'user_android_current_version';
    case USER_ANDROID_MINIMAL_VERSION = 'user_android_minimal_version';
    case USER_ANDROID_FORCE_UPDATE = 'user_android_force_update';

    case USER_IOS_CURRENT_VERSION = 'user_ios_current_version';
    case USER_IOS_MINIMAL_VERSION = 'user_ios_minimal_version';
    case USER_IOS_FORCE_UPDATE = 'user_ios_force_update';

    public static function types()
    {
        return [
            self::FACEBOOK->value => 'url',
            self::YOUTUBE->value => 'url',
            self::LINKEDIN->value => 'url',
            self::TWITTER->value => 'url',
            self::PHONE->value => 'tel',
            self::EMAIL->value => 'email',
            self::ADDRESS->value => 'text',
            self::ABOUT_US->value => 'textarea',
            self::PRIVACY_POLICY->value => 'textarea',
            self::TERMS_CONDITION->value => 'textarea',

            self::APP_STORE->value => 'url',
            self::GOOGLE_PLAY->value => 'url',
            // self::DRIVER_COMMISSION->value => 'number',
            self::COMPANY_COMMISSION->value => 'number',

            self::DRIVER_ANDROID_CURRENT_VERSION->value => 'text',
            self::DRIVER_ANDROID_MINIMAL_VERSION->value => 'text',
            self::DRIVER_ANDROID_FORCE_UPDATE->value => 'checkbox',

            self::DRIVER_IOS_CURRENT_VERSION->value => 'number',
            self::DRIVER_IOS_MINIMAL_VERSION->value => 'number',
            self::DRIVER_IOS_FORCE_UPDATE->value => 'checkbox',

            self::USER_ANDROID_CURRENT_VERSION->value => 'number',
            self::USER_ANDROID_MINIMAL_VERSION->value => 'number',
            self::USER_ANDROID_FORCE_UPDATE->value => 'checkbox',

            self::USER_IOS_CURRENT_VERSION->value => 'number',
            self::USER_IOS_MINIMAL_VERSION->value => 'number',
            self::USER_IOS_FORCE_UPDATE->value => 'checkbox',
        ];
    }
    public static function labels()
    {
        return [
            self::FACEBOOK->value => __('Facebook URL'),
            self::LINKEDIN->value => __('Linkedin URL'),
            self::YOUTUBE->value => __('Youtube URL'),
            self::PHONE->value => __('Phone Number'),
            self::TWITTER->value => __('Twitter URL'),
            self::EMAIL->value => __('Email'),
            self::ADDRESS->value => __('Address'),
            self::GOOGLE_PLAY->value => __('Google Play URL'),
            self::APP_STORE->value => __('App Store URL'),
            self::ABOUT_US->value => __('About Us'),
            self::PRIVACY_POLICY->value => __('Privacy Policy'),
            self::TERMS_CONDITION->value => __('Terms & Conditions'),
            self::COMPANY_COMMISSION->value => __('Company Commission %'),

            self::DRIVER_ANDROID_CURRENT_VERSION->value => __('Driver Android Current Version'),
            self::DRIVER_ANDROID_MINIMAL_VERSION->value => __('Driver Android Minimal Version'),
            self::DRIVER_ANDROID_FORCE_UPDATE->value => __('Driver Android Force Update'),

            self::DRIVER_IOS_CURRENT_VERSION->value => __('Driver iOS Current Version'),
            self::DRIVER_IOS_MINIMAL_VERSION->value => __('Driver iOS Minimal Version'),
            self::DRIVER_IOS_FORCE_UPDATE->value => __('Driver iOS Force Update'),

            self::USER_ANDROID_CURRENT_VERSION->value => __('User Android Current Version'),
            self::USER_ANDROID_MINIMAL_VERSION->value => __('User Android Minimal Version'),
            self::USER_ANDROID_FORCE_UPDATE->value => __('User Android Force Update'),

            self::USER_IOS_CURRENT_VERSION->value => __('User iOS Current Version'),
            self::USER_IOS_MINIMAL_VERSION->value => __('User iOS Minimal Version'),
            self::USER_IOS_FORCE_UPDATE->value => __('User iOS Force Update'),
        ];
    }
    public static function label($value)
    {
        return self::labels()[$value];
    }

    public static function getType($value)
    {
        $types = self::types();
        return $types[$value];
    }
}
