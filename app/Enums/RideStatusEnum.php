<?php
namespace App\Enums;

enum RideStatusEnum : string
{
    case PENDING = 'pending';
    case ARRIVED = 'arrived';
    case ACCEPTED = 'accepted';
    case IN_PROGRESS = 'in_progress';
    case COMPLETED = 'completed';
    case CANCELED = 'canceled';
    

    public function label(): string
    {
        return match ($this) {
            self::PENDING => __('Pending'),
            self::ARRIVED => __('Arrived'),
            self::ACCEPTED => __('Accepted'),
            self::IN_PROGRESS => __('In Progress'),
            self::COMPLETED => __('Completed'),
            self::CANCELED => __('Canceled')
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'secondary',
            self::ARRIVED => 'info',
            self::ACCEPTED => 'primary',
            self::IN_PROGRESS => 'warning',
            self::COMPLETED => 'success',
            self::CANCELED => 'danger'
        };
    }
}
