<?php

namespace App\Enums;

enum DriverStatusEnum : string
{
    case PENDING = 'pending';    
    case APPROVED = 'approved'; 
    case REFUSED = 'refused';   


    public function label()
    {
        return match ($this) {
            self::PENDING => __('pending'),
            self::APPROVED => __('approved'),
            self::REFUSED => __('refused')
        };
    }
    public function color()
    {
        return match ($this) {
            self::PENDING => 'secondary',
            self::APPROVED => 'primary',
            self::REFUSED => 'danger'
        };
    }
   
}
