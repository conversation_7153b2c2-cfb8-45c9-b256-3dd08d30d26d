<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;




class Vehicle extends Model 
{
    use HasFactory;
    

    protected $fillable = [
        'driver_id','model', 'plate_number', 'color', 'seats', 'manufacturing_year'
    ];

    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }


    

    public function carType(): BelongsTo 
    {
        return $this->belongsTo(CarType::class, 'car_type_id', 'id');
    }


    public function driver(): BelongsTo 
    {
        return $this->belongsTo(Driver::class, 'driver_id', 'id');
    }


    public static function getPolymorphicTypes() 
    {
        return [

        ];
    }


}
