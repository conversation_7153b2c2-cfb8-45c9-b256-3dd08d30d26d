<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Customer extends Model
{
    use HasFactory;


    protected $fillable = [
        'user_id', 'points', 'refer_code'
    ];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [

    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }




    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function rides(): HasMany
    {
        return $this->hasMany(Ride::class, 'customer_id', 'id');
    }

    public function wallet(): MorphOne
    {
        return $this->morphOne(Wallet::class, 'walletable');
    }

    /**
     * Get all ratings given by this customer
     */
    public function givenRatings()
    {
        return $this->morphMany(Rating::class, 'rater');
    }

    /**
     * Get all ratings received by this customer
     */
    public function receivedRatings()
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    public static function getPolymorphicTypes()
    {
        return [

        ];
    }
 // Delete the associated User when the Customer is deleted
 protected static function booted()
 {
     static::deleted(function ($customer) {
         // Check if the user exists before deleting
         if ($customer->user) {
             $customer->user()->delete();
         }
     });
 }


}
