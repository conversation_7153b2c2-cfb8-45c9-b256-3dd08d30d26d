<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;




class Pricing extends Model
{
    use HasFactory;


    protected $fillable = [
        'shift_id',
        'car_type_id',
        'minimum_fare',
        'km_price',
        'miu_price',
        'flag_down_fee'
    ];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }


    protected $appends = ['car_type_name', 'shift_name'];

    public function getCarTypeNameAttribute()
    {
        return $this->carType->name;
    }

    public function getShiftNameAttribute()
    {
        return $this->shift->name;
    }

    public function carType(): BelongsTo
    {
        return $this->belongsTo(CarType::class, 'car_type_id', 'id');
    }


    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class, 'shift_id', 'id');
    }


    public static function getPolymorphicTypes()
    {
        return [];
    }
}
