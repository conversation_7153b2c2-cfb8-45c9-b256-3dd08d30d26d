<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DriverLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'driver_id',
        'ride_id',
        'latitude',
        'longitude',
        'heading',
        'speed',
        'address',
        'is_online'
    ];

    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'heading' => 'float',
        'speed' => 'float',
        'is_online' => 'boolean'
    ];

    /**
     * Get the driver that owns the location.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Get the ride associated with the location.
     */
    public function ride(): BelongsTo
    {
        return $this->belongsTo(Ride::class);
    }
}
