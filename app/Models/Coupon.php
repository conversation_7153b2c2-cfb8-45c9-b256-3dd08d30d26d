<?php

namespace App\Models;

use App\Enums\CouponTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Coupon extends Model implements TranslatableContract
{
    use HasFactory, Translatable;


    protected $fillable = [
        'code',
        'usage_limit_per_user',
        'start_at',
        'end_at',
        'type',
        'value'
    ];

    public $translatedAttributes = ['name', 'descrption'];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];
    protected $casts = [
        'type' => CouponTypeEnum::class
    ];


    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }


    public function scopeValidForActivation($query)
    {
        return $query->where('end_at', '>', now())
            ->where('start_at', '<=', now());
    }

    public function scopeValidForRide($query, Ride $ride)
    {
        return $query->where(function ($q) use ($ride) {
            $q->where('start_at', '<=', $ride->created_at)
                ->where('end_at', '>=', $ride->created_at);
        });
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_coupons')
            ->withPivot('times_used');
    }
    public function ride(): HasOne
    {
        return $this->hasOne(Ride::class, 'coupon_id');
    }

    public function calculatePriceAfterDiscount(float|int $final_price): float
    {
        return match ($this->type) {
            CouponTypeEnum::PERCENTAGE => $final_price - ($final_price * ($this->value / 100)),
            CouponTypeEnum::FIXED => $final_price - min($this->value, $final_price),
            default => 0
        };
    }

    public function calculateDiscount(float $final_price):int
    {
        return match ($this->type) {
            CouponTypeEnum::PERCENTAGE => $final_price * ($this->value / 100),
            CouponTypeEnum::FIXED => min($this->value, $final_price),
            default => 0
        };
    }

    public static function getPolymorphicTypes()
    {
        return [];
    }
}
