<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Admin extends Model 
{
    use HasFactory;
    

    protected $fillable = [
        'user_id'
    ];

    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }


    

    public function user(): BelongsTo 
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function initiator(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'initiatorable');
    }


    public static function getPolymorphicTypes() 
    {
        return [

        ];
    }

     // Delete the associated User when the Customer is deleted
 protected static function booted()
 {
     static::deleted(function ($admin) {
         // Check if the user exists before deleting
         if ($admin->user) {
             $admin->user()->delete();
         }
     });
 }


}
