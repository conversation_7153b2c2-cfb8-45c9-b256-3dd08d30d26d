<?php

namespace App\Models;

use App\Enums\RideStatusEnum;
use App\Enums\RideTypeEnum;
use App\Interfaces\RideStateInterface;
use App\Observers\Ride\RideObserver;
use App\States\AcceptedState;
use App\States\CanceledState;
use App\States\CompletedState;
use App\States\InProgressState;
use App\States\PendingState;
use App\States\ArrivedState;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

#[ObservedBy([RideObserver::class])]
class Ride extends Model implements TranslatableContract
{
    use HasFactory;
    use Translatable;


    protected $fillable = [
        'customer_id',
        'driver_id',
        'pricing_id',
        'coupon_id',
        'status',
        'estimated_distance',
        'actual_distance',
        'estimated_duration',
        'actual_duration',
        'fare',
        'final_price',
        'note',
        'cancel_reason',
        'scheduled_at',
        'requested_at',
        'driver_gender',
        'accepted_at',
        'started_at',
        'completed_at',
        'canceled_at',
        'type',
        'estimated_price'
    ];


    protected const RELATION_COLUMN = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        'name'
    ];

    protected $casts = [
        'status' => RideStatusEnum::class,
        'type' => RideTypeEnum::class,
        'scheduled_at' => 'datetime',
        'requested_at' => 'datetime',
        'accepted_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'canceled_at' => 'datetime'
    ];

    public function state(): RideStateInterface
    {
        return match ($this->status) {
            RideStatusEnum::PENDING => new PendingState($this),
            RideStatusEnum::ACCEPTED => new AcceptedState($this),
            RideStatusEnum::ARRIVED => new ArrivedState($this),
            RideStatusEnum::IN_PROGRESS => new InProgressState($this),
            RideStatusEnum::COMPLETED => new CompletedState($this),
            RideStatusEnum::CANCELED => new CanceledState($this),
        };
    }
    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }
    protected function StartedAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }
    protected function ScheduledAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }
    protected function RequestedAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }
    protected function CompletedAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }
    protected function CanceledAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null,
        );
    }



    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class, 'coupon_id', 'id');
    }


    public function pricing(): BelongsTo
    {
        return $this->belongsTo(Pricing::class, 'pricing_id', 'id');
    }

    public function points()
    {
        return $this->hasMany(RidePoint::class, 'ride_id');
    }
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class, 'driver_id', 'id');
    }

    public static function getPolymorphicTypes()
    {
        return [];
    }


    public function getPricingDetailsAttribute(): array
    {
        $currentTime = now('Asia/Damascus')->format('H:i:s');

        $shift = Shift::where(function ($query) use ($currentTime): void {
            $query->whereRaw(
                "CASE
            WHEN start_time < end_time THEN
                ? BETWEEN start_time AND end_time
            ELSE
                ? >= start_time OR ? <= end_time
            END",
                [$currentTime, $currentTime, $currentTime]
            );
        })->first();

        if (!$shift) {
            return [];
        }

        return Pricing::with('carType')
            ->where('shift_id', $shift->id)
            ->get()
            ->map(function ($pricing) {
                return [
                    'car_type' => $pricing->carType->name,
                    'price' => $this->formatNumber(
                        $this->calculatePriceForType($pricing)
                    ),
                    'details' => [
                        'minimum_fare' => $pricing->minimum_fare,
                        'flag_down_fee' => $pricing->flag_down_fee,
                        'km_price' => $pricing->km_price,
                        'miu_price' => $this->formatNumber($pricing->miu_price)
                    ]
                ];
            })->toArray();
    }

    private function calculatePriceForType($pricing): float
    {
        $calculated =
            //  $pricing->flag_down_fee+
            ($this->estimated_distance * $pricing->km_price)
            + ($this->estimated_duration * $pricing->miu_price);

        return max($calculated, $pricing->minimum_fare);
    }

    private function formatNumber($value): string
    {
        return number_format((float) $value, 0, '.', ',');
    }

    /**
     * Get all driver locations for this ride.
     */
    public function driverLocations()
    {
        return $this->hasMany(DriverLocation::class);
    }

    /**
     * Get the latest driver location for this ride.
     */
    public function latestDriverLocation()
    {
        return $this->hasOne(DriverLocation::class)->latest();
    }


    public function customerRating()
    {
        return $this->hasOne(Rating::class, 'ride_id', 'id')->where('rateable_type', Customer::class);
    }

    public function driverRating()
    {
        return $this->hasOne(Rating::class, 'ride_id', 'id')->where('rateable_type', Driver::class);
    }
}
