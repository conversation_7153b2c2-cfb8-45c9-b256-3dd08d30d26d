<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;


use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;


class Region extends Model implements TranslatableContract
{
    use HasFactory;
    use Translatable;


    protected $fillable = [
        
    ];

    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        'name'
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_region');
    }
    

    public static function getPolymorphicTypes() 
    {
        return [

        ];
    }


}
