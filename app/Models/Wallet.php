<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Wallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'balance',
        'is_active',
        'walletable_id',
        'walletable_type'
    ];

    public function walletable()
    {
        return $this->morphTo();
    }

    protected $translatedAttributes = [

    ];
    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d') : null,
        );
    }
    protected function updatedAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): ?string => $value ? Carbon::parse($value)->format('Y-m-d') : null,
        );
    }

    public function sentTransactions()
    {
        return $this->hasMany(Transaction::class, 'sender_wallet_id');
    }

    public function receivedTransactions()
    {
        return $this->hasMany(Transaction::class, 'receiver_wallet_id');
    }
    public function transactions()
    {
        return Transaction::where('sender_wallet_id', $this->id)
            ->orWhere('receiver_wallet_id', $this->id);
    }

    public static function getPolymorphicTypes()
    {
        return [
            \App\Models\Driver::class,
            \App\Models\Customer::class,
            \App\Models\Company::class,
        ];
    }

}

