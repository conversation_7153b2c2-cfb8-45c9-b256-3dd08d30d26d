<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;




class Rating extends Model
{
    use HasFactory;


    protected $fillable = [
        'ride_id',
        'rater_id',
        'rater_type',
        'rateable_id',
        'rateable_type',
        'rating',
        'comment'
    ];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [

    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }




    /**
     * Get the ride that was rated
     */
    public function ride(): BelongsTo
    {
        return $this->belongsTo(Ride::class);
    }

    /**
     * Get the entity that gave the rating (polymorphic)
     */
    public function rater(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the entity that was rated (polymorphic)
     */
    public function rateable(): MorphTo
    {
        return $this->morphTo();
    }


    public static function getPolymorphicTypes()
    {
        return [

        ];
    }


}
