<?php

namespace App\Models;

use App\Enums\UserGenderEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\MediaLibrary\HasMedia;
use App\Traits\HasMediaTrait;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\HasApiTokens;
use Modules\Notification\Traits\HasNotification;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements HasMedia
{
    use HasFactory;
    use HasMediaTrait;
    use HasApiTokens;
    use HasRoles;
    use HasNotification;



    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'phone_verified_at',
        'password',
        'userable_type',
        'userable_id',
        'is_active',
        'birthdate',
        'gender',
        'code'
    ];


    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'gender' => UserGenderEnum::class
    ];

    protected $appends = ['full_name'];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }
    protected function birthdate(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }

    public function fullName(): string
    {
        return ucfirst($this->first_name) . ' ' . ucfirst($this->last_name);
    }


    public function getFullNameAttribute()
    {
        return ucfirst($this->first_name) . ' ' . ucfirst($this->last_name);
    }
    public function markPhoneAsVerified(): bool
    {
        return $this->forceFill([
            'phone_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    public function setPasswordAttribute($password)
    {
        $this->attributes['password'] = Hash::make($password);
    }


    public static function getPolymorphicTypes()
    {
        return [
            'App\Models\Driver',
            'App\Models\Customer',
            'App\Models\Admin',
        ];
    }


    public function userable(): MorphTo
    {
        return $this->morphTo('userable');
    }
    public function driver(): HasOne
    {
        return $this->hasOne(Driver::class, 'user_id', 'id');
    }
    public function admin(): HasOne
    {
        return $this->hasOne(Admin::class, 'user_id', 'id');
    }


    public function deviceTokens(): HasMany
    {
        return $this->hasMany(DeviceToken::class, 'user_id', 'id');
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(UserAddress::class, 'user_id', 'id');
    }

    public function isCustomer(): bool
    {
        return $this->userable instanceof Customer;
    }

    public function isDriver(): bool
    {
        return $this->userable instanceof Driver;
    }

    public function coupons(): BelongsToMany
    {
        return $this->belongsToMany(Coupon::class, 'user_coupons')
            ->withPivot('times_used');
    }

    public function activeCoupons()
    {
        return $this->coupons()
            ->where('start_at', '<=', now())
            ->where('end_at', '>', now())
            ->whereRaw('user_coupons.times_used < coupons.usage_limit_per_user');
    }

    /**
     * Get all ratings given by this user
     */
    public function givenRatings()
    {
        return $this->morphMany(Rating::class, 'rater');
    }

    /**
     * Get all ratings received by this user
     */
    public function receivedRatings()
    {
        return $this->morphMany(Rating::class, 'rateable');
    }
}
