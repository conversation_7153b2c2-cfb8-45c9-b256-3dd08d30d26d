<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;




class RegionTranslation extends Model 
{
    use HasFactory;
    

    protected $fillable = [
        'name', 'region_id'
    ];

    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }


    

    public static function getPolymorphicTypes() 
    {
        return [

        ];
    }


}
