<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RidePoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'ride_id',
        'longitude',
        'latitude',
        'address',
        'type', 
        'sort_order',
    ];


    public function ride() : BelongsTo  {
        return $this->belongsTo(Ride::class);
    }
}
