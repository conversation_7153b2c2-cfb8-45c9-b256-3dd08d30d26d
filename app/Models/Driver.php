<?php

namespace App\Models;

use App\Enums\DriverStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;


use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Driver extends Model implements TranslatableContract, HasMedia
{
    use HasFactory;
    use Translatable;
    use  InteractsWithMedia;


    protected $fillable = [
        'user_id',
        'phone',
        'is_available',
        'license_number',
        'status',
        'current_latitude',
        'current_longitude',
        'refuse_reason'
    ];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [];

    protected $casts = [
        'status' => DriverStatusEnum::class
    ];
    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }




    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function vehicle(): HasOne
    {
        return $this->hasOne(Vehicle::class, 'driver_id','id');
    }
    public function rides(): HasMany
    {
        return $this->hasMany(Ride::class, 'driver_id','id');
    }
    public function carTypes(): BelongsToMany
    {
        return $this->belongsToMany(CarType::class, 'car_type_driver');
    }

    public function wallet(): MorphOne
    {
        return $this->morphOne(Wallet::class, 'walletable');
    }

    public function regions() : BelongsToMany
    {
        return $this->belongsToMany(Region::class, 'driver_region');
    }


    public static function getPolymorphicTypes()
    {
        return [];
    }

    public function scopeWhereAvailable($query)
    {
        return $query->where('is_available', true)
            ->whereNotNull('current_latitude')
            ->whereNotNull('current_longitude');
    }


    public function scopeWithinRadius($query, $latitude, $longitude, $radius)
    {
        return $query->whereRaw('
            6371 * ACOS(
                COS(RADIANS(?)) * COS(RADIANS(current_latitude)) *
                COS(RADIANS(current_longitude) - RADIANS(?)) +
                SIN(RADIANS(?)) * SIN(RADIANS(current_latitude))
            ) <= ?
        ', [$latitude, $longitude, $latitude, $radius]);
    }

    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class);
    }

    public function currentVehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class, 'current_vehicle_id');
    }

    /**
     * Get all ratings given by this driver
     */
    public function givenRatings()
    {
        return $this->morphMany(Rating::class, 'rater');
    }

    /**
     * Get all ratings received by this driver
     */
    public function receivedRatings()
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    /**
     * Get all locations for this driver.
     */
    public function locations()
    {
        return $this->hasMany(DriverLocation::class);
    }

    /**
     * Get the latest location for this driver.
     */
    public function latestLocation()
    {
       
        return $this->hasOne(DriverLocation::class)->latest();
    }

    
     // Delete the associated User when the Customer is deleted
 protected static function booted()
 {
     static::deleted(function ($driver) {
         // Check if the user exists before deleting
         if ($driver->user) {
             $driver->user()->delete();
         }
     });
 }
}
