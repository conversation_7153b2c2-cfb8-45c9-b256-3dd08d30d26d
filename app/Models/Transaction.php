<?php

namespace App\Models;

use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Transaction extends Model implements TranslatableContract
{
    use HasFactory;
    use Translatable;

    protected $fillable = [
        'sender_wallet_id',
        'receiver_wallet_id',
        'ride_id',
        'coupon_id',
        'payment_method',
        'status',
        'type',
        'reference',
        'purpose',
        'initiator_type',
        'initiator_id',
        'amount',
        'coupon_type',
        'coupon_amount',
    ];

    protected $casts = [
        'payment_method' => TransactionPaymentMethodEnum::class,
        'status' => TransactionStatusEnum::class,
        'type' => TransactionTypeEnum::class,
    ];
    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        'description'
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }

    public function senderWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'sender_wallet_id');
    }

    public function receiverWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'receiver_wallet_id');
    }

    public function ride(): BelongsTo
    {
        return $this->belongsTo(Ride::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }
    public function initiator(): MorphTo  
    {
        return $this->morphTo();
    }
    
    public static function getPolymorphicTypes(): array
    {
        return [
            'user' => \App\Models\User::class,
            'driver' => \App\Models\Driver::class,
            'admin' => \App\Models\Admin::class,
            'company' => \App\Models\Company::class,
        ];
    }


}
