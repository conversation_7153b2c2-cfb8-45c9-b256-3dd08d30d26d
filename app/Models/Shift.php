<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;



class Shift extends Model implements TranslatableContract
{
    use HasFactory;
    use Translatable;
    

    protected $fillable = [
        'start_time', 'end_time','minimum_fare','km_price','flag_down_fee','minute_price'
    ];

    
    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $translatedAttributes = [
        'name' ,'notes'
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn (string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }
   /**
     * Accessor for start_time (formats as 12-hour with translated AM/PM)
     */
    public function getStartTimeAttribute($value)
    {
        $time = Carbon::parse($value);
        $amPm = $time->format('A'); // Get AM/PM
        $translatedAmPm = __($amPm === 'AM' ? 'locale.am' : 'locale.pm'); // Translate AM/PM

        return $time->format('h:i') . ' ' . $translatedAmPm;
    }

    /**
     * Accessor for end_time (formats as 12-hour with translated AM/PM)
     */
    public function getEndTimeAttribute($value)
    {
        $time = Carbon::parse($value);
        $amPm = $time->format('A'); // Get AM/PM
        $translatedAmPm = __($amPm === 'AM' ? 'locale.am' : 'locale.pm'); // Translate AM/PM

        return $time->format('h:i') . ' ' . $translatedAmPm;
    }

    

    public static function getPolymorphicTypes() 
    {
        return [

        ];
    }


}
