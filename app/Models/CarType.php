<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;


use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;


class CarType extends Model implements TranslatableContract
{
    use HasFactory;
    use Translatable;


    protected $fillable = [
        'km_range',
        'is_available'
    ];


    protected const RELATION_COLUMN  = "id";

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'is_available' => 'boolean',
        'km_range' => 'decimal:1',
    ];

    protected $translatedAttributes = [
        'name', 'description'
    ];

    protected function createdAt(): Attribute
    {
        return Attribute::get(
            fn(string|null $value): string => Carbon::parse($value)->format('Y-m-d'),
        );
    }

    /**
     * Get the formatted km_range attribute with one decimal place and comma as separator
     *
     * @return string
     */
    public function getFormattedKmRangeAttribute(): string
    {
        return number_format((float)$this->km_range, 1, ',', '');
    }


    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'car_type_driver');
    }



    public static function getPolymorphicTypes()
    {
        return [];
    }
}
