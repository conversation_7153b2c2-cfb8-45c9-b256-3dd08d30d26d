<?php

namespace App\Policies;

use App\Enums\RideStatusEnum;
use App\Models\Driver;
use App\Models\Ride;
use App\Models\User;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\Log;

class RidePolicy
{
    public function apply(User $user, Ride $ride): bool
    {
        // Ensure the authenticated user is a driver and the ride has no driver assigned
        return $user->userable_type === Driver::class && $ride->driver_id === null;
    }

    public function start(User $user, Ride $ride): bool
    {
        
        return $user->userable_type === Driver::class && $ride->driver_id === $user->userable->id;
    }
    
    public function arrive(User $user, Ride $ride): bool
    {
        
        return $user->userable_type === Driver::class && $ride->driver_id === $user->userable->id;
    }

    public function complete(User $user, Ride $ride): bool
    {
        return $user->userable_type === Driver::class && $ride->driver_id === $user->userable->id;

    }

    public function cancel(User $user, Ride $ride): Response
    {
        if (($user->isCustomer() && $ride->customer_id === $user->userable->id) || ($user->isDriver() && $ride->driver_id !== $user->userable->id)) 
        {

           return  Response::deny('Unauthorized access to ride details');
        }
        return Response::allow();
       
    }

    /**
     * Determine if the user can view the ride details.
     */
    public function view(User $user, Ride $ride): Response
    {
        return ($user->isCustomer() && $ride->customer_id === $user->userable->id) ||
               ($user->isDriver() && $ride->driver_id === $user->userable->id)
            ? Response::allow()
            : Response::deny('Unauthorized access to ride details');
    }
}
