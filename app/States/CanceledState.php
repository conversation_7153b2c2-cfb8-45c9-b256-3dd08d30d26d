<?php

namespace App\States;

use App\Enums\RideStatusEnum;
use App\Models\Driver;

class CanceledState extends BaseRideState
{
    public function cancel(Driver $driver, string $reason): array
    {
        
        $this->ride->update([
            'status' => RideStatusEnum::CANCELED,
            'cancel_reason' => $reason,
        ]);

        return ['status' => 'success', 'message' => __('Ride canceled')];
    }
}
