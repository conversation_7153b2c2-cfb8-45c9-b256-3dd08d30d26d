<?php

namespace App\States;

use App\Enums\RideStatusEnum;
use App\Models\Driver;

class AcceptedState extends BaseRideState
{

    public function arrive(): array
    {
        $this->updateRideStatus(RideStatusEnum::ARRIVED);
        return ['status' => 'success'];
    }
    
    public function start(): array
    {
        $this->updateRideStatus(RideStatusEnum::IN_PROGRESS, [
            'started_at' => now(),
        ]);
        $this->updateDriverAvailability($this->ride->driver, false);

        return ['status' => 'success'];
    }

    public function cancel(Driver $driver, string $cancelReason): array
    {
        $this->updateRideStatus(RideStatusEnum::CANCELED, [
            'canceled_at' => now(),
            'cancel_reason' => $cancelReason,
        ]);
        $this->updateDriverAvailability($driver, true);

        return ['status' => 'success'];
    }
}