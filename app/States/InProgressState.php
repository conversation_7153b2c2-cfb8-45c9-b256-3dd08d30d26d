<?php

namespace App\States;

use App\Enums\RideStatusEnum;

class InProgressState extends BaseRideState
{
    public function complete(array $driverLocation): array
    {
        $this->updateRideStatus(RideStatusEnum::COMPLETED, [
            'completed_at' => now(),
        ]);
        
        $this->updateDriverAvailability($this->ride->driver, true);

        $this->ride->driver->update([
            'current_latitude' => $driverLocation['current_latitude'],
            'current_longitude' => $driverLocation['current_longitude'],
        ]);
        return ['status' => 'success'];
    }
}