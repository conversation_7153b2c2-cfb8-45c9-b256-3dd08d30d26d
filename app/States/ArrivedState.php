<?php

namespace App\States;

use App\Enums\RideStatusEnum;
use App\Models\Driver;
use App\Models\Ride;
use App\States\BaseRideState;

class ArrivedState extends BaseRideState
{
   
    public function start(): array
    {
        $this->updateRideStatus(RideStatusEnum::IN_PROGRESS, [
            'started_at' => now()
        ]);
        return ['status' => 'success'];
    }

    public function cancel(Driver $driver, string $cancelReason): array
    {
        $this->updateRideStatus(RideStatusEnum::CANCELED, [
            'canceled_at' => now(),
            'cancel_reason' => $cancelReason,
        ]);
        $this->updateDriverAvailability($driver, true);

        return ['status' => 'success'];
    }
}