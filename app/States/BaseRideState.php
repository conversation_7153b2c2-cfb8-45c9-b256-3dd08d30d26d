<?php

namespace App\States;

use App\Enums\RideStatusEnum;
use App\Interfaces\RideStateInterface;
use App\Models\Driver;
use App\Models\Ride;

abstract class BaseRideState implements RideStateInterface
{
    public function __construct(protected Ride $ride) {}

    protected function error(string $message): array
    {
        return ['status' => 'error', 'message' => $message];
    }

    protected function updateRideStatus(RideStatusEnum $status, array $additionalData = []): void
    {
        $this->ride->update(array_merge([
            'status' => $status,
        ], $additionalData));
    }

    protected function updateDriverAvailability(Driver $driver, bool $isAvailable): void
    {
        $driver->update(['is_available' => $isAvailable]);
    }

    // Default implementations for actions that are invalid in most states
    public function apply(Driver $driver): array
    {
        return $this->error(__('Ride cannot be applied in the current state ,Ride must be pending'));
    }

    public function arrive(): array
    {
        return $this->error(__('Ride cannot be arrived in the current state.Ride must be accepted'));
    }
    
    public function start(): array
    {
        return $this->error(__('Ride cannot be started in the current state.Ride must be accepted'));
    }
    

    public function complete(array $data): array
    {
        return $this->error(__('Ride cannot be completed in the current state.Ride must be in progress'));
    }

    public function cancel(Driver $driver, string $cancelReason): array
    {
        return $this->error(__('Ride cannot be canceled in the current state.Ride must be pending'));
    }
}
