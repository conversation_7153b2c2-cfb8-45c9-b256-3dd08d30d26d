<?php

namespace App\States;

use App\Enums\RideStatusEnum;
use App\Enums\RideTypeEnum;
use App\Models\Driver;

class PendingState extends BaseRideState
{
    public function apply(Driver $driver): array
    {
        $this->updateRideStatus(RideStatusEnum::ACCEPTED, [
            'driver_id' => $driver->id,
            'accepted_at' => now(),
        ]);

        if ($this->ride->type == RideTypeEnum::SCHEDULED){
            $this->updateDriverAvailability($this->ride->driver, true);
        }
        return ['status' => 'success'];
    }

    public function cancel(Driver $driver, string $cancelReason): array
    {
        $this->updateRideStatus(RideStatusEnum::CANCELED, [
            'canceled_at' => now(),
            'cancel_reason' => $cancelReason,
        ]);

        $this->updateDriverAvailability($this->ride->driver, true);


        return ['status' => 'success'];
    }
}