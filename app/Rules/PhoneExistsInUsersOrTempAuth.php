<?php

namespace App\Rules;

use App\Models\TempAuth;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PhoneExistsInUsersOrTempAuth implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!User::wherePhone($value)->exists() && !TempAuth::wherePhone($value)->exists()) {
            $fail(__('Incorrect phone number'));
        }  
    }
}
