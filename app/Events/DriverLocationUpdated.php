<?php

namespace App\Events;

use App\Models\DriverLocation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DriverLocationUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The driver location instance.
     *
     * @var DriverLocation
     */
    public $driverLocation;

    /**
     * Create a new event instance.
     */
    public function __construct(DriverLocation $driverLocation)
    {
        $this->driverLocation = $driverLocation;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('driver.'.$this->driverLocation->driver_id),
            // Add a public channel for testing
            new Channel('test-driver-location'),
        ];

        // If this location is associated with a ride, also broadcast on the ride-specific private channel
        if ($this->driverLocation->ride_id) {
            $channels[] = new PrivateChannel('ride.'.$this->driverLocation->ride_id);
        }

        // Log the channels we're broadcasting to
        \Illuminate\Support\Facades\Log::info('Broadcasting to channels', [
            'channels' => array_map(function($channel) {
                return $channel->name;
            }, $channels)
        ]);

        return $channels;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'location.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->driverLocation->id,
            'driver_id' => $this->driverLocation->driver_id,
            'ride_id' => $this->driverLocation->ride_id,
            'latitude' => $this->driverLocation->latitude,
            'longitude' => $this->driverLocation->longitude,
            'heading' => $this->driverLocation->heading,
            'speed' => $this->driverLocation->speed,
            'address' => $this->driverLocation->address,
            'is_online' => $this->driverLocation->is_online,
            'timestamp' => $this->driverLocation->created_at->toIso8601String(),
        ];
    }
}
