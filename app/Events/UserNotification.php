<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The user ID
     *
     * @var int
     */
    public $user_id;

    /**
     * The notification message
     *
     * @var string
     */
    public $message;

    /**
     * The notification type
     *
     * @var string
     */
    public $type;

    /**
     * Additional data
     *
     * @var array
     */
    public $data;

    /**
     * The timestamp
     *
     * @var string
     */
    public $timestamp;

    /**
     * Create a new event instance.
     *
     * @param int $user_id
     * @param string $message
     * @param string $type
     * @param array $data
     */
    public function __construct(int $user_id, string $message, string $type = 'info', array $data = [])
    {
        $this->user_id = $user_id;
        $this->message = $message;
        $this->type = $type;
        $this->data = $data;
        $this->timestamp = now()->toIso8601String();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('user.' . $this->user_id);
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'notification';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'user_id' => $this->user_id,
            'message' => $this->message,
            'type' => $this->type,
            'data' => $this->data,
            'timestamp' => $this->timestamp,
        ];
    }
}
