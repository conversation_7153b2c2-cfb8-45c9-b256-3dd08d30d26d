<?php

namespace App\Observers\Ride;

use App\Enums\RideStatusEnum;
use App\Jobs\Ride\SendRideAcceptedNotificationJob;
use App\Jobs\Ride\SendRideArrivedNotificationJob;
use App\Jobs\Ride\SendRideCanceledNotificationJob;
use App\Jobs\Ride\SendRideCompletedNotificationJob;
use App\Jobs\SendDriverStartedRideNotificationJob;
use App\Models\Driver;
use App\Models\Ride;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;

class RideObserver implements ShouldDispatchAfterCommit
{
    /**
     * Handle the Ride "created" event.
     */
    public function created(Ride $ride): void {}


    /**
     * Handle the Ride "updated" event.
     */
    public function updated(Ride $ride): void
    {
        if ($ride->isDirty('status')) {
            match ($ride->status) {
                RideStatusEnum::ACCEPTED => $this->handleAcceptedStatus($ride),
                RideStatusEnum::ARRIVED => $this->handleArrivedStatus($ride),
                RideStatusEnum::COMPLETED => $this->handleCompletedStatus($ride),
                RideStatusEnum::CANCELED => $this->handleCancelledStatus($ride),
                RideStatusEnum::IN_PROGRESS => $this->handleInProgressStatus($ride),
                default => null,
            };
        }
    }

    protected function handleAcceptedStatus(Ride $ride): void
    {
        $ride->loadMissing('driver.user', 'driver.vehicle');
        SendRideAcceptedNotificationJob::dispatch($ride);
    }

    protected function handleArrivedStatus(Ride $ride): void
    {
        $ride->loadMissing('driver.user', 'driver.vehicle', 'points');
        SendRideArrivedNotificationJob::dispatch($ride);
    }

    protected function handleCompletedStatus(Ride $ride): void
    {
        $ride->loadMissing('driver.user', 'driver.vehicle', 'points');
        SendRideCompletedNotificationJob::dispatch($ride);
    }

    protected function handleCancelledStatus(Ride $ride): void
    {
        $ride->loadMissing('customer.user', 'driver.user', 'points');
        SendRideCanceledNotificationJob::dispatch($ride);
    }

    protected function handleInProgressStatus(Ride $ride): void
    {
        $ride->loadMissing('customer.user', 'driver.user', 'driver.vehicle', 'points');
        SendDriverStartedRideNotificationJob::dispatch($ride);
    }

    /**
     * Handle the Ride "deleted" event.
     */
    public function deleted(Ride $ride): void
    {
        //
    }

    /**
     * Handle the Ride "restored" event.
     */
    public function restored(Ride $ride): void
    {
        //
    }

    /**
     * Handle the Ride "force deleted" event.
     */
    public function forceDeleted(Ride $ride): void
    {
        //
    }
}
