<?php

namespace App\Console\Commands;

use App\Events\UserNotification;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Pusher\Pusher;

class TestPusherConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pusher:test 
                            {--driver_id= : The ID of the driver to send a location update for}
                            {--user_id= : The ID of the user to send a notification to}
                            {--message= : The message to send in the notification}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Pusher connection by sending events';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Pusher connection...');

        // Check if Pusher is configured
        if (!config('broadcasting.connections.pusher.key') || 
            !config('broadcasting.connections.pusher.secret') || 
            !config('broadcasting.connections.pusher.app_id')) {
            $this->error('Pusher is not properly configured. Please check your .env file.');
            return 1;
        }

        try {
            // Create Pusher instance
            $pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options')
            );

            // Test connection
            $result = $pusher->trigger('test-channel', 'test-event', ['message' => 'This is a test']);
            
            if ($result) {
                $this->info('Successfully connected to Pusher and triggered a test event.');
            } else {
                $this->error('Failed to trigger test event.');
                return 1;
            }

            // Send driver location update if driver_id is provided
            if ($driverId = $this->option('driver_id')) {
                $driver = Driver::find($driverId);
                
                if (!$driver) {
                    $this->error("Driver with ID {$driverId} not found.");
                } else {
                    // Default location (Damascus, Syria)
                    $latitude = 33.5123;
                    $longitude = 36.2923;
                    
                    $data = [
                        'driver_id' => $driver->id,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'timestamp' => now()->toIso8601String(),
                    ];
                    
                    $result = $pusher->trigger(
                        'private-driver.' . $driver->id,
                        'location-updated',
                        $data
                    );
                    
                    if ($result) {
                        $this->info("Successfully sent location update for driver {$driver->id}.");
                    } else {
                        $this->error("Failed to send location update for driver {$driver->id}.");
                    }
                }
            }

            // Send user notification if user_id is provided
            if ($userId = $this->option('user_id')) {
                $user = User::find($userId);
                
                if (!$user) {
                    $this->error("User with ID {$userId} not found.");
                } else {
                    $message = $this->option('message') ?? 'This is a test notification from the command line.';
                    
                    $data = [
                        'user_id' => $user->id,
                        'message' => $message,
                        'type' => 'info',
                        'timestamp' => now()->toIso8601String(),
                    ];
                    
                    $result = $pusher->trigger(
                        'private-user.' . $user->id,
                        'notification',
                        $data
                    );
                    
                    if ($result) {
                        $this->info("Successfully sent notification to user {$user->id}.");
                    } else {
                        $this->error("Failed to send notification to user {$user->id}.");
                    }
                }
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('Error testing Pusher connection: ' . $e->getMessage());
            Log::error('Pusher test error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }
}
