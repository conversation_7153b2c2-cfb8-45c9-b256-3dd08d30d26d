<?php

namespace App\Console\Commands;

use App\Enums\RideStatusEnum;
use App\Enums\RideTypeEnum;
use App\Models\Ride;
use App\Services\RideService;
use Illuminate\Console\Command;

class AutoCancelIgnoredRides extends Command
{
    protected $signature = 'rides:auto-cancel';
    protected $description = 'Cancel ignored scheduled rides and recreate as immediate';

    public function handle(RideService $rideService)
    {
        $threshold = now()->addMinutes(15);

        Ride::where('type', RideTypeEnum::SCHEDULED)
            ->where('status', RideStatusEnum::ACCEPTED)
            ->where('scheduled_at', '<=', $threshold)
            ->with('points')
            ->each(function ($ride) use ($rideService): void {
                // Cancel with system reason
                $ride->update([
                    'status' => RideStatusEnum::CANCELED,
                    'cancel_reason' => 'Ignored by driver',
                    'cancelled_by' => 'system',
                    'canceled_at' => now()
                ]);
                $rideData = [
                    'points' => $rideService->extractPointsData($ride),
                    'pricing_id' => $ride->pricing_id,
                    'type' => RideTypeEnum::IMMEDIATE->value,
                    'scheduled_at' => null,
                    'requested_at' => $ride->requested_at,
                    'driver_gender' => $ride->driver_gender,
                    'coupon_code' => optional($ride->coupon)->code,
                    'note' => $ride->note,
                ];
                // Recreate as immediate
                $rideService->createRide($ride->customer_id,$rideData);
            });

        $this->info('Processed ignored rides');
    }
}
