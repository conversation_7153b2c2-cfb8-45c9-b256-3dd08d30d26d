<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class GenerateCollectionResource extends Command
{
    protected $signature = 'generate:collection-resource {tableName} {className}';

    protected $description = 'Generate a Collection Resource class';

    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $stubContent = $this->getStubContent('custom-collection-resource.stub', ['className' => $className]);
        $folderPath = app_path("Http/Resources");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Collection.php";
        file_put_contents($fileName, $stubContent);
        $this->info("CollectionResource class '{$className}Collection' generated successfully!");
    }
    private function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            if ($key === 'attributes') {
                $value = $this->formatAttributes($value);
            }
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
        }
        return $stubContent;
    }

    private function formatAttributes($attributes)
    {
        return implode(",\n            ", $attributes);
    }
}
