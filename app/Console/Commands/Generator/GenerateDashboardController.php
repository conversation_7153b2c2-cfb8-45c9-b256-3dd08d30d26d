<?php

namespace App\Console\Commands\Generator;

use Illuminate\Support\Facades\Schema;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class GenerateDashboardController extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:dashboard-controller {className} {tableName}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate an Dashboard Controller class';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $tableNameKebab = str($tableName)->studly()->kebab();
        $classNamePlural = Str::plural($className);
        
        // $controller_model = ;
        $modelClass = 'App\\Models\\' . ucfirst(Str::singular($className));
        $model = new \ReflectionClass($modelClass);
        $relations = $this->getModelRelationshipMethods($model, $className);
        $shared_relation_queries = '';
        $shared_relation_collections = '';
        foreach($relations as $relation)
        {
            $rel_name = $relation['relation_name'];
            $shared_relation_queries .= "$".Str::plural($rel_name)."_values = \App\Models\\".ucfirst($rel_name)."::pluck('id');\n";
            if ($relation['with_translation'] == true) {
                $shared_relation_queries .= "$".Str::plural($rel_name)."_names = \App\Models\\".ucfirst($rel_name)."::listsTranslations('name')->pluck('".$relation['column_name']."');\n";
            } else {
                $shared_relation_queries .= "$".Str::plural($rel_name)."_names = \App\Models\\".ucfirst($rel_name)."::pluck('".$relation['column_name']."');\n";
            }
            $shared_relation_collections .= "'".Str::plural($rel_name)."_values' => $".Str::plural($rel_name)."_values,\n";
            $shared_relation_collections .= "'".Str::plural($rel_name)."_names' => $".Str::plural($rel_name)."_names,\n";
        }
        $stubContent = $this->getStubContent(
            "custom-controller.dashboard.stub",
            [
                'className' => $className,
                'classNamePlural' => $classNamePlural,
                'tableNameKebab' => $tableNameKebab,
                'shared_relation_queries' => $shared_relation_queries,
                'shared_relation_collections' => $shared_relation_collections
            ]
        );

        $folderPath = app_path("Http/Controllers/Dashboard");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Controller.php";
        file_put_contents($fileName, $stubContent);
        $this->info("Dashboard Controller '{$className}' generated successfully!");
    }

    /**
     * @return array{relation_name: mixed, column_name: mixed, with_translation: bool}[]
     */
    protected function getModelRelationshipMethods(\ReflectionClass $reflector, $className): array
    {
        $methods = $reflector->getMethods(\ReflectionMethod::IS_PUBLIC);
        $relations = [];
        foreach ($methods as $method) {
                $returnType = $this->getReturnType($method);
                if ($returnType == 'BelongsTo') {

                    $relatedModel = $this->getRelatedModelName($method);
                    if ($relatedModel != "Translation") {
                        $relatedModelClass = "App\\Models\\" . $relatedModel;
                        if (class_exists($relatedModelClass)) {
                            $relatedModelReflectionClass = new \ReflectionClass($relatedModelClass);
                            $relatedTableName = (new $relatedModelClass())->getTable();
                            $relatedClassProperties = $relatedModelReflectionClass->getProperties();
                            foreach ($relatedClassProperties as $property) {
                                if (in_array($property->name, ['translatedAttributes', 'fillable'])) {
                                    $relatedModelObject = new $relatedModelClass();
                                    $relationColumnValue = $relatedModelReflectionClass->getConstant('RELATION_COLUMN');
                                    $attributes = $property->getValue($relatedModelObject);
                                    foreach ($attributes as $attribute) {
                                        if ($attribute == $relationColumnValue) {
                                            if ($property->name === 'translatedAttributes') {
                                                $columns[] = "Column::make('{$method->getName()}.translation.$attribute')
                                                    ->render('$.fn.dataTable.render.ellipsis(50)')
                                                    ->title('{$relatedModel}.$attribute')";
                                                if ($method->getName() != Str::lower($className) && !in_array($method->getName(), $relations)) {
                                                    $relations[] = [
                                                        'relation_name' => $method->getName(),
                                                        'column_name' => $attribute,
                                                        'with_translation' => true
                                                    ];
                                                }
                                            } elseif (in_array(Schema::getColumnType($relatedTableName, $attribute), ['varchar', 'text'])) {
                                                $columns[] = "Column::make('{$method->getName()}.$attribute')
                                                ->render('$.fn.dataTable.render.ellipsis(50)')
                                                ->title('{$relatedModel}.$attribute')";
                                                if (!in_array($method->getName(), $relations)) {
                                                    $relations[] = [
                                                        'relation_name' => $method->getName(),
                                                        'column_name' => $attribute,
                                                        'with_translation' => false
                                                    ];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // if(!in_array($method->getName(), $relations))
                    // $relations[] = $method->getName();
                }
        }
        return $relations;
    }

    private function getReturnType($method)
    {
        $returnType = $method->getReturnType();
        $parts = explode('\\', $returnType);
        return end($parts);
    }
    private function getRelatedModelName($method)
    {
        return Str::studly(Str::singular($method->getName()));
    }

    private function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
            $stubContent = str_replace("{{ ucfirst($$key) }}", ucfirst($value), $stubContent);
            $stubContent = str_replace("{{ lcfirst($$key) }}", lcfirst($value), $stubContent);
        }
        return $stubContent;
    }
}
