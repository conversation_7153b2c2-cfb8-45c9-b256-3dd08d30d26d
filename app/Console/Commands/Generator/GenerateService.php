<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateService extends BaseCommand
{
    protected $signature = 'generate:service {tableName} {className}';

    protected $description = 'Generate a service class';

    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');

        $moreStoreOrUpdateCode = "";
        $tableComment = $this->getTableComment($tableName);

        $hasMedia = $this->detectHasMedia($tableComment);
        if ($hasMedia) {
            $moreStoreOrUpdateCode .= $this->generateMediaHandlingCode($tableComment);
        }

        $comments = $this->getAllColumnComments($tableName);
        // Get the columns of the specified table
        $columns = Schema::getColumnListing($tableName);

        $stringFields = array_map(function ($column) use ($tableName, $comments) {
            $comment = json_decode($comments[$column], true);


            // Check for relationship type
            $isMorph = isset($comment['relationshipType']) && $comment['relationshipType'] === 'MORPH';

            // Return the column if it meets the criteria; otherwise, return null
            return (Schema::getColumnType($tableName, $column) === 'varchar' && !$isMorph) ? $column : null;
        }, $columns);
        $stringFields = array_filter($stringFields);

        // Include filters for belongsTo relationships
        $relationshipFilters = $this->getBelongsToFilters($tableName);
        $translationFilters = $this->getTranslationFilters($tableName);

        if ($translationFilters) {
            $stringFields = array_merge($stringFields, $relationshipFilters, $translationFilters);
        }
        // Combine string fields and relationship filters
        $stringFields = array_merge($stringFields, $relationshipFilters);
        // dd($stringFields);
        $stringFields = json_encode($stringFields);
        // Generate the query builder logic for all string fields
        $queryBuilderCode = "";
        $queryBuilderCode .= "            ->allowedFilters($stringFields)\n";

        $defaultSorts = "created_at";
        $queryBuilderCode .= "            ->defaultSorts('{$defaultSorts}')\n";

        // Update stub content with the new queryBuilder code
        $stubContent = $this->getStubContent('custom-service.stub', ['className' => $className, 'moreStoreOrUpdateCode' => $moreStoreOrUpdateCode, 'queryBuilderCode' => $queryBuilderCode]);
        $folderPath = app_path("Services");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Service.php";
        file_put_contents($fileName, $stubContent);

        $this->info("Service class '{$className}' generated successfully!");
    }

    /**
     * @return \list<\non-falsy-string>
     */
    private function getBelongsToFilters($tableName): array
    {
        $filters = [];
        $columns = Schema::getColumnListing($tableName);

        // Get all comments for the columns in the table
        $CloumnsComments = $this->getAllColumnComments($tableName);
        $tableComment = $this->getTableComment($tableName);

        // Merge the table comment with column comments
        $comments = array_merge(['tableComment' => $tableComment], $CloumnsComments);
        foreach ($columns as $column) {
            // Check if the column has a comment
            if (isset($comments[$column])) {
                $commentData = json_decode($comments[$column], true);
                // Check for relationshipType and ensure it is 'belongsTo'
                if ((isset($commentData['relationshipType']) && $commentData['relationshipType'] === 'belongsTo') ||
                    (isset($commentData['relationColumn']))
                ) {

                    // Get the related model name, removing _id
                    $relationColumn = str_replace('_id', '', $column);

                    // Get the translation table name
                    $relationTableName = $this->getTranslationTableName($relationColumn);

                    if (Schema::hasTable($relationTableName)) {
                        $translationColumns = Schema::getColumnListing($relationTableName);
                        $translationComments = $this->getAllColumnComments($relationTableName);

                        foreach ($translationColumns as $translationColumn) {
                            // Check if it's a string field and exclude 'locale' and fields ending with '_type'
                            if (isset($translationComments[$translationColumn]) && (in_array(Schema::getColumnType($relationTableName, $translationColumn), ['string', 'varchar']) && $translationColumn !== 'locale' && !str_contains($translationColumn, 'able_type'))) {
                                // Add to filters with relation prefix
                                $filters[] = "{$relationColumn}.{$translationColumn}";
                                // E.g., 'category.name'
                            }
                        }
                    }
                }
            }
        }

        return $filters; // Return only columns with relationshipType 'belongsTo' and their translation fields
    }

    private function getTranslationFilters($tableName)
    {
        $filters = [];

        $tableComment = json_decode($this->getTableComment($tableName), true);
        if (isset($tableComment['relationColumn'])) {
            $translationTable = $this->getTranslationTableName($tableName);

            if (Schema::hasTable($translationTable)) {

                $translationColumns = Schema::getColumnListing($translationTable);
                $translationComments = $this->getAllColumnComments($translationTable);
                foreach ($translationColumns as $translationColumn) {
                    // Check if it's a string field and exclude 'locale' and fields ending with '_type'
                    if (isset($translationComments[$translationColumn]) && (in_array(Schema::getColumnType($translationTable, $translationColumn), ['string', 'varchar']) && $translationColumn !== 'locale' && !str_contains($translationColumn, 'able_type'))) {
                        // Add to filters with relation prefix
                        $filters[] = "{$translationColumn}";
                        // E.g., 'category.name'
                    }
                }
                return $filters;
            }
        }
        return null;
    }
    private function generateMediaHandlingCode($tableComment)
    {
        $comment = json_decode($tableComment, true);
        $code = "";
        if (isset($comment['mediaCollections'])) {
            foreach ($comment['mediaCollections'] as $collection => $type) {
                $isPlural = Str::plural(Str::singular($collection)) === $collection;
                if ($isPlural) {
                    $code .=
                        <<<EOD
                        if(request()->input('{$collection}')) { 
                            foreach(request()->input('{$collection}') as \$file)
                            {
                                \$tmp_media = storage_path('tmp/uploads/' . \$file);
                                if(file_exists(\$tmp_media))
                                {
                                    \$model->addMedia(\$tmp_media)->toMediaCollection('{$collection}'); 
                                }
                            }
                        }  
                    EOD;
                    // if (isset(\$data['{$collection}']) && is_array(\$data['{$collection}']) && !is_null(\$data['{$collection}'])) {
                    //     \$model->clearMediaCollection('{$collection}');
                    //     foreach (\$data['{$collection}'] as \$file) {
                    //         \$model->addMedia(\$file)->preservingOriginal()->toMediaCollection('{$collection}');
                    //     }
                    // }
                } else {
                    $code .=
                        <<<EOD
                        \$file = request()->input('{$collection}'); 
                        \$tmp_media = storage_path('tmp/uploads/' . \$file);
                        if(file_exists(\$tmp_media))
                        {
                            \$model->addMedia(\$tmp_media)->toMediaCollection('{$collection}'); 
                        }
                    EOD;
                    // if (isset(\$data['{$collection}']) && !is_null(\$data['{$collection}'])) {
                    //     \$model->clearMediaCollection('{$collection}');
                    //     \$model->addMedia(\$data['{$collection}'])->preservingOriginal()->toMediaCollection('{$collection}');
                    // }
                }
            }
        }
        return $code;
    }
}
