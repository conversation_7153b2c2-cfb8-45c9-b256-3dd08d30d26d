<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateFactory extends BaseCommand
{
    protected $signature = 'generate:factory {tableName} {className}';

    protected $description = 'Generate a factory class';

    public function handle()
    {
        $tableName = $this->argument('tableName');
        $className = $this->argument('className');
        $factoryColumns = $this->getFactoryColumns($tableName, $className);
        $stubContent = $this->getStubContent('custom-factory.stub', ['className' => $className, 'factoryColumns' => $factoryColumns]);
        $folderPath = database_path("factories");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Factory.php";
        // $fileName = database_path("factories/{$className}Factory.php");
        file_put_contents($fileName, $stubContent);
        $this->info("Factory for table '{$tableName}' generated successfully!");
    }


    private function getFactoryColumns($tableName, $className)
    {
        $columns = Schema::getColumns($tableName);
        $foreignKeys = Schema::getForeignKeys($tableName);
        $hasTranslation = $this->detectTranslationTable($tableName);
        $translatedColumns = [];
        if ($hasTranslation) {
            $translatedColumns = $this->getTranslatedAttributes($tableName)->toArray();
            $columns = array_merge($columns, $translatedColumns);
        }

        return implode("\n", array_map(function ($column) use ($foreignKeys, $className) {
            $columnName = $column['name'];
            $columnType = $column['type_name'];

            if (isset($column['comment'])) {
                $columnComment = json_decode($column['comment'], true);
                if (isset($columnComment['isEnumColumn']) && $columnComment['isEnumColumn'] == true) {
                    $enumName = $className . str($column['name'])->ucfirst() . "Enum";
                    return "'{$columnName}' => \$this->faker->randomElement(\\App\Enums\\{$enumName}::cases()),";
                }
            }

            if (in_array($columnName, $this->fillableColumnsToIgnore())) {
                return '';
            }

            foreach ($foreignKeys as $foreignKey) {
                if (in_array($columnName, $foreignKey['columns'])) {
                    $relatedTable = Str::singular(Str::studly($foreignKey['foreign_table']));
                    $relatedColumn = $foreignKey['foreign_columns'][0];
                    return "'{$columnName}' => \$this->faker->randomElement(\\App\Models\\{$relatedTable}::pluck('{$relatedColumn}')->toArray()),";
                }
            }

            // Handle other column types
            switch ($columnType) {
                case 'int':
                case 'bigint':
                case 'integer':
                case 'mediumint':
                case 'smallint':
                case 'tinyint':
                    return "'{$columnName}' => \$this->faker->numberBetween(1, 100),";
                case 'varchar':
                case 'char':
                    if (strpos($columnName, 'email') !== false) {
                        return "'{$columnName}' => \$this->faker->unique()->safeEmail,";
                    } elseif (strpos($columnName, 'password') !== false) {
                        return "'{$columnName}' => bcrypt('password'),";
                    } else {
                        return "'{$columnName}' => \$this->faker->word,";
                    }
                case 'text':
                case 'longtext':
                case 'mediumtext':
                    return "'{$columnName}' => \$this->faker->paragraph(),";
                case 'datetime':
                case 'timestamp':
                case 'timestamptz':
                    return "'{$columnName}' => \$this->faker->dateTime()->format('Y-m-d H:i:s'),";
                case 'date':
                    return "'{$columnName}' => \$this->faker->date(),";
                case 'year':
                    return "'{$columnName}' => \$this->faker->year,";
                case 'time':
                case 'timetz':
                    return "'{$columnName}' => \$this->faker->time(),";
                case 'bool':
                    return "'{$columnName}' => \$this->faker->boolean(),";
                case 'uuid':
                    return "'{$columnName}' => \$this->faker->uuid,";
                case 'float':
                case 'decimal':
                case 'double':
                    return "'{$columnName}' => \$this->faker->randomFloat(2, 0, 100),";
                case 'binary':
                    return "'{$columnName}' => \$this->faker->randomAscii,";
                case 'ipaddress':
                    return "'{$columnName}' => \$this->faker->ipv4,";
                case 'macaddress':
                    return "'{$columnName}' => \$this->faker->macaddress,";
                case 'enum':
                case 'geometry':
                case 'geometrycollection':
                case 'json':
                case 'jsonb':
                case 'line':
                case 'linestring':
                case 'multilinestring':
                case 'multipoint':
                case 'multipolygon':
                case 'polygon':
                case 'point':
                case 'set':
                    return "'{$columnName}' => \$this->faker->randomElement,";
                default:
                    return "'{$columnName}' => \$this->faker->word,";
            }
        }, $columns));
    }
}
