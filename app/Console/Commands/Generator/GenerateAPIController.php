<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateAPIController extends Command
{
    protected $signature = 'generate:api-controller {className}';

    protected $description = 'Generate an API Controller class';

    public function handle()
    {
        $className = $this->argument('className');
        $classNamePlural = Str::plural($className);
        $stubContent = $this->getStubContent('custom-controller.api.stub', ['className' => $className, 'classNamePlural' => $classNamePlural]);

        $folderPath = app_path("Http/Controllers/Api");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
    
        $fileName = "{$folderPath}/{$className}Controller.php";

        // $fileName = app_path("Http/Controllers/API/{$className}Controller.php");
        file_put_contents($fileName, $stubContent);
        $this->info("API Controller '{$className}' generated successfully!");
    }

    private function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);

        foreach ($data as $key => $value) {
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
            $stubContent = str_replace("{{ ucfirst($$key) }}", ucfirst($value), $stubContent);
            $stubContent = str_replace("{{ lcfirst($$key) }}", lcfirst($value), $stubContent);
        }

        return $stubContent;
    }
}
