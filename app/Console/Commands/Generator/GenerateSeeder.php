<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateSeeder extends BaseCommand
{
    protected $signature = 'generate:seeder {tableName} {className}';

    protected $description = 'Generate a seeder class';

    public function handle()
    {
        $tableName = $this->argument('tableName');
        $className = $this->argument('className');

        $stubContent = $this->getStubContent('custom-seeder.stub', ['className' => $className]);
        $folderPath = database_path("seeders");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Seeder.php";
        // $fileName = database_path("seeders/{$className}Seeder.php");
        file_put_contents($fileName, $stubContent);

        $this->info("Seeder for table '{$tableName}' generated successfully!");
    }
}
