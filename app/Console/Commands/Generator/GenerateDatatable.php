<?php

namespace App\Console\Commands\Generator;

use Faker\Provider\Base;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use ReflectionAttribute;
use ReflectionClass;
use ReflectionMethod;

class GenerateDatatable extends BaseCommand
{
    protected $signature = 'generate:datatable {tableName} {className}';
    protected $description = 'Generate a DataTable class for a given model';


    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $modelClass = "App\\Models\\" . $className;

        $tableNameKebab = str($tableName)->studly()->kebab();

        $columns = $this->getTableColumns($tableName);
        $filters = [];
        [$newColumns, $relationships] = $this->getResourceRelationships($modelClass, $filters);
        $filter_query = '';
        foreach($filters as $filter)
        {
            $filter_query .= 
            "
                if(request('".$filter.'_id'."'))
                    \$query->where('".$filter.'_id'."', request('".$filter.'_id'."'));
            ";
        }
        $combinedAttributes = array_merge($columns, $newColumns);
        $stubContent = $this->getStubContent('custom-datatable.stub', [
            'className' => $className,
            'relations' => array_unique($relationships),
            'tableName' => $tableName,
            'columns' => $combinedAttributes,
            'prefixRoute' => "dashboard.$tableNameKebab",
            'model' => $className,
            'addRouteUrl' => "route('dashboard." . $tableNameKebab . ".create')",
            'filter_query' => $filter_query
            // 'actionOptions' => []
        ]);

        $folderPath = app_path("DataTables");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}DataTable.php";
        // $fileName = app_path("DataTables/{$className}DataTable.php");

        file_put_contents($fileName, $stubContent);

        // Add the keys to the ar.json
        $this->addKeysToArJson($columns);
        $this->info("DataTable class '{$className}' generated successfully!");
    }

    /**
     * Add translation keys to ar.json file
     */
    private function addKeysToArJson(array $columns)
    {
        $jsonFilePath = base_path('lang\ar.json');
        // Read the current content of ar.json
        if (File::exists($jsonFilePath)) {
            $translations = json_decode(File::get($jsonFilePath), true);
        } else {
            $translations = [];
        }

        // Loop through columns and add missing keys
        foreach ($columns as $column) {
            // Extract the title from the column definition (assuming it's in __())
            preg_match("/->title\(\__\('(.+?)'\)\)/", $column, $matches);
            if (isset($matches[1])) {
                $key = $matches[1];

                // Add the key if it doesn't exist
                if (!array_key_exists($key, $translations)) {
                    $translations[$key] = ""; // Leave the value empty for translation
                }
            }
        }

        // Write the updated translations back to the file
        File::put($jsonFilePath, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info("Translation keys added to ar.json.");
    }

    private function getTableColumns($tableName)
    {
        $columns = Schema::getColumns($tableName);
        $foreignKeys = Schema::getForeignKeys($tableName);
        $foreignKeysColumns = Arr::flatten(Arr::pluck($foreignKeys, 'columns'));
        return collect($columns)
            ->reject(function ($column) use ($foreignKeysColumns) {
                return $column == 'updated_at' || in_array($column['name'], $foreignKeysColumns);
            })
            ->map(function ($column) {
                $title = str($column['name'])->studly()->value;
                $localizedTitle = __('' . $title);
                if (in_array($column['type_name'], $this->getTextDataTypes())) {
                    return "Column::make('{$column['name']}')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(true)->title(__('{$localizedTitle}'))";
                } else {
                    return "Column::make('{$column['name']}')->render('$.fn.dataTable.render.ellipsis(50)')->searchable(false)->title(__('{$localizedTitle}'))";
                }
            })
            ->toArray();
    }

    /**
     * @return never[]|array<int, \list<\non-falsy-string>>
     */
    private function getResourceRelationships($modelClass, &$filters): array
    {
        if (!class_exists($modelClass)) {
            return [];
        }

        $reflectionClass = new ReflectionClass($modelClass);
        $columns = [];
        $relations = [];
        $className = $this->argument('className');

        foreach ($reflectionClass->getMethods(ReflectionMethod::IS_PUBLIC) as $method) {
            if ($this->identifyRelationshipType($method) && in_array($this->getReturnType($method), ['HasOne', 'BelongsTo'])) {
                if ($this->getReturnType($method) == 'BelongsTo') {
                    $filters[] = $method->getName();
                }
                $relatedModel = $this->getRelatedModelName($method);
                if ($relatedModel != "Translation") {
                    $relatedModelClass = "App\\Models\\" . $relatedModel;
                    if (class_exists($relatedModelClass)) {
                        $relatedModelReflectionClass = new ReflectionClass($relatedModelClass);
                        $relatedTableName = (new $relatedModelClass())->getTable();
                        $relatedClassProperties = $relatedModelReflectionClass->getProperties();
                        foreach ($relatedClassProperties as $property) {
                            if (in_array($property->name, ['translatedAttributes', 'fillable'])) {
                                $relatedModelObject = new $relatedModelClass();
                                $relationColumnValue = $relatedModelReflectionClass->getConstant('RELATION_COLUMN');
                                $attributes = $property->getValue($relatedModelObject);
                                foreach ($attributes as $attribute) {
                                    if ($attribute == $relationColumnValue) {
                                        if ($property->name === 'translatedAttributes') {
                                            $columns[] = "Column::make('{$method->getName()}.translation.$attribute')
                                                ->render('$.fn.dataTable.render.ellipsis(50)')
                                                ->title('{$relatedModel}.$attribute')";
                                            if ($method->getName() != Str::lower($className)) {
                                                $relations[] = "'" . $method->getName() . ".translation'";
                                            }
                                        } elseif (in_array(Schema::getColumnType($relatedTableName, $attribute), $this->getTextDataTypes())) {
                                            $columns[] = "Column::make('{$method->getName()}.$attribute')
                                                    ->render('$.fn.dataTable.render.ellipsis(50)')
                                                    ->title('{$relatedModel}.$attribute')";
                                            $relations[] = "'" . $method->getName() . "'";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return [$columns, $relations];
    }

    private function identifyRelationshipType($method)
    {
        // You can adapt and refine this method to better detect relationship types
        
        $returnType = (string)$method->getReturnType();
        return Str::contains($returnType, [
            'HasOne',
            'HasMany',
            'BelongsTo',
            'BelongsToMany',
            'MorphOne',
            'MorphMany',
            'MorphToMany',
            'MorphTo'
        ]) ? $returnType : null;
    }

    private function getReturnType($method)
    {
        $returnType = $method->getReturnType();
        $parts = explode('\\', $returnType);
        return end($parts);
    }
    private function getRelatedModelName($method)
    {
        return Str::studly(Str::singular($method->getName()));
    }


    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $content = File::get($stubPath);

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $replaceValue = implode(",\n", $value);
            } else {
                $replaceValue = $value; // Use the value directly if it's not an array
            }
            $content = str_replace("{{ $$key }}", $replaceValue, $content);
        }

        return $content;
    }
}
