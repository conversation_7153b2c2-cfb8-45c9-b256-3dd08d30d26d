<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use ReflectionClass;
use ReflectionMethod;
use Illuminate\Support\Str;

class GenerateJsonResource extends BaseCommand
{
    protected $signature = 'generate:json-resource {tableName} {className}';

    protected $description = 'Generate a JSON Resource class';

    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $modelClass = "App\\Models\\" . $className;

        $combinedAttributes = $this->getResourceAttributes($tableName);
        $relationships = $this->getResourceRelationships($modelClass);
        $combinedAttributes = array_merge($combinedAttributes, $relationships);

        $stubContent = $this->getStubContent('custom-json-resource.stub', ['className' => $className, 'combinedAttributes' => $combinedAttributes]);
        $folderPath = app_path("Http/Resources");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}Resource.php";

        // $fileName = app_path("Http/Resources/{$className}Resource.php");
        file_put_contents($fileName, $stubContent);

        $this->info("JSON Resource class '{$className}' generated successfully!");
    }

    /**
     * @return \list<\non-falsy-string>
     */
    private function getResourceAttributes($tableName): array
    {
        $columns = Schema::getColumnListing($tableName);
        $attributes = [];
        foreach ($columns as $column) {
            $attributes[] = "'$column' => \$this->$column";
        }
        $columns = Schema::getColumnListing($this->getTranslationTableName($tableName));
        foreach ($columns as $column) {
            if (!in_array($column, $this->translationColumnsToIgnore($tableName))) {
                $attributes[] = "'$column' => \$this->$column";
            }
        }
        return $attributes;
    }

    /**
     * @return never[]|\list<\non-falsy-string>
     */
    private function getResourceRelationships($modelClass): array
    {
        if (!class_exists($modelClass)) {
            return []; // Return empty array if class does not exist
        }

        $reflectionClass = new ReflectionClass($modelClass);
        $relationships = [];

        foreach ($reflectionClass->getMethods(ReflectionMethod::IS_PUBLIC) as $method) {
            $relationType = $this->identifyRelationshipType($method);
            if ($relationType) {
                $relatedModel = $this->getRelatedModelName($method);
                if ($relatedModel != "Translation") {
                    $resourceClass = $this->findResourceClass($relatedModel, $relationType);
                    if ($resourceClass) {
                        $relationships[] = "'{$method->getName()}' => {$resourceClass}::make(\$this->whenLoaded('{$method->getName()}'))";
                        // $returnType = (string)$method->getReturnType();
                        // if ($returnType ===  'Illuminate\Database\Eloquent\Relations\BelongsToMany' || $returnType === 'Illuminate\Database\Eloquent\Relations\MorphToMany') {
                        //     $pivotTableName = $this->inferPivotTableName($method, $modelClass); // Infer pivot table name if needed
                        //     $relationships[] = "'{$pivotTableName}' => {$resourceClass}::make(\$this->whenPivotLoaded('{$pivotTableName}', '{$method->getName()}'))";
                        // }
                    }
                }
            }
        }
        return $relationships;
    }
    // private function inferPivotTableName($method, $modelClass)
    // {
    //     // This should be implemented to correctly infer or retrieve the pivot table name
    //     // Example: return 'role_user'; for a User model having roles()
    //     return Str::snake($method->getName());
    // }
    private function identifyRelationshipType($method)
    {
        $returnType = (string)$method->getReturnType();
        $relationships = [
            'Illuminate\Database\Eloquent\Relations\HasOne' => 'Single',
            'Illuminate\Database\Eloquent\Relations\HasMany' => 'Collection',
            'Illuminate\Database\Eloquent\Relations\BelongsTo' => 'Single',
            'Illuminate\Database\Eloquent\Relations\BelongsToMany' => 'Collection',
            'Illuminate\Database\Eloquent\Relations\MorphOne' => 'Single',
            'Illuminate\Database\Eloquent\Relations\MorphMany' => 'Collection',
            'Illuminate\Database\Eloquent\Relations\MorphToMany' => 'Collection',
            // 'Illuminate\Database\Eloquent\Relations\MorphTo' => 'Single'
        ];

        return $relationships[$returnType] ?? null;
    }

    private function getRelatedModelName($method)
    {
        // This would ideally be derived from method analysis or annotations
        return Str::studly(Str::singular($method->getName()));
    }

    private function findResourceClass($relatedModel, $relationType)
    {
        if ($relationType === 'Collection') {
            return   $relatedModel . 'Collection';
        } else {
            return   $relatedModel . 'Resource';
        }
    }

    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            $replaceValue = is_array($value) ? implode(",\n            ", $value) : $value;
            $stubContent = str_replace("{{ $$key }}", $replaceValue, $stubContent);
        }
        return $stubContent;
    }
}
