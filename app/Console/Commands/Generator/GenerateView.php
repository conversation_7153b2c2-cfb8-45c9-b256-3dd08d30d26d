<?php

namespace App\Console\Commands\Generator;

use App\Helpers\Helpers;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class GenerateView extends BaseCommand
{
    protected $signature = 'generate:dashboard-view {tableName} {className}';

    protected $description = 'Generate views for CRUD operations';

    public function handle()
    {
        $tableName = $this->argument('tableName');
        $className = $this->argument('className');
        $viewTypes = ['index', 'create', 'edit', 'show'];

        foreach ($viewTypes as $type) {
            $this->generateView($tableName, $className, $type);
        }

        $this->info("Views class '{$className}' generated successfully!");
    }

    private function addKeysToArJson(array $translationKeys)
    {
    // Define the file path at the root of the project
    $jsonFilePath = base_path('lang\ar.json');

    // Read the current content of ar.json
    if (File::exists($jsonFilePath)) {
        $translations = json_decode(File::get($jsonFilePath), true);
    } else {
        $translations = [];
    }

    // Add the keys if they don't exist
    foreach ($translationKeys as $key) {
        if (!array_key_exists($key, $translations)) {
            $translations[$key] = ""; // Leave the value empty for translation
        }
    }

    // Write the updated translations back to the file
    File::put($jsonFilePath, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    $this->info("Translation keys added to ar.json.");
    }

    private function generateView($tableName, $className, $type)
    {
        $tableNameKebab = str($tableName)->studly()->kebab();
        $viewDirectory = resource_path("views/dashboard/{$tableNameKebab}");
        if (!File::isDirectory($viewDirectory)) {
            File::makeDirectory($viewDirectory, 0755, true);
        }
        $path = "{$viewDirectory}/{$type}.blade.php";
        $classNamePlural = Str::plural($className);
        $storeRoute = "{{ route('dashboard.$tableNameKebab.store') }}";
        $updateRoute = '{{ route("dashboard.' . $tableNameKebab . '.update", ["' . Str::lower($className) . '" =>  $' . Str::lower($className) . '->id]) }}';
        $modelClass = 'App\\Models\\' . $className;
        $model = new \ReflectionClass($modelClass);

        // Translation keys for various views
        $translationKeys = [
            "Create $className",
            "Edit $className",
            "Show $className",
            "$classNamePlural",
            
        ];

        // Add translation keys to ar.json
        $this->addKeysToArJson($translationKeys);


        $attributes = $model->getDefaultProperties()['fillable'];
        $fields = [];
        $datatable_filters = [];
        foreach ($attributes as $attribute) {
            if (substr(trim($attribute), -3) === '_id' || substr(trim($attribute), -5) === '_type') {
                continue;
            }
            $attribute_type = Schema::getColumnType($tableName, $attribute);
            if ($attribute_type == 'enum') {
                $fields[] = [
                    'name' => $attribute,
                    'type' => Helpers::getColumnInputType($attribute_type),
                    'options' => Helpers::getEnumValues($tableName, $attribute)
                ];
            } else {
                $fields[] = [
                    'name' => $attribute,
                    'type' => Helpers::getColumnInputType($attribute_type)
                ];
            }
        }
        $translatedAttributes = $model->getProperty('translatedAttributes');
        $modelObject = new $modelClass();
        $translatedAttributesArray = $translatedAttributes->getValue($modelObject);
        foreach ($translatedAttributesArray as $translatedAttribute) {
            $attribute_type = Schema::getColumnType($this->getTranslationTableName($tableName), $translatedAttribute);
            if (Helpers::getColumnInputType($attribute_type) == 'text') {
                $fields[] = [
                    'name' => $translatedAttribute,
                    'type' => 'text_with_translation'
                ];
            } else {
                $fields[] = [
                    'name' => $translatedAttribute,
                    'type' => 'textarea_with_translation'
                ];
            }
        }
        $relations = $this->getModelRelationshipMethods($model, 'App\\Models\\' . $className, $className, $datatable_filters);
        $polymorphicTypes = [];
        foreach ($relations as $relation_attribute) {
            if(substr($relation_attribute, -9) === 'able_type')
            {
                $polymorphicAttribute = Str::lower($relation_attribute);
                $typeOptions = [];
                $methods = $model->getMethods(\ReflectionMethod::IS_PUBLIC);
                foreach ($methods as $method) {
                    if ($method->name === 'getPolymorphicTypes') {
                        $polymorphicTypes = $modelClass::getPolymorphicTypes();
                        break;
                    }
                }
                foreach ($polymorphicTypes as $polymorphicType) {
                    $typeName = class_basename($polymorphicType);
                    $typeOptions[] =  [
                        'value' => $polymorphicType,
                        'name' => $typeName
                    ];
                }
                $fields[] = [
                    'name' => strtolower($relation_attribute),
                    'type' => 'select',
                    'options' => $typeOptions
                ];
            } elseif(substr($relation_attribute, -7) === 'able_id')
            {
                $polymorphicIdAttribute = Str::lower($relation_attribute);
                $fields[] = [
                    'name' => strtolower($relation_attribute),
                    'type' => 'select',
                    'options' => []
                ];
            }else{
                $relation_model_name = ucfirst($relation_attribute);
                $modelNameSpace = "App\\Models\\$relation_model_name";
                $res = $modelNameSpace::get();
                $relation_model = new \ReflectionClass('App\\Models\\' . $relation_model_name);
                $related_column = $relation_model->getConstant('RELATION_COLUMN');
                $options = $res->map(function ($query) use ($related_column) {
                    return [
                        'value' => $query->id,
                        'name' => $query->{$related_column}
                    ];
                })->all();
                $fields[] = [
                    'name' => $relation_attribute . '_id',
                    'type' => 'select',
                    'options' => $options
                ];
            }
        }

        $tableComment = $this->getTableComment($tableName);
        $comment = json_decode($tableComment, true);
        if (isset($comment['mediaCollections'])) {
            foreach ($comment['mediaCollections'] as $collection => $file_type) {
                $isPlural = Str::plural(Str::singular($collection)) === $collection;
                switch ($file_type) {
                    case 'IMAGE':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'image/*\']",
                            'multiple' => $isPlural,

                        ];
                        break;
                    case 'VIDEO':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'.mp4\',\'.avi\']",
                            'multiple' => $isPlural
                        ];
                        break;
                    case 'FILE':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "null",
                            'multiple' => $isPlural
                        ];
                        break;
                    case 'AUDIO':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'.octet-stream\',\'.mpeg\',\'.mpga\',\'.mp3\',\'.wav\']",
                            'multiple' => $isPlural
                        ];
                        break;
                    case 'DOCUMENT':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'.txt\',\'.pdf\',\'.msword\',\'.doc\',\'.docx\',\'.xls\',\'.xlsx\']",
                            'multiple' => $isPlural
                        ];
                        break;
                    case 'ARCHIVE':
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'.zip\',\'.rar\']",
                            'multiple' => $isPlural
                        ];
                        break;
                    default:
                        $fields[] = [
                            'name' => $isPlural? $collection.'[]':$collection,
                            'type' => 'file',
                            'media_rule' => "[\'*\']",
                            'multiple' => $isPlural
                        ];
                        break;
                }
            }
        }

        $model_name = Str::lower($className);
        $content = $this->getStubContent(
            "custom-view.{$type}.stub",
            [
                'className' => $className,
                'tableName' => $tableName,
                'classNamePlural' => $classNamePlural,
                'storeRoute' => $storeRoute,
                'updateRoute' => $updateRoute,
                'form_content' => $this->getFormContent($fields),
                'update_form_content' => $this->getUpdateFormContent($model_name, $fields),
                'show_content' => $this->getShowContent($model_name, $fields),
                'polymorphicTypes' => json_encode($polymorphicTypes),
                'polymorphicAttribute' => isset($polymorphicAttribute) ? $polymorphicAttribute:"",
                'polymorphicIdAttribute' => isset($polymorphicIdAttribute) ? $polymorphicIdAttribute:'id',
                'modelName' => Str::lower($className),
                'filter_content' => $this->getFilterContent($datatable_filters),
                'filter_script_content' => $this->getFilterScriptContent($datatable_filters),
            ]
        );
   
        file_put_contents($path, $content);
    }
    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");

        $stubContent = File::get($stubPath);

        foreach ($data as $key => $value) {
            
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
            $stubContent = str_replace("{{ ucfirst($$key) }}", ucfirst($value), $stubContent);
            $stubContent = str_replace("{{ lcfirst($$key) }}", lcfirst($value), $stubContent);
        }
        
        return $stubContent;
    }
    protected function getFormContent($fields)
    {
        $content = '';
        foreach ($fields as $field) {
            switch ($field['type']) {
                case 'text':
                    if ($field['name'] == 'password') {
                        $content .= '<x-forms.input.password :field="\'' . $field['name'] . '\'"  />';
                    } else {
                        $content .= '<x-forms.input.text :field="\'' . $field['name'] . '\'"  />';
                    }
                    break;
                case 'number':
                    $content .= '<x-forms.input.number :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'date':
                    $content .= '<x-forms.input.date :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'datetime':
                    $content .= '<x-forms.input.datetime :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'textarea':
                    $content .= '<x-forms.textarea :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'checkbox':
                    $content .= '<x-forms.input.checkbox :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'file':
                    $collection_name = substr(trim($field['name']), -2) === '[]' ? substr($field['name'],0,-2) : $field['name'];
                    $content .= '<x-forms.input.file :rule="\'' . $field['media_rule'] . '\'"
                                  :collection="\'' . $collection_name . '\'" :field="\'' . $field['name'] . '\'" 
                                  :multiple="\'' . $field['multiple'] . '\'"  />';
                    break;
                case 'select':
                    if (substr(trim($field['name']), -7) === 'able_id' || substr(trim($field['name']), -5) === '_type')
                    {
                        $option_values = json_encode(collect($field['options'])->map(function ($option) {
                            return $option['value'];
                        })->all(), true);
                        $option_names = json_encode(collect($field['options'])->map(function ($option) {
                            return str_replace("\"", '\'', $option['name']);
                        })->all(), true);
                    }else{
                        $option_values = "$".Str::plural(substr(trim($field['name']), 0, -3))."_values";
                        $option_names = "$".Str::plural(substr(trim($field['name']), 0, -3))."_names";
                    }
                    $content .= '<x-forms.select :field="\'' . $field['name'] . '\'" :optionValues=\'' .$option_values. '\' :optionNames=\'' . $option_names . '\'/>';
                    break;
                case 'multi_select':
                    if (substr(trim($field['name']), -7) === 'able_id' || substr(trim($field['name']), -5) === '_type')
                    {
                        $option_values = json_encode(collect($field['options'])->map(function ($option) {
                            return $option['value'];
                        })->all(), true);
                        $option_names = json_encode(collect($field['options'])->map(function ($option) {
                            return str_replace("\"", '\'', $option['name']);
                        })->all(), true);
                    }else{
                        $option_values = "$".Str::plural(substr(trim($field['name']), 0, -3))."_values";;
                        $option_names = "$".Str::plural(substr(trim($field['name']), 0, -3))."_names";
                    }
                    $content .= '<x-forms.multiselect :field="\'' . $field['name'] . '\'"  :optionValues=\'' . $option_values . '\' :optionNames=\'' . $option_names . '\'/>';
                    break;
                case 'text_with_translation':
                    foreach (config('translatable.locales') as $locale) {

                        $content .= '<x-forms.input.text :field="\'' . $locale . '[' . $field['name'] . ']\'"  />';
                    }
                    break;
                case 'textarea_with_translation':
                    foreach (config('translatable.locales') as $locale) {
                        $content .= '<x-forms.textarea :field="\'' . $locale . '[' . $field['name'] . ']\'"  />';
                    }
                    break;
                default:
                    $content .= '<x-forms.textarea :field="\'' . $field['name'] . '\'"  />';
                    break;
            }
        }
        return new HtmlString($content);
    }
    protected function getUpdateFormContent($model_name, $fields)
    {
        $content = '';

        foreach ($fields as $field) {
            switch ($field['type']) {
                case 'text':
                    if ($field['name'] == 'password') {
                        $content .= '<x-forms.input.password :field="\'' . $field['name'] . '\'"  />';
                    } else {
                        $content .= '<x-forms.input.text :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    }
                    break;
                case 'number':
                    $content .= '<x-forms.input.number :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'date':
                    $content .= '<x-forms.input.date :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'datetime':
                    $content .= '<x-forms.input.datetime :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'textarea':
                    $content .= '<x-forms.textarea :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'checkbox':
                    $content .= '<x-forms.input.checkbox :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
                case 'file':
                    $collection_name = substr(trim($field['name']), -2) === '[]' ? substr($field['name'],0,-2) : $field['name'];
                    $content .= '<x-forms.input.file :model="$' . $model_name . '"  :rule="\'' . $field['media_rule'] . '\'"
                                  :collection="\'' . $collection_name . '\'" :field="\'' . $field['name'] . '\'" 
                                  :multiple="\'' . $field['multiple'] . '\'" />';
                    break;
                case 'select':
                    if (substr(trim($field['name']), -7) === 'able_id' || substr(trim($field['name']), -5) === '_type')
                    {
                        $option_values = json_encode(collect($field['options'])->map(function ($option) {
                            return $option['value'];
                        })->all(), true);
                        $option_names = json_encode(collect($field['options'])->map(function ($option) {
                            return str_replace("\"", '\'', $option['name']);
                        })->all(), true);
                    }else{
                        $option_values = "$".Str::plural(substr(trim($field['name']), 0, -3))."_values";;
                        $option_names = "$".Str::plural(substr(trim($field['name']), 0, -3))."_names";
                    }
                    $content .= '<x-forms.select :value="$' . $model_name . '->' . $field['name'] . '"  :field="\'' . $field['name'] . '\'"  :optionValues=\'' . $option_values . '\' :optionNames=\'' . $option_names . '\'/>';
                    break;
                case 'multi_select':
                    if (substr(trim($field['name']), -7) === 'able_id' || substr(trim($field['name']), -5) === '_type')
                    {
                        $option_values = json_encode(collect($field['options'])->map(function ($option) {
                            return $option['value'];
                        })->all(), true);
                        $option_names = json_encode(collect($field['options'])->map(function ($option) {
                            return str_replace("\"", '\'', $option['name']);
                        })->all(), true);
                    }else{
                        $option_values = "$".Str::plural(substr(trim($field['name']), 0, -3))."_values";;
                        $option_names = "$".Str::plural(substr(trim($field['name']), 0, -3))."_names";
                    }
                    $content .= '<x-forms.multiselect :field="\'' . $field['name'] . '\'"  :optionValues=\'' . $option_values . '\' :optionNames=\'' . $option_names . '\'/>';
                    break;
                case 'text_with_translation':
                    foreach (config('translatable.locales') as $locale) {
                        $value = '"$' . $model_name . '->{\'' . $field['name'] . ':' . $locale . '\'}"';
                        $content .= '<x-forms.input.text :value=' . $value . ' :field="\'' . $locale . '[' . $field['name'] . ']\'" />';
                    }

                    break;
                case 'textarea_with_translation':
                    foreach (config('translatable.locales') as $locale) {
                        $value = '"$' . $model_name . '->{\'' . $field['name'] . ':' . $locale . '\'}"';
                        $content .= '<x-forms.textarea :value=' . $value . ' :field="\'' . $locale . '[' . $field['name'] . ']\'"  />';
                    }

                    break;
                default:
                    $content .= '<x-forms.textarea :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'"  />';
                    break;
            }
        }

        return new HtmlString($content);
    }
    protected function getShowContent($model_name, $fields)
    {
        $content = '';
        foreach ($fields as $field) {
            if (substr(trim($field['name']), -7) === 'able_id') {
                $content .= '<x-forms.span :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'" />';
            } elseif (substr(trim($field['name']), -9) === 'able_type') {
                $content .= '<x-forms.span :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'" />';
            } elseif (substr(trim($field['name']), -3) === '_id') {
                $relation_name = substr($field['name'], 0, -3);
                $relation_model = new \ReflectionClass('App\\Models\\' . ucfirst($relation_name));
                $relation_column_name = $relation_model->getConstant('RELATION_COLUMN');
                $content .= '<x-forms.span :value="$' . $model_name . '->' . $relation_name . '->' . $relation_column_name . '" :field="\'' . $relation_name . '\'" />';
            } elseif ($field['name'] == 'password') {
                $content .= '<x-forms.span :value="\'********\'" :field="\'' . $field['name'] . '\'" />';
            } else {
                $content .= '<x-forms.span :value="$' . $model_name . '->' . $field['name'] . '" :field="\'' . $field['name'] . '\'" />';
            }
        }
        return new HtmlString($content);
    }
    protected function getFilterContent($datatable_filters)
    {
        $content = '';
        foreach ($datatable_filters as $filter) 
            $content .= '<x-forms.select :field="\'' . $filter['name'] . '\'" :optionValues=\'' .$filter['option_values']. '\' :optionNames=\'' . $filter['option_names'] . '\'/>';
        return new HtmlString($content);
    }
    protected function getFilterScriptContent($datatable_filters)
    {
        $table_name = $this->argument('tableName');
        $datatable_url = "'{!! route('dashboard.".$table_name.".index') !!}?";
        foreach($datatable_filters as $key => $filter) 
        {
            if ($key == 0) {
                $datatable_url .= $filter['name'].'=\'+'.$filter['name'];
            } else {
                $datatable_url .= '+\'&'.$filter['name'].'=\'+'.$filter['name'];
            }
        }

        $content = "
        <script type=\"module\">
            $(document).ready(function() {";
            foreach($datatable_filters as $filter) 
                $content .= "var ".$filter['name']." = $('#".$filter['name']."').val();";
           
            foreach($datatable_filters as $filter)
                $content .=
                    "
                    $(\"#".$filter['name']."\").on('change', function(){
                    ".$filter['name']." = $(this).val();
                    console.log(".$filter['name'].")
                    if (".$filter['name'].") {
                      var dataTableName = $('#".$table_name."-table').DataTable();
                       dataTableName.ajax.url(".$datatable_url.").load();
                       console.log(".$filter['name'].")
                    }
                });
                    ";
        $content .= "
            });
        </script>
        ";

        return $content;
    }
    /**
     * @return mixed[]
     */
    protected function getModelRelationshipMethods(\ReflectionClass $reflector, string $modelClass, $className, &$datatable_filters): array
    {
        $methods = $reflector->getMethods(\ReflectionMethod::IS_PUBLIC);
        $relations = [];
        foreach ($methods as $method) {
            if ($method->class == $modelClass) {
                $returnType = $this->getReturnType($method);
                if ($returnType == 'BelongsTo') {
                    /////////-------------datatable filters------------////////////
                    $filter_values = "$".Str::plural($method->getName())."_values";
                    $filter_names = "$".Str::plural($method->getName())."_names";
                    
                    $datatable_filters[] = [
                        'name' => $method->getName() . '_id',
                        'option_values' => $filter_values,
                        'option_names' => $filter_names
                    ];

                    $relatedModel = $this->getRelatedModelName($method);
                    if ($relatedModel != "Translation") {
                        $relatedModelClass = "App\\Models\\" . $relatedModel;
                        if (class_exists($relatedModelClass)) {
                            $relatedModelReflectionClass = new \ReflectionClass($relatedModelClass);
                            $relatedTableName = (new $relatedModelClass())->getTable();
                            $relatedClassProperties = $relatedModelReflectionClass->getProperties();
                            foreach ($relatedClassProperties as $property) {
                                if (in_array($property->name, ['translatedAttributes', 'fillable'])) {
                                    $relatedModelObject = new $relatedModelClass();
                                    $relationColumnValue = $relatedModelReflectionClass->getConstant('RELATION_COLUMN');
                                    $attributes = $property->getValue($relatedModelObject);
                                    foreach ($attributes as $attribute) {
                                        //--------print_r($method->getName());
                                        //--------print_r($relationColumnValue);
                                        //--------print_r($attribute);
                                        if ($attribute == $relationColumnValue) {
                                            if ($property->name === 'translatedAttributes') {
                                                $columns[] = "Column::make('{$method->getName()}.translation.$attribute')
                                                    ->render('$.fn.dataTable.render.ellipsis(50)')
                                                    ->title('{$relatedModel}.$attribute')";
                                                if ($method->getName() != Str::lower($className) && !in_array($method->getName(), $relations)) {
                                                    $relations[] = $method->getName();
                                                }
                                            } elseif (in_array(Schema::getColumnType($relatedTableName, $attribute), ['varchar', 'text'])) {
                                                $columns[] = "Column::make('{$method->getName()}.$attribute')
                                                ->render('$.fn.dataTable.render.ellipsis(50)')
                                                ->title('{$relatedModel}.$attribute')";
                                                if (!in_array($method->getName(), $relations)) {
                                                    $relations[] = $method->getName();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } elseif($returnType == 'MorphTo'){
                    //TODO: handle multi morph to relation in the same model
                    //--------print_r($returnType);
                    $relatedModel = $this->getRelatedModelName($method);
                    //--------print_r($relatedModel);
                    $relations[] = $relatedModel.'_type';
                    $relations[] = $relatedModel.'_id';
                }
            }
        }
        return $relations;
    }
    private function getReturnType($method)
    {
        $returnType = $method->getReturnType();
        $parts = explode('\\', $returnType);
        return end($parts);
    }
    private function getRelatedModelName($method)
    {
        return Str::studly(Str::singular($method->getName()));
    }
    protected function detectHasMedia($tableComment)
    {
        return json_decode($tableComment, true)['hasMedia'] ?? false;
    }
    protected function getTableComment($tableName)
    {
        return collect(Schema::getTables())->firstWhere('name', $tableName)['comment'] ?? "";
    }
}