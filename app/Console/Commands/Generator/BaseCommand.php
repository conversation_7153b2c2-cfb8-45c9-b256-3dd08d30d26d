<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;

abstract class BaseCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:base-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Base Command Generation';
    protected $allTables;
    protected $exceptedTables;
    /**
     * Execute the console command.
     */
    function __construct()
    {
        parent::__construct();
        $this->exceptedTables = config('generator.EXCEPTED_TABLES');

        // Get all tables and filter out excepted tables
        $this->allTables = collect(Schema::getTables())
            ->whereNotIn('name', $this->exceptedTables)
            ->values()
            ->toArray();
    }
    public function handle() {}
    protected function getTranslationTableName($tableName)
    {
        return str($tableName)->singular() . '_translations';
    }
    protected function detectTranslationTable($tableName)
    {
        return Schema::hasTable($this->getTranslationTableName($tableName));
    }
    protected function translationColumnsToIgnore($tableName)
    {
        $foreignKeyName = str($tableName)->singular() . '_id';
        return ['id', 'created_at', 'updated_at',  'deleted_at', 'remember_token', 'locale', $foreignKeyName,];
    }
    protected function fillableColumnsToIgnore()
    {
        return ['id', 'created_at', 'updated_at', 'deleted_at', 'remember_token', 'locale'];
    }

    protected function getTranslatedAttributes($tableName)
    {
        $translationTable = $this->getTranslationTableName($tableName);
        $translationColumns = Schema::getColumns($translationTable);
        return collect($translationColumns)->reject(function ($column) use ($tableName) {
            return in_array($column['name'], $this->translationColumnsToIgnore($tableName));
        });
    }
    protected function detectSoftDeleteColumn($columns)
    {
        return collect($columns)->contains(function ($column) {
            return isset($column['comment']) && (json_decode($column['comment'], true)['isSoftDeleteColumn'] ?? false);
        });
    }
    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $value = $value === [] ? '' : "'" . implode("', '", $value) . "'";
            }
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
        }
        return $stubContent;
    }
    protected function getTablesToIgnore()
    {
        return $this->exceptedTables
        ;
    }
    protected function detectHasMedia($tableComment)
    {
        return json_decode($tableComment, true)['hasMedia'] ?? false;
    }
    protected function getTableComment($tableName)
    {
        return collect($this->allTables)->firstWhere('name', $tableName)['comment'] ?? "";
    }
    protected function getAllColumnComments($tableName)
    {
        $comments = [];
        // Use raw SQL to get column comments
        $columnComments = DB::select("SHOW FULL COLUMNS FROM `{$tableName}`");

        foreach ($columnComments as $column) {
            $comments[$column->Field] = $column->Comment; // Map column name to its comment
        }

        return $comments; // Return an associative array of column comments
    }
    protected function getTextDataTypes()
    {
        return ['varchar', 'text'];
    }
}
