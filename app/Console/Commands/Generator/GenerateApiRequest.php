<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateApiRequest extends BaseCommand
{
    protected $signature = 'generate:api-request {tableName} {className}';

    protected $description = 'Generate Store and Update Form Request classes';

    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $foreignKeys = Schema::getForeignKeys($tableName);

        // Generate rules for store and update
        $storeRules = $this->generateStoreRules($tableName);
        $updateRules = $this->generateUpdateRules($tableName);
        $langRules = $this->generateTranslationValidationRules($tableName);

        // Add foreign key rules to both store and update
        $this->addForeignKeyRules($storeRules, $foreignKeys);
        $this->addForeignKeyRules($updateRules, $foreignKeys);

        // Generate the request stubs
        $this->generateRequestStub($className, $storeRules, 'Store', $langRules);
        $this->generateRequestStub($className, $updateRules, 'Update', $langRules);
        
        $this->info("FormRequest classes for 'Store{$className}Request' and 'Update{$className}Request' generated successfully!");
    }

    /**
     * @return array{'required', mixed}[]
     */
    private function generateStoreRules($tableName): array
    {
        $rules = [];
        $columns = Schema::getColumns($tableName);
        foreach ($columns as $column) {
            $columnName = $column['name'];
            $columnType = $column['type_name'];

            if (in_array($columnName, $this->fillableColumnsToIgnore())) {
                continue;
            }

            $rule = $this->convertColumnTypeToRule($columnType);
            // Only add 'required' rule without 'nullable'
            $rules[$columnName] = ['required', $rule];
        }
        return $rules;
    }

    /**
     * @return array{'nullable', mixed}[]
     */
    private function generateUpdateRules($tableName): array
    {
        $rules = [];
        $columns = Schema::getColumns($tableName);
        foreach ($columns as $column) {
            $columnName = $column['name'];
            $columnType = $column['type_name'];

            if (in_array($columnName, $this->fillableColumnsToIgnore())) {
                continue;
            }

            $rule = $this->convertColumnTypeToRule($columnType);

            // Allow fields to be nullable for updates
            $rules[$columnName] = ['nullable', $rule];
        }
        return $rules;
    }
    private function generateTranslationValidationRules($tableName)
    {
        $hasTranslation = $this->detectTranslationTable($tableName);
        if ($hasTranslation) {
            $columns = Schema::getColumns($this->getTranslationTableName($tableName));
            $rules = [];
            $str = 'foreach (config("app._availableLocale") as $locale) {' . "\n            ";
            foreach ($columns as $column) {
                $columnName = $column['name'];
                $columnType = $column['type_name'];
                if (in_array($columnName, $this->translationColumnsToIgnore($tableName))) {
                    continue;
                }
                $rule = $this->convertColumnTypeToRule($columnType);
                $rules['$locale.' . $columnName] = [];
                $rules['$locale.' . $columnName][] = $column['nullable'] ? 'nullable' : 'required';
                $rules['$locale.' . $columnName][] = $rule;
                $str .= '$langRules["$locale.' . $columnName . '"] = ' . json_encode($rules['$locale.' . $columnName]) . ";\n            ";
            }
            $str .= '}';
            return $str;
        }
        return null;
    }
   

    private function generateRequestStub($className, $rules, $type,$langRules)
    {
        $stubContent = $this->getStubContent('custom-api-request.stub', ['className' => $className, 'rules' => $rules, 'type' => $type, 'langRules' => $langRules]);
        $folderPath = app_path("Http/Requests/Api/$className");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$type}{$className}Request.php";
        file_put_contents($fileName, $stubContent);
    }

    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            if ($key === 'rules') {
                $value = $this->formatRules($value);
            }
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
        }
        return $stubContent;
    }

    private function formatRules($rules)
    {
        $formattedRules = [];
        foreach ($rules as $field => $fieldRules) {
            $formattedRules[] = "'$field' => " . json_encode($fieldRules);
        }
        return implode(",\n            ", $formattedRules);
    }

    private function convertColumnTypeToRule($columnType)
    {
        // Map MySQL column types to Laravel validation rules
        $typeMap = [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'smallint' => 'integer',
            'mediumint' => 'integer',
            'varchar' => 'string',
            'char' => 'string',
            'text' => 'string',
            'longtext' => 'string',
            'mediumtext' => 'string',
            'decimal' => 'numeric',
            'double' => 'numeric',
            'float' => 'numeric',
            'date' => 'date',
            'datetime' => 'date',
            'timestamp' => 'date',
            'time' => 'date',
            'year' => 'date',
            // Add more conversions as needed
        ];

        return $typeMap[$columnType] ?? 'string';
    }
    private function addForeignKeyRules(&$rules, $foreignKeys)
    {
        foreach ($foreignKeys as $foreignKey) {
            $relatedTableName = $foreignKey['foreign_table'];
            $relatedColumnName = $foreignKey['foreign_columns'][0];
            $rules[$foreignKey['columns'][0]][] = "exists:$relatedTableName,$relatedColumnName";
        }
        return $rules;
    }
    protected function fillableColumnsToIgnore()
    {
        // Add any columns to ignore during validation rule generation
        return ['id', 'created_at', 'updated_at','deleted_at']; // Example columns to ignore
    }
}
