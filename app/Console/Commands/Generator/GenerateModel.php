<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateModel extends BaseCommand
{
    protected $signature = 'generate:model {tableName} {className}';

    protected $description = 'Generate a model class';

    public function handle()
    {
        $tableName = $this->argument('tableName');
        $className = $this->argument('className');
        $tableComment = $filteredTables = $this->getTableComment($tableName);
        $columns = Schema::getColumns($tableName);
        $allTables = $this->allTables;
        //TODO: user model always return id instead of name as a relation column
        //TODO: handle hash password before store
        $relationColumn = $this->getRelationColumn($tableComment);
        $fillable = $this->getFillableColumns($columns);
        $relationships = $this->extractRelationships($tableName, $allTables);
        $isPivot = $this->isPivotTable($tableComment);
        if (!$isPivot) {
            $this->addBelongsToManyToPivotModels($tableName, $allTables, $relationships);
        }

        $namespaces = "";
        $traits = "";
        $interfaces = "implements ";

        $hasSoftDeletes = $this->detectSoftDeleteColumn($columns);
        if ($hasSoftDeletes) {
            $namespaces .= "use Illuminate\Database\Eloquent\SoftDeletes;\n";
            $traits .= "use SoftDeletes;\n";
        }

        $hasTranslationTable = $this->detectTranslationTable($tableName);
        if ($hasTranslationTable) {
            $namespaces .= "use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;\n";
            $namespaces .= "use Astrotomic\Translatable\Translatable;\n";
            $interfaces .= $interfaces === "implements " ? "TranslatableContract" : ", TranslatableContract";
            $traits .= "use Translatable;\n";
            $translatedAttributes = $this->getTranslatedAttributes($tableName)->pluck('name')->toArray();
        } else {
            $translatedAttributes = [];
        }

        $hasMedia = $this->detectHasMedia($tableComment);
        if ($hasMedia) {
            $namespaces .= "use Spatie\MediaLibrary\HasMedia;\n";
            $namespaces .= "use App\Traits\HasMediaTrait;\n";
            $interfaces .= $interfaces === "implements " ? "HasMedia" : ", HasMedia";
            $traits .= "use HasMediaTrait;\n";
        }

        // Determine the stub file based on whether it's a pivot model or regular model
        $stubName = $isPivot ? 'custom-model.pivot.stub' : 'custom-model.stub';

        if ($interfaces === "implements ") {
            $interfaces = "";
        }

        $stubContent = $this->getStubContent($stubName, ['className' => $className, 'fillable' => $fillable, 'relationships' => $relationships, 'namespaces' => $namespaces, 'traits' => $traits, 'interfaces' => $interfaces, 'translatedAttributes' => $translatedAttributes, 'relationColumn' => $relationColumn]);
        $folderPath = app_path("Models");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/{$className}.php";
        file_put_contents($fileName, $stubContent);

        $this->info("Model for table '{$tableName}' generated successfully!");
    }
    private function getRelationColumn($comment)
    {
        return json_decode($comment, true)['relationColumn'] ?? 'id';
    }

    private function extractRelationships($currentTable, $allTables)
    {
        $relationships = '';
        $addedMethods = [];
        foreach ($allTables as $table) {
            $foreignKeys = Schema::getForeignKeys($table['name']);
            foreach ($foreignKeys as $foreignKey) {
                if ($table['name'] === $currentTable) {
                    $relatedTable = Str::studly(Str::singular($foreignKey['foreign_table']));
                    $methodName = Str::camel($relatedTable);
                    if (!in_array($methodName, $addedMethods)) {
                        $relationships .= "\n\n    public function " . $methodName . "(): BelongsTo \n";
                        $relationships .= "    {\n";
                        $relationships .= "        return \$this->belongsTo({$relatedTable}::class, '{$foreignKey['columns'][0]}', '{$foreignKey['foreign_columns'][0]}');\n";
                        $relationships .= "    }\n";
                        $addedMethods[] = $methodName;
                    }
                } elseif ($foreignKey['foreign_table'] === $currentTable) {
                    $relatedTable = Str::studly(Str::singular($table['name']));
                    $relatedColumns = Schema::getColumns($table['name']);
                    $foreignColumn = null;
                    foreach ($relatedColumns as $column) {
                        if ($column['name'] === $foreignKey['columns'][0]) {
                            $foreignColumn = $column;
                            break;
                        }
                    }
                    if ($foreignColumn && isset($foreignColumn['comment'])) {
                        $comment = json_decode($foreignColumn['comment'], true);
                        if ($comment && isset($comment['relationshipType'])) {
                            $relationshipType = $comment['relationshipType'];
                            $returnType = str($relationshipType)->studly();
                            $relationMethodName = $relationshipType === 'hasMany' ? Str::plural(Str::camel($relatedTable)) : Str::camel($relatedTable);

                            if (!in_array($relationMethodName, $addedMethods)) {
                                $relationships .= "\n\n    public function " . $relationMethodName . "(): {$returnType} \n";
                                $relationships .= "    {\n";
                                $relationships .= "        return \$this->$relationshipType({$relatedTable}::class, '{$foreignKey['columns'][0]}', '{$foreignKey['foreign_columns'][0]}');\n";
                                $relationships .= "    }\n";
                                $addedMethods[] = $relationMethodName;
                            }
                        }
                    }
                }
            }
            $columns = Schema::getColumns($table['name']);
            foreach ($columns as $column) {
                $polymorphicTypes = [];
                $comment = json_decode($column['comment'], true);
                if ($comment && isset($comment['relationshipType']) && isset($comment['relatedTables']) && $comment['relationshipType'] == "MORPH") {
                    $relatedTables = $comment['relatedTables'];
                    if ($table['name'] === $currentTable) 
                    {
                        $morphRelations = array_keys(json_decode($column['comment'], true)['relatedTables']);
                        foreach($morphRelations as $morphRelation)
                        {
                            $polymorphicTypes[] = 'App\\Models\\'.ucfirst((Str::singular($morphRelation)));
                        }
                        if (!in_array('getPolymorphicTypes', $addedMethods)) {
                            $relationships .= "\n\n    public static function getPolymorphicTypes() \n";
                            $relationships .= "    {\n";
                            $relationships .= "        return [\n";
                            foreach($polymorphicTypes as $polymorphicType)
                            {
                                $relationships .= "'".$polymorphicType."'";
                                $relationships .= ",";
                            }
                            $relationships .= "\n";
                            $relationships .= "        ];\n";
                            $relationships .= "    }\n";
                            $addedMethods[] = 'getPolymorphicTypes';
                        }
                    }
                    foreach ($relatedTables as $morphTable => $morphType) {
                        if ($table['name'] === $currentTable) {
                            $morphName = str($column['name'])->before('_type');
                            if (!in_array($morphName, $addedMethods)) {
                                switch ($morphType) {
                                    case 'morphOne':
                                    case 'morphMany':
                                        $relationships .= "\n\n    public function {$morphName}(): MorphTo \n";
                                        $relationships .= "    {\n";
                                        $relationships .= "        return \$this->morphTo('{$morphName}');\n";
                                        $relationships .= "    }\n";
                                        $addedMethods[] = $morphName;
                                        break;
                                } 
                            }
                        } elseif ($currentTable == $morphTable) {
                            $morphName = str($table['name']);
                            if ($morphType == 'morphOne') {
                                $morphName = str($morphName)->singular();
                            }
                            $relatedModel = str($table['name'])->singular()->studly();
                            $returnType = str($morphType)->studly();
                            if (!in_array($morphName, $addedMethods)) {
                                $relationships .= "\n\n    public function {$morphName}(): {$returnType} \n";
                                $relationships .= "    {\n";
                                $relationships .= "        return \$this->$morphType({$relatedModel}::class, '{$morphName}able');\n";
                                $relationships .= "    }\n";
                                $addedMethods[] = $morphName;
                            }
                        }
                    }
                }
            }
            if ($table['name'] === $currentTable && !in_array('getPolymorphicTypes', $addedMethods)) {
                $relationships .= "\n\n    public static function getPolymorphicTypes() \n";
                $relationships .= "    {\n";
                $relationships .= "        return [\n";
                $relationships .= "\n";
                $relationships .= "        ];\n";
                $relationships .= "    }\n";
                $addedMethods[] = 'getPolymorphicTypes';
            }
        }

        return $relationships;
    }

    private function isPivotTable($tableComment)
    {
        return json_decode($tableComment, true)['isPivot'] ?? false;
    }

    private function getFillableColumns($columns)
    {
        return collect($columns)->reject(function ($column) {
            return in_array($column['name'], $this->fillableColumnsToIgnore());
        })->pluck('name')->toArray();
    }

    private function addBelongsToManyToPivotModels($tableName, $allTables, &$relationships)
    {
        foreach ($allTables as $table) {
            $tableCommentOriginal = $this->getTableComment($table['name']);
            $tableCommentDecoded = json_decode($tableCommentOriginal, true);
            if ($this->isPivotTable($tableCommentOriginal) && isset($tableCommentDecoded['table1Name'], $tableCommentDecoded['table2Name'])) {
                $firstTable = $tableCommentDecoded['table1Name'];
                $secondTable = $tableCommentDecoded['table2Name'];
                if (Str::singular($firstTable) === Str::singular($tableName) || Str::singular($secondTable) === Str::singular($tableName)) {
                    $relatedTable = Str::studly(
                        Str::singular(
                            $firstTable === Str::singular($tableName) ? $secondTable : $firstTable
                        )
                    );
                    $relatedModel = Str::studly(Str::singular($relatedTable));
                    $pivotModelName = Str::studly(Str::singular($table['name']));
                    if ($relatedModel) {
                        $relationships .= "\n\n    public function " . Str::plural(Str::camel($relatedModel)) . "(): BelongsToMany \n";
                        $relationships .= "    {\n";
                        $relationships .= "        return \$this->belongsToMany({$relatedModel}::class, '{$table['name']}')->using({$pivotModelName}::class);\n";
                        $relationships .= "    }\n";
                    }
                }
            }
        }
    }
}