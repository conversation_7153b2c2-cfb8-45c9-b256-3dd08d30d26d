<?php

namespace App\Console\Commands\Generator;

use App\Services\DashboardMenuService;
use Illuminate\Console\Command;

class GenerateDashboardSideMenuContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:dashboard-side-menu {tableName?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Dashboard Side Menu Content';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dashboardMenuService = new DashboardMenuService;
        $tableName = $this->argument('tableName');

        if ($tableName) {
            $this->info("Generating side menu for table: {$tableName}...");
            $dashboardMenuService->generateMenu($tableName);
        } else {
            $this->info("Generating side menu for all tables...");
            $dashboardMenuService->generateMenu();
        }

        $this->info("Dashboard Side Menu generated successfully!");
    }
}
