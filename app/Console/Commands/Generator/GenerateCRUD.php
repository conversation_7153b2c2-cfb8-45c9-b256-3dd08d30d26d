<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateCRUD extends BaseCommand
{
    protected $signature = 'crud:generate';

    protected $description = 'Generate CRUD components based on database schema';

    public function handle(): void
    {
        $before = time();

        $tables = $this->getTablesToProcess();
        $componentsToGenerate = $this->getComponentsToGenerate();

        foreach ($tables as $table) {
            if (!in_array($table['name'], $this->getTablesToIgnore())) {
                $tableName = $table['name'];
                $className = Str::studly(Str::singular($tableName));
                if (in_array('model', $componentsToGenerate)) {
                    $this->call('generate:model', ['tableName' => $tableName, 'className' => $className]);
                }
            }
        }

        foreach ($tables as $table) {
            if (!in_array($table['name'], $this->getTablesToIgnore())) {
                $tableName = $table['name'];
                $className = Str::studly(Str::singular($tableName));
                if (!str($tableName)->endsWith('_translations')) {
                    foreach ($componentsToGenerate as $component) {
                        $options = [];
                        $prompt =$component;
                        if ($component === 'request') {
                            $options = ['type' => 'Store'];
                        }
                        $this->generateComponent($component, $prompt, $tableName, $className, $options);
                    }
                }
            }
        }

        $after = time();
        $diff = $after - $before;
        $this->info("CRUD components generated successfully! in {$diff} seconds");
    }

    /**
     * Get tables to process based on user input.
     *
     * @return array
     */
    private function getTablesToProcess(): array
    {
        if ($this->confirm('Do you want to generate CRUD components for all tables?')) {
            return $this->allTables;
        }

        $tableName = $this->ask('Please enter the table name:');
        $tables = collect($this->allTables)->where('name', $tableName)->all();

        if (empty($tables)) {
            $this->error("Table '$tableName' does not exist. Exiting...");
            exit(1);
        }

        return $tables;
    }

    /**
     * Get components to generate based on user input.
     *
     * @return array
     */
    private function getComponentsToGenerate(): array
    {
        $components = [
            'model',
            'enum',
            'factory',
            'seeder',
            'service',
            'request',
            'api-request',
            'json-resource',
            'collection-resource',
            'api-controller',
            'api-route',
            'dashboard-view',
            'datatable',
            'dashboard-controller',
            'dashboard-route',
            'dashboard-side-menu',
            'all',
        ];

        $selectedComponents = $this->choice(
            'Select the components you want to generate (comma separated for multiple)',
            $components,
            null,
            null,
            true
        );
    
        // Check if 'all' is selected and return all components
        if (in_array('all', $selectedComponents)) {
            return array_diff($components, ['all']); // Return all except 'all'
        }
    
        return $selectedComponents;
    }

    /**
     * Generate a CRUD component based on user confirmation.
     *
     * @param string $commandName The Artisan command name (e.g., 'generate:model').
     * @param string $prompt The confirmation prompt.
     * @param string|null $tableName The table name for specific commands.
     * @param string $className The class name to pass as a parameter.
     * @param array $options Additional options for the command.
     */
    private function generateComponent(string $commandName, string $prompt, ?string $tableName, string $className, array $options = []): void
    {
        $parameters = ['className' => $className];
        if ($commandName !== 'api-controller' && $tableName !== null) {
            $parameters['tableName'] = $tableName;
        }
        
        if ($commandName === 'dashboard-side-menu' && $tableName !== null) {

            $parameters['className'] = $className;
        }
        if ($options !== []) {
            $parameters = array_merge($parameters, $options);
        }

        $this->call('generate:' . $commandName, $parameters);
    }
}
