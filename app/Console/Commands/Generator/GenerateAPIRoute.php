<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateAPIRoute extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:api-route {tableName} {className}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate API Route Resource';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $tableSlug = Str::slug($tableName);

        $routeImport = "use App\\Http\Controllers\\Api\\{$className}Controller;";
        $routeDefinition = "Route::apiResource('$tableSlug', {$className}Controller::class);";

        $apiRoutesPath = base_path('routes/api.php');

        // Read the current content of api.php
        $existingRoutes = File::get($apiRoutesPath);

        // Check if the import statement already exists in api.php
        if (strpos($existingRoutes, $routeImport) === false) {
            // Insert the import statement after the opening PHP tag
            $newContent = preg_replace('/<\?php\s*/', "<?php\n\n$routeImport\n", $existingRoutes);
            // Check if the route definition already exists
            if (strpos($existingRoutes, $routeDefinition) === false) {
                // Append the route definition
                $newContent .= "\n$routeDefinition";
                $this->info("Route added for $className successfully!");
            } else {
                $this->info("Route for $className already exists!");
            }
            // Write the new content to api.php
            File::put($apiRoutesPath, $newContent);
        } elseif (strpos($existingRoutes, $routeDefinition) === false) {
            // Check if the route definition already exists
            // Append the route definition
            File::append($apiRoutesPath, "\n$routeDefinition");
            $this->info("Route added for $className successfully!");
        } else {
            $this->info("Route for $className already exists!");
        }
    }
}
