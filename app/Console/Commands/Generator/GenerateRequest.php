<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateRequest extends BaseCommand
{
    protected $signature = 'generate:request {tableName} {className} {type}';

    protected $description = 'Generate a Form Request class';

    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $type = $this->argument('type');
        $foreignKeys = Schema::getForeignKeys($tableName);
        $rules = [];
        $this->addIdRule($rules, $tableName);
        $this->generateValidationRules($rules, $tableName);
        $this->addForeignKeyRules($rules, $foreignKeys);

        $tableComment = $this->getTableComment($tableName);
        $hasMedia = $this->detectHasMedia($tableComment);
        if ($hasMedia) {
           // $this->addMediaRules($rules, $tableComment);
        }
        $langRules = $this->generateTranslationValidationRules($tableName);
        $stubContent = $this->getStubContent('custom-request.stub', ['className' => $className, 'rules' => $rules, 'type' => $type, 'langRules' => $langRules]);

        $folderPath = app_path("Http/Requests");
        if (!File::isDirectory($folderPath)) {
            File::makeDirectory($folderPath, 0755, true);
        }
        $fileName = "{$folderPath}/Store{$className}Request.php";
        // $fileName = app_path("Http/Requests/Store{$className}Request.php");
        file_put_contents($fileName, $stubContent);

        $this->info("FormRequest class 'Store{$className}Request' generated successfully!");
    }

    private function generateValidationRules(&$rules, $tableName)
    {
        $columns = Schema::getColumns($tableName);
        foreach ($columns as $column) {
            $columnName = $column['name'];
            $columnType = $column['type_name'];
            if (in_array($columnName, $this->fillableColumnsToIgnore())) {
                continue;
            }
            $rule = $this->convertColumnTypeToRule($columnType);
            $rules[$columnName][] = $column['nullable'] ? 'nullable' : 'required_without:id';
            $rules[$columnName][] = $rule;
        }
        return $rules;
    }

    private function generateTranslationValidationRules($tableName)
    {
        $hasTranslation = $this->detectTranslationTable($tableName);
        if ($hasTranslation) {
            $columns = Schema::getColumns($this->getTranslationTableName($tableName));
            $rules = [];
            $str = 'foreach (config("app._availableLocale") as $locale) {' . "\n            ";
            foreach ($columns as $column) {
                $columnName = $column['name'];
                $columnType = $column['type_name'];
                if (in_array($columnName, $this->translationColumnsToIgnore($tableName))) {
                    continue;
                }
                $rule = $this->convertColumnTypeToRule($columnType);
                $rules['$locale.' . $columnName] = [];
                $rules['$locale.' . $columnName][] = $column['nullable'] ? 'nullable' : 'required_without:id';
                $rules['$locale.' . $columnName][] = $rule;
                $str .= '$langRules["$locale.' . $columnName . '"] = ' . json_encode($rules['$locale.' . $columnName]) . ";\n            ";
            }
            $str .= '}';
            return $str;
        }
        return null;
    }

    private function addForeignKeyRules(&$rules, $foreignKeys)
    {
        foreach ($foreignKeys as $foreignKey) {
            $relatedTableName = $foreignKey['foreign_table'];
            $relatedColumnName = $foreignKey['foreign_columns'][0];
            $rules[$foreignKey['columns'][0]][] = "exists:$relatedTableName,$relatedColumnName";
        }
        return $rules;
    }

    private function addIdRule(&$rules, $tableName)
    {
        $rules['id'] = ['nullable', "exists:$tableName,id"];
        return $rules;
    }

    protected function getStubContent($stubName, $data)
    {
        $stubPath = base_path("stubs/{$stubName}");
        $stubContent = File::get($stubPath);
        foreach ($data as $key => $value) {
            if ($key === 'rules') {
                $value = $this->formatRules($value);
            }
            $stubContent = str_replace("{{ $$key }}", $value, $stubContent);
        }
        return $stubContent;
    }

    private function formatRules($rules)
    {
        $formattedRules = [];
        foreach ($rules as $field => $fieldRules) {
            $formattedRules[] = "'$field' => " . json_encode($fieldRules);
        }
        return implode(",\n            ", $formattedRules);
    }
    private function convertColumnTypeToRule($columnType)
    {
        // Map MySQL column types to Laravel validation rules
        $typeMap = [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'smallint' => 'integer',
            'mediumint' => 'integer',
            'varchar' => 'string',
            'char' => 'string',
            'text' => 'string',
            'longtext' => 'string',
            'mediumtext' => 'string',
            'decimal' => 'numeric',
            'double' => 'numeric',
            'float' => 'numeric',
            'date' => 'date',
            'datetime' => 'date',
            'timestamp' => 'date',
            'time' => 'date',
            'year' => 'date',
            // Add more conversions as needed
        ];

        return $typeMap[$columnType] ?? 'string';
    }


    private function addMediaRules(&$rules, $tableComment)
    {
        $comment = json_decode($tableComment, true);
        if (isset($comment['mediaCollections'])) {
            foreach ($comment['mediaCollections'] as $collection => $type) {
                $isPlural = Str::plural(Str::singular($collection)) === $collection;
                switch ($type) {
                    case 'IMAGE':
                        $ruleSet = ['required', 'file', 'image', 'max:1024']; // Example rules for image
                        break;
                    case 'VIDEO':
                        $ruleSet = ['required', 'file', 'mimetypes:video/mp4,video/avi', 'max:51200']; // Example rules for video
                        break;
                    case 'FILE':
                        $ruleSet = ['required', 'file', 'max:10240']; // Example rules for file
                        break;
                    case 'AUDIO':
                        $ruleSet = ['required', 'file', 'mimes:application/octet-stream,audio/mpeg,mpga,mp3,wav', 'max:20480']; // Example rules for audio
                        break;
                    case 'DOCUMENT':
                        $ruleSet = ['required', 'file', 'mimetypes:application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'max:10240']; // Example rules for documents
                        break;
                    case 'ARCHIVE':
                        $ruleSet = ['required', 'file', 'mimetypes:application/zip,application/x-rar-compressed', 'max:10240']; // Example rules for archives
                        break;
                    default:
                        $ruleSet = ['required', 'file', 'max:10240']; // Default rules for other file types
                        break;
                }

                if ($isPlural) {
                    $rules[$collection] = ['required', 'array'];
                    $rules[$collection . '.*'] = $ruleSet;
                } else {
                    $rules[$collection] = $ruleSet;
                }
            }
        }

        return $rules;
    }
}
