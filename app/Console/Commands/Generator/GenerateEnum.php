<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class GenerateEnum extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:enum {tableName} {className}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Enum';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $columns = Schema::getColumns($tableName);
        foreach ($columns as $column) {
            if (isset($column['comment'])) {
                $columnComment = json_decode($column['comment'], true);
                if (isset($columnComment['isEnumColumn'], $columnComment['enumCases'], $columnComment['enumReturnType']) && $columnComment['isEnumColumn'] == true && is_array($columnComment['enumCases'])) {
                    $enumName = $className . str($column['name'])->ucfirst() . "Enum";
                    $returnType =  $columnComment['enumReturnType'];
                    $cases = "";
                    foreach ($columnComment['enumCases'] as $name => $value) {
                        $cases .= "case $name = $value;\n    ";
                    }
                    $stubContent = $this->getStubContent('custom-enum.stub', ['enumName' => $enumName, 'returnType' => $returnType, 'cases' => $cases]);
                    $folderPath = app_path("Enums");
                    if (!File::isDirectory($folderPath)) {
                        File::makeDirectory($folderPath, 0755, true);
                    }
                    $fileName = "{$folderPath}/{$enumName}.php";
                    file_put_contents($fileName, $stubContent);
                }
            }
        }

        $this->info("Enum for table '{$tableName}' generated successfully!");
    }
}
