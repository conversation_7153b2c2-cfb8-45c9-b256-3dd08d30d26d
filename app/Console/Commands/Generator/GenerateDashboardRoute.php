<?php

namespace App\Console\Commands\Generator;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateDashboardRoute extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:dashboard-route {tableName} {className}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Dashboard Route Resource';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $className = $this->argument('className');
        $tableName = $this->argument('tableName');
        $tableSlug = Str::slug($tableName);

        $routeImport = "use App\\Http\Controllers\\Dashboard\\{$className}Controller;";
        $routeDefinition = "Route::resource('$tableSlug', {$className}Controller::class);";

        $dashboardRoutesPath = base_path('routes/dashboard.php');

        // Read the current content of dashboard.php
        $existingRoutes = File::get($dashboardRoutesPath);

        // Check if the import statement already exists in dashboard.php
        if (strpos($existingRoutes, $routeImport) === false) {
            // Insert the import statement after the opening PHP tag
            $newContent = preg_replace('/<\?php\s*/', "<?php\n\n$routeImport\n", $existingRoutes);
            // Check if the route definition already exists
            if (strpos($existingRoutes, $routeDefinition) === false) {
                // Append the route definition
                $newContent .= "\n$routeDefinition";
                $this->info("Route added for $className successfully!");
            } else {
                $this->info("Route for $className already exists!");
            }
            // Write the new content to dashboard.php
            File::put($dashboardRoutesPath, $newContent);
        } elseif (strpos($existingRoutes, $routeDefinition) === false) {
            // Check if the route definition already exists
            // Append the route definition
            File::append($dashboardRoutesPath, "\n$routeDefinition");
            $this->info("Route added for $className successfully!");
        } else {
            $this->info("Route for $className already exists!");
        }
    }
}
